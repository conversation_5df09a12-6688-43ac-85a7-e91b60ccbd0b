/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: [
    "./app/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: "#0050ff",
        "primary-text": "#ffffff",
        secondary: "#f1f5f9",
        "secondary-text": "#0050ff",
        "secondary-foreground": "#0f172a",
        background: "#ffffff",
        foreground: "#1E1E1E",
        destructive: "#FE4F5A",
        "destructive-text": "#f7f9fb",
        accent: "#f1f5f9",
        "accent-text": "#0f172a",
        input: "#0050ff",
        ring: "#94a3b7",
        border: "#D9D9D9",
        "placeholder-color": "#0d0d0d",
        "input-text-color": "#1E1E1E",
        muted: {
          DEFAULT: "#f1f5f9",
          text: "#64748b",
        },
        popover: {
          DEFAULT: "#ffffff",
          text: "#0f172a",
        },
        card: {
          DEFAULT: "#ffffff",
          text: "#0f172a",
        },
      },
      ringColor: {
        ring: "hsl(var(--border-outline-color)",
      },
      borderRadius: {},
      keyframes: {
        "caret-blink": {
          "0%,70%,100%": {
            opacity: "1",
          },
          "20%,50%": {
            opacity: "0",
          },
        },
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "caret-blink": "caret-blink 1.25s ease-out infinite",
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      fontFamily: {
        "dm-sans": ["DM Sans", "sans-serif"],
        "cormorant-garamond": ["Cormorant Garamond", "sans-serif"],
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    function ({ addUtilities }) {
      addUtilities({
        ".hide-browser-eye-icon": {
          "&::-ms-reveal, &::-ms-clear": {
            display: "none !important",
          },
          "&::-webkit-credential-auto-fill-button": {
            visibility: "hidden !important",
          },
        },
      });
    },
  ],
};
