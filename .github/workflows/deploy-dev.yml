name: Deploy Prod

on:
  push:
    branches:
      - development

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install dependencies
        env:
          CI: false
        run: |
          yarn install
          yarn build:dev

      - name: Upload build to EC2 server
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.EC2_HOST_DEV }}
          username: ${{ secrets.EC2_USER_DEV}}
          key: ${{ secrets.EC2_SSH_KEY_DEV }}
          port: 22
          script: |
            # Clean old build files
            sudo rm -rf /var/www/html/*

            # Make a temp directory
            mkdir -p ~/temp_build

      - name: Copy build folder contents to EC2
        uses: appleboy/scp-action@master
        with:
          host: ${{ secrets.EC2_HOST_DEV }}
          username: ${{ secrets.EC2_USER_DEV }}
          key: ${{ secrets.EC2_SSH_KEY_DEV }}
          port: 22
          source: "build"
          target: "~/temp_build/"
          strip_components: 1

      - name: Move build to nginx folder
        uses: appleboy/ssh-action@v1.2.2
        with:
          host: ${{ secrets.EC2_HOST_DEV }}
          username: ${{ secrets.EC2_USER_DEV }}
          key: ${{ secrets.EC2_SSH_KEY_DEV }}
          port: 22
          script: |
            # Move files from temp_build to Nginx root
            sudo cp -r ~/temp_build/* /var/www/html/

            # Clean up temp folder
            rm -rf ~/temp_build

            # Reload nginx (optional, usually not needed just for static files)
            sudo systemctl reload nginx
