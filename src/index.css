@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: "DM Sans", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, "Courier New",
    monospace;
}

@layer base {
  /* body {
    @apply font-sans antialiased bg-[hsl(var(--background))] text-[hsl(var(--foreground))];
  } */
  :root {
    /* Buttons */

    /* Active */
    --botton-bg-clr: 221, 100%, 50%;
    --active-btn-hover: 221, 100%, 40%;
    --btn-active-text-color: 0, 0%, 100%;

    /* Secondary */
    --btn-sec-text-color: 221, 100%, 50%;
    --btn-border-clr: 221, 100%, 50%;

    /* Input */
    --border-outline-color: 0, 0%, 85%;
    --placeholder-color: 0, 0%, 5%;

    /* Others */
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
    --muted: 0, 0%, 5%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 221, 100%, 50%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;
    --primary: 221, 100%, 50%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
    --ring: 215 20.2% 65.1%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;
    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;
    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;
    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 1.2%;
    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --ring: 216 34% 17%;
  }
}
@font-face {
  font-display: swap;
  font-family: "DM Sans";
  font-style: normal;
  font-weight: 100;
  src: url("../fonts/dm-sans-v15-latin-100.woff2") format("woff2");
}
/* dm-sans-200 - latin */
@font-face {
  font-display: swap;
  font-family: "DM Sans";
  font-style: normal;
  font-weight: 200;
  src: url("../fonts/dm-sans-v15-latin-200.woff2") format("woff2");
}
/* dm-sans-300 - latin */
@font-face {
  font-display: swap;
  font-family: "DM Sans";
  font-style: normal;
  font-weight: 300;
  src: url("../fonts/dm-sans-v15-latin-300.woff2") format("woff2");
}
/* dm-sans-regular - latin */
@font-face {
  font-display: swap;
  font-family: "DM Sans";
  font-style: normal;
  font-weight: 400;
  src: url("../fonts/dm-sans-v15-latin-regular.woff2") format("woff2");
}
/* dm-sans-500 - latin */
@font-face {
  font-display: swap;
  font-family: "DM Sans";
  font-style: normal;
  font-weight: 500;
  src: url("../fonts/dm-sans-v15-latin-500.woff2") format("woff2");
}
/* dm-sans-600 - latin */
@font-face {
  font-display: swap;
  font-family: "DM Sans";
  font-style: normal;
  font-weight: 600;
  src: url("../fonts/dm-sans-v15-latin-600.woff2") format("woff2");
}
/* dm-sans-700 - latin */
@font-face {
  font-display: swap;
  font-family: "DM Sans";
  font-style: normal;
  font-weight: 700;
  src: url("../fonts/dm-sans-v15-latin-700.woff2") format("woff2");
}
/* dm-sans-800 - latin */
@font-face {
  font-display: swap;
  font-family: "DM Sans";
  font-style: normal;
  font-weight: 800;
  src: url("../fonts/dm-sans-v15-latin-800.woff2") format("woff2");
}
/* dm-sans-900 - latin */
@font-face {
  font-display: swap;
  font-family: "DM Sans";
  font-style: normal;
  font-weight: 900;
  src: url("../fonts/dm-sans-v15-latin-900.woff2") format("woff2");
}

/* Cormorant Garamond-900 */
@font-face {
  font-display: swap;
  font-family: "Cormorant Garamond";
  font-style: normal;
  font-weight: 700;
  src: url("../fonts/CormorantGaramond-Bold.woff2") format("woff2");
}
