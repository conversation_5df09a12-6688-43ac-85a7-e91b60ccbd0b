/**
 * Date and Time Formatting Helper Functions
 * Centralized functions for consistent date/time formatting across the application
 */

/**
 * Format a date string to localized date format
 * @param {string|Date} dateString - The date string or Date object to format
 * @param {string} locale - The locale for formatting (default: 'en-GB' for DD/MM/YYYY)
 * @param {object} options - Additional options for toLocaleDateString
 * @returns {string} Formatted date string or 'N/A' if invalid
 */
export const formatDate = (dateString, locale = "en-GB", options = {}) => {
  if (!dateString) return "N/A";
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "Invalid Date";
    return date.toLocaleDateString(locale, options);
  } catch (error) {
    console.error("Date formatting error:", error);
    return "Invalid Date";
  }
};

/**
 * Format a date string to UK format (DD/MM/YYYY)
 * @param {string|Date} dateString - The date string or Date object to format
 * @returns {string} Formatted date string in UK format
 */
export const formatDateUK = (dateString) => {
  return formatDate(dateString, "en-GB");
};

/**
 * Format a date string to a readable format (e.g., "January 15, 2024")
 * @param {string|Date} dateString - The date string or Date object to format
 * @returns {string} Formatted date string in readable format
 */
export const formatDateReadable = (dateString) => {
  return formatDate(dateString, "en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  });
};

/**
 * Format a date string to short readable format (e.g., "Jan 15, 2024")
 * @param {string|Date} dateString - The date string or Date object to format
 * @returns {string} Formatted date string in short readable format
 */
export const formatDateShort = (dateString) => {
  return formatDate(dateString, "en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

/**
 * Format time from a date string without timezone conversion
 * @param {string|Date} dateString - The date string or Date object to format
 * @param {object} options - Additional options for time formatting
 * @returns {string} Formatted time string in 12-hour format or 'N/A' if invalid
 */
export const formatTime = (dateString, options = {}) => {
  if (!dateString) return "N/A";
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "Invalid Time";

    const defaultOptions = {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
      timeZone: "UTC", // Prevents timezone conversion
      ...options,
    };

    return date.toLocaleTimeString("en-US", defaultOptions);
  } catch (error) {
    console.error("Time formatting error:", error);
    return "Invalid Time";
  }
};
