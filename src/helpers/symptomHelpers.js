/**
 * Formats a camelCase symptom name to a readable format
 * @param {string} symptom - The camelCase symptom name
 * @returns {string} - The formatted symptom name
 * 
 * @example
 * formatSymptomName("highFever") => "High Fever"
 * formatSymptomName("excessiveThirst") => "Excessive Thirst"
 * formatSymptomName("soreThroat") => "Sore Throat"
 */
export const formatSymptomName = (symptom) => {
  if (!symptom || typeof symptom !== 'string') {
    return symptom || '';
  }
  
  return symptom
    .replace(/([A-Z])/g, " $1")
    .replace(/^./, (str) => str.toUpperCase())
    .trim();
};

/**
 * Formats an array of camelCase symptom names to readable format
 * @param {string[]} symptoms - Array of camelCase symptom names
 * @returns {string} - Comma-separated formatted symptom names
 * 
 * @example
 * formatSymptomArray(["highFever", "soreThroat"]) => "High Fever, Sore Throat"
 */
export const formatSymptomArray = (symptoms) => {
  if (!symptoms || !Array.isArray(symptoms) || symptoms.length === 0) {
    return "N/A";
  }
  
  return symptoms
    .map(symptom => formatSymptomName(symptom))
    .join(", ");
};
