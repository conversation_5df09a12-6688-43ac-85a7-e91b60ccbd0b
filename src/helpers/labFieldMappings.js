// Lab field ID to label mappings
// This maps backend field IDs to their human-readable labels as displayed in the Labs component

export const labFieldMappings = {
  // Chemistry fields
  glucoseFasting: "Glucose- Fasting",
  alkPhosAlp: "Alk Phos-ALP",
  crp: "CRP",
  glucoseRandom: "Glucose- Random",
  alt: "ALT",
  ammonia: "Ammonia",
  electrolytesCO2: "Electrolytes CO2",
  ggt: "GGT",
  calciumIonized: "Calcium-Ionized",
  creatinineEgfr: "Creatinine-eGFR",
  ld: "LD",
  osmolality: "Osmolality",
  calciumTotal: "Calcium- Total",
  lipase: "Lipase",
  lactateGreenOnIce: "Lactate (Green on Ice)",
  totalProtein: "Total Protein",
  ck: "CK",
  uricAcidUrate: "Uric Acid- Urate",
  albumin: "Albumin",
  serumPregnancy: "Serum Pregnancy (+-)",
  magnesium: "Magnesium",
  totalBilirubin: "Total Bilirubin",
  bilirubinDirect: "Bilirubin- Direct",
  phosphate: "Phosphate",

  // Hematology fields
  cbcAutoDitt: "CBC & Auto Ditt",
  monoScreen: "Mono Screen",
  a1c: "A1C",

  // Coagulation fields
  ptt: "PTT",
  dDimer: "D-Dimer",
  fibrinogen: "Fibrinogen'",

  // Immunology fields
  tissueTransglutamine: "PTT",
  protein: "D-Dimer",
  vasculitisMpoPr3: "Vasculitis MPO & PR3'",
  rheumatoidFactor: "Rheumatoid Factor''",
  ccpCitroineAb: "CCP (Citroine Ab)', value:'",
  microglobulin: "Microglobulin'",
  complementC3C4: "Complement C3 & C4'",
  serumFreeLightChains: "Serum Free Light Chains'",
  iggIgaIgm: "IgG IgA & IgM'",
  antiGbm: "Anti-GBM'",
  asot: "ASOT'",
  u1Antitrypsin: "U-1-Antitrypsin",
  farmersLung: "Farmers Lung''",
  ama: "AMA'",
  dsDNA: "ds DNA'",
  haptoglobin: "Haptoglobin'",
  cardiolipin: "Cardiolipin'",

  // Cardiac Function and Lipids fields
  hsCrpCardiac: "HS-CRP-Cardiac",
  passingNonPassing: "Passing Non-Passing",
  bnpPurpleTube: "BNP (Purple Tube)",
  ldlHdlTriglycerides: "Lipid Profile: Cholesterol, LDL, HDL & Triglycerides",
  troponinGreenTube: "Troponin ( Green Tube)",

  // Tolerance Tests fields
  "75gDiabeticConfirmatory": "75 g Diabetic-Confirmatory",
  "50gGestationalScreen": "50 g Gestational-Screen",
  lactoseToleranceTest: "Lactose Tolerance Test",
  "75gPostPartumScreen": "75 g Post-partum-Screen (Gestational Diabetes Patients)",
  "75gGestationalConfirmatory": "75 g Gestational-Confirmatory",

  // Nutritional Status fields
  ferritin: "Ferritin",
  prealbumin: "Prealbumin",
  ironStudies: "Iron Studies",
  vitaminB12: "Vitamin B12",

  // Endocrine and Tumor Markers fields
  prolactin: "Prolactin",
  dheas: "DHEAS",
  cortisolHS: "Cortisol H/S",
  pthIntactRedTube: "PTH- Intact (red tube)",
  psa40To75Yrs: "PSA (40 to 75 yrs)",
  tshDiagnostic: "TSH- Diagnostic",
  progesterone: "Progesterone",
  estradiol: "Estradiol",
  fsh: "FSH",
  lh: "LH",
  ca153: "CA 15-3",
  tshMonitorTx: "TSH- Monitor Tx",
  ca125: "CA-125",
  cea: "CEA",
  afp: "AFP",
  ca199: "CA 19-9",
  testosteroneTotal: "Testosterone -Total",

  // Serum Toxicology fields
  ethanol: "Ethanol",
  tricyclicsScreen: "Tricyclics- Screen",
  acetaminophen: "Acetaminophen",
  salicylate: "Salicylate",

  // Blood Gas fields
  carboxyhemoglobinCo: "Carboxyhemoglobin-CO",
  methemoglobin: "Methemoglobin",
  specimenArterialCapillary: "Specimen Arterial Capillary Central Mixed Venous Venous (Green Tube no Gel on Ice) Cord Send Cord on Ice)",
  lactate: "Lactate",

  // Therapeutic Drug Monitoring fields
  digoxinLastDose: "Digoxin",
  lithiumLastDose: "Lithium",
  phenobarbitalLastDose: "Phenobarbital",
  phenytoinLastDose: "Phenytoin",
  primidoneLastDose: "Primidone (mysoline)",
  valproicAcidLastDose: "Valproic acid (epival)",
  cyclosporineLastDose: "Cyclosporine (purple tube)",
  vancomycinLastDose: "Vancomycin",
  preDoseLastDose: "Pre-dose",
  postDoseLastDose: "Post-dose",
  gentamicinLastDose: "Gentamicin",
  tobramycinLastDose: "Tobramycin",
  preDoseLevelLastDose: "Pre-dose level",
  extendedIntervalPediatricsLastDose: "Extended interval pediatrics",
  postDoseLevelLastDose: "Post-dose level",
  extendedIntervalLastDose: "Extended interval",
  hr22PostLevelNeonatesLastDose: "22hr post level - neonates",
};

// Helper function to get readable label for a field ID
export const getFieldLabel = (fieldId) => {
  return labFieldMappings[fieldId] || fieldId;
};

// Helper function to map an array of field IDs to their labels
export const mapFieldsToLabels = (fieldIds) => {
  if (!Array.isArray(fieldIds)) return [];
  return fieldIds.map(fieldId => getFieldLabel(fieldId));
};
