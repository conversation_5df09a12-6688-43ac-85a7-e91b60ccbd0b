import React from "react";
import { NavLink } from "react-router-dom";
import { ReactComponent as <PERSON>Mobil<PERSON>ogo } from "../../assets/svgs/DocMobilLogo.svg";
import { ReactComponent as DashboardIcon } from "../../assets/svgs/DashboardIcon.svg";
import { ReactComponent as PatientIcon } from "../../assets/svgs/PatientIcon.svg";
import { ReactComponent as AppointmentsIcon } from "../../assets/svgs/AppointmentsIcon.svg";
import { ReactComponent as PaymentHistoryIcon } from "../../assets/svgs/PaymentHistoryIcon.svg";
import { ReactComponent as SharedPatientEMRIcon } from "../../assets/svgs/SharedPatientEMRIcon.svg";
import { ReactComponent as SetAvailabilityIcon } from "../../assets/svgs/clock.svg";

const DoctorSidebar = () => {
  return (
    <div className="fixed left-0 top-0 h-screen w-64 min-w-64 max-w-64 bg-white border border-solid border-r[1px] border-[#DFE0E2] flex flex-col z-40">
      <div className="p-6 text-center">
        <DocMobilLogo className="mx-auto w-12 h-12" />
      </div>

      <nav className="flex-1">
        <ul className="space-y-2 px-4">
          <li>
            <NavLink
              to="/doctor/dashboard"
              className={({ isActive }) =>
                `flex items-center space-x-3 py-2 px-4 rounded-lg ${
                  isActive
                    ? "bg-blue-50 text-blue-600"
                    : "hover:bg-gray-100 text-[#666970]"
                }`
              }
            >
              <DashboardIcon fill="currentColor" />
              <span className="text-sm font-medium">Dashboard</span>
            </NavLink>
          </li>

          <li>
            <NavLink
              to="/doctor/appointments"
              className={({ isActive }) =>
                `flex items-center space-x-3 py-2 px-4 rounded-lg ${
                  isActive
                    ? "bg-blue-50 text-blue-600"
                    : "hover:bg-gray-100 text-[#666970]"
                }`
              }
            >
              <AppointmentsIcon fill="currentColor" />
              <span className="text-sm font-medium">Appointments</span>
            </NavLink>
          </li>

          <li>
            <NavLink
              to="/doctor/patients"
              className={({ isActive }) =>
                `flex items-center space-x-3 py-2 px-4 rounded-lg ${
                  isActive
                    ? "bg-blue-50 text-blue-600"
                    : "hover:bg-gray-100 text-[#666970]"
                }`
              }
            >
              <PatientIcon fill="currentColor" />
              <span className="text-sm font-medium">Patients</span>
            </NavLink>
          </li>

          <li>
            <NavLink
              to="/doctor/payments"
              className={({ isActive }) =>
                `flex items-center space-x-3 py-2 px-4 rounded-lg ${
                  isActive
                    ? "bg-blue-50 text-blue-600"
                    : "hover:bg-gray-100 text-[#666970]"
                }`
              }
            >
              <PaymentHistoryIcon fill="currentColor" />
              <span className="text-sm font-medium">Payment History</span>
            </NavLink>
          </li>

          <li>
            <NavLink
              to="/doctor/emr-sharing"
              className={({ isActive }) =>
                `flex items-center space-x-3 py-2 px-4 rounded-lg ${
                  isActive
                    ? "bg-blue-50 text-blue-600"
                    : "hover:bg-gray-100 text-[#666970]"
                }`
              }
            >
              <SharedPatientEMRIcon fill="currentColor" />
              <span className="text-sm font-medium">Shared Patient EMR</span>
            </NavLink>
          </li>

          {/* <li>
            <NavLink
              to="/doctor/profile"
              className={({ isActive }) =>
                `flex items-center space-x-3 py-2 px-4 rounded-lg ${
                  isActive
                    ? "bg-blue-50 text-blue-600"
                    : "hover:bg-gray-100 text-[#666970]"
                }`
              }
            >
              <ProfileIcon fill="currentColor" />
              <span className="text-sm font-medium">Profile</span>
            </NavLink>
          </li> */}

          <li>
            <NavLink
              to="/doctor/set-availability"
              className={({ isActive }) =>
                `flex items-center space-x-3 py-2 px-4 rounded-lg ${
                  isActive
                    ? "bg-blue-50 text-blue-600"
                    : "hover:bg-gray-100 text-[#666970]"
                }`
              }
            >
              <SetAvailabilityIcon fill="currentColor" />
              <span className="text-sm font-medium">Set Availability</span>
            </NavLink>
          </li>
        </ul>
      </nav>
    </div>
  );
};

export default DoctorSidebar;
