import { cn } from "../../lib/utils";
import { Button } from "../ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { ReactComponent as CalendarIcon } from "../../assets/svgs/CalendarIcon.svg";
import { format } from "date-fns";
import { Calendar } from "../ui/calendar";

const PatientDatePicker = ({ selectedDate, setSelectedDate }) => {
  return (
    <div className="relative">
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal pl-10",
              !selectedDate && "text-muted-foreground",
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4 absolute left-3" />
            {selectedDate ? (
              format(selectedDate, "dd/MM/yyyy")
            ) : (
              <span>dd/mm/yyyy</span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={setSelectedDate}
            disabled={(date) => date < new Date("1900-01-01")}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default PatientDatePicker;
