import React, { useEffect, useRef } from "react";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { cn } from "../../lib/utils";

const PhoneInputWithCountryCode = ({
  value,
  onChange,
  error,
  defaultCountry = "us",
  ...props
}) => {
  const phoneInputRef = useRef(null);
  const initialValueSet = useRef(false);

  // Handle both formats: with or without "+" prefix
  const formatValue = (val) => {
    if (!val) return "";
    return val.startsWith("+") ? val : `+${val}`;
  };

  // This effect ensures the value is properly set and maintained
  useEffect(() => {
    if (value && !initialValueSet.current && phoneInputRef.current) {
      const timer = setTimeout(() => {
        if (phoneInputRef.current && phoneInputRef.current.setState) {
          phoneInputRef.current.setState({
            value: formatValue(value),
          });
          initialValueSet.current = true;
        }
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [value]);

  // Reset the initialValueSet ref when value changes to empty
  useEffect(() => {
    if (!value) {
      initialValueSet.current = false;
    }
  }, [value]);

  return (
    <PhoneInput
      ref={phoneInputRef}
      country={defaultCountry}
      value={formatValue(value)}
      onChange={(phone, country) => {
        if (onChange && typeof onChange === "function") {
          const rawNumber =
            "+" +
            country.dialCode +
            phone.substring(country.dialCode.length).replace(/[^0-9]/g, "");
          // Keep the same behavior as before - removing the "+" prefix
          onChange(rawNumber.replace(/^\+/, ""));
        }
      }}
      inputClass={cn("custom-phone-input", error ? "phone-input-error" : "")}
      containerClass="custom-phone-container"
      buttonClass="custom-phone-dropdown-button"
      dropdownClass="custom-phone-dropdown"
      searchClass="custom-phone-search"
      enableSearch={true}
      searchPlaceholder="Search country"
      inputProps={{
        name: "contactNumber",
        required: true,
        autoFocus: false,
        placeholder: "Enter phone number",
        ...props.inputProps,
      }}
      specialLabel=""
      countryCodeEditable={false}
      preferredCountries={["us", "gb", "ca", "au", "in"]}
      {...props}
    />
  );
};

export default PhoneInputWithCountryCode;
