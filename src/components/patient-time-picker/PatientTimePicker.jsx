import React from "react";
import { useState } from "react";
import { Button } from "../ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { cn } from "../../lib/utils";
import { Clock } from "lucide-react";

const PateintTimePicker = ({ value, onChange }) => {
  const [open, setOpen] = useState(false);
  const [time, setTime] = useState(value || "");

  const hours = Array.from({ length: 12 }, (_, i) =>
    (i + 1).toString().padStart(2, "0"),
  );
  const minutes = ["00", "15", "30", "45"];
  const periods = ["AM", "PM"];

  const handleTimeSelect = (selectedTime) => {
    setTime(selectedTime);
    onChange(selectedTime);
    setOpen(false);
  };

  return (
    <div className="relative">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start pl-10",
              !time && "text-muted-foreground",
            )}
          >
            <Clock className="mr-2 h-4 w-4 absolute left-3" />
            {time || <span>HH:MM AM/PM</span>}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0">
          <div className="flex p-2">
            <div className="flex-1 h-48 overflow-y-auto">
              {hours.map((hour) => (
                <div
                  key={hour}
                  onClick={() =>
                    setTime(
                      `${hour}:${time.split(":")[1]?.split(" ")[0] || "00"} ${time.split(" ")[1] || "AM"}`,
                    )
                  }
                  className={`px-4 py-2 hover:bg-accent cursor-pointer rounded ${
                    time.split(":")[0] === hour
                      ? "bg-primary text-primary-foreground"
                      : ""
                  }`}
                >
                  {hour}
                </div>
              ))}
            </div>

            <div className="flex-1 h-48 overflow-y-auto">
              {minutes.map((minute) => (
                <div
                  key={minute}
                  onClick={() =>
                    setTime(
                      `${time.split(":")[0] || "12"}:${minute} ${time.split(" ")[1] || "AM"}`,
                    )
                  }
                  className={`px-4 py-2 hover:bg-accent cursor-pointer rounded ${
                    time.split(":")[1]?.split(" ")[0] === minute
                      ? "bg-primary text-primary-foreground"
                      : ""
                  }`}
                >
                  {minute}
                </div>
              ))}
            </div>

            <div className="flex-1 h-48 overflow-y-auto">
              {periods.map((period) => (
                <div
                  key={period}
                  onClick={() =>
                    setTime(
                      `${time.split(":")[0] || "12"}:${time.split(":")[1]?.split(" ")[0] || "00"} ${period}`,
                    )
                  }
                  className={`px-4 py-2 hover:bg-accent cursor-pointer rounded ${
                    time.split(" ")[1] === period
                      ? "bg-primary text-primary-foreground"
                      : ""
                  }`}
                >
                  {period}
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-end p-2 border-t">
            <Button
              variant="outline"
              onClick={() => setOpen(false)}
              className="mr-2"
            >
              Cancel
            </Button>
            <Button
              onClick={() => {
                handleTimeSelect(time);
              }}
            >
              OK
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default PateintTimePicker;
