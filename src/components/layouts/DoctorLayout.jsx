import React from "react";
import { Outlet, useLocation } from "react-router-dom";
import { AnimatePresence, motion } from "framer-motion";
import Navbar from "../navbar/Navbar";
import DoctorS<PERSON><PERSON> from "../sidebar/DoctorSidebar";

const DoctorLayout = () => {
  const location = useLocation();
  const authPaths = [
    "/doctor/login",
    "/doctor/signup",
    "/doctor/verify-otp",
    "/doctor/forgot-password/email",
    "/doctor/forgot-password/verify",
    "/doctor/forgot-password/create-password",
    "/doctor/information",
    "/doctor/profileUnderReview",
  ];
  const shouldShowNavbar = !authPaths.includes(location.pathname);

  return (
    <div className="min-h-screen">
      {shouldShowNavbar && <DoctorSidebar />}
      <div className={`flex flex-col ${shouldShowNavbar ? "ml-64" : ""}`}>
        {shouldShowNavbar && <Navbar />}
        <AnimatePresence mode="wait">
          <motion.main
            key={location.pathname}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
            className="p-4 flex-grow"
          >
            <Outlet />
          </motion.main>
        </AnimatePresence>
      </div>
    </div>
  );
};

export default DoctorLayout;
