import React from "react";
import { Outlet, useLocation } from "react-router-dom";
import Navbar from "../navbar/Navbar";
import Footer from "../patient-footer/PatientFooter";

const PatientLayout = () => {
  const location = useLocation();
  const authPaths = [
    "/patient/login",
    "/patient/signup",
    "/patient/verify-otp",
    "/patient/forgot-password/email",
    "/patient/forgot-password/verify",
    "/patient/forgot-password/create-password",
    "/patient/information",
    "/patient/profileUnderReview",
  ];
  const shouldShowNavbar = !authPaths.includes(location.pathname);

  return (
    <div className="flex flex-col">
      {shouldShowNavbar && <Navbar />}
      <Outlet />
      <Footer />
    </div>
  );
};

export default PatientLayout;
