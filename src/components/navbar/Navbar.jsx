"use client";
import { useState, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { logOutUser } from "../../common/logoutUser";
import { ReactComponent as NotificationImg } from "../../assets/svgs/NotificationImg.svg";
import { ReactComponent as DocMobilLogo } from "../../assets/svgs/DocMobilLogoWithText.svg";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import DocMobileLogo from "../../assets/images/DocMobilLogo.png";
import {
  ChevronDown,
  CrossIcon,
  LogOut,
  MenuIcon,
  User,
  XIcon,
} from "lucide-react";
import { useTranslation } from "react-i18next";
import {
  Select as SelectActive,
  SelectContent as SelectActiveContent,
  SelectGroup as SelectActiveGroup,
  SelectItem as SelectActiveItem,
  SelectTrigger as SelectActiveTrigger,
  SelectValue as SelectActiveValue,
} from "../ui/select-active";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import DefaultAvatar from "../../assets/images/DefaultAvatar.png";
import AvatarDropdown from "../top-navbar-avatar-dropdown/AvatarDropdown";

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { t, i18n } = useTranslation();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const user = useSelector((state) => state.userReducer.user);
  const [currentLanguage, setCurrentLanguage] = useState("en");

  // Set default language to English if not set
  useEffect(() => {
    // If i18n.language is empty or cimode, change to English
    if (!i18n.language || i18n.language === "cimode") {
      i18n.changeLanguage("en");
    }

    // Update our local state with the current language
    setCurrentLanguage(i18n.language || "en");

    // Listen for language changes
    const handleLanguageChanged = (lng) => {
      setCurrentLanguage(lng);
    };

    i18n.on("languageChanged", handleLanguageChanged);

    return () => {
      i18n.off("languageChanged", handleLanguageChanged);
    };
  }, [i18n]);

  // Determine navbar type based on user and route
  const isPublicRoute =
    !user &&
    !location.pathname.includes("/doctor/") &&
    !location.pathname.includes("/patient/");
  const isDoctorRoute =
    user?.userType === "doctor" || location.pathname.includes("/doctor/");
  const isPatientRoute =
    user?.userType === "patient" || location.pathname.includes("/patient/");

  // Check if we should show navbar (some auth routes don't show navbar)
  const authPaths = [
    "/doctor/login",
    "/doctor/signup",
    "/doctor/verify-otp",
    "/doctor/forgot-password/email",
    "/doctor/forgot-password/verify",
    "/doctor/forgot-password/create-password",
    "/doctor/information",
    "/doctor/profileUnderReview",
    "/patient/login",
    "/patient/signup",
    "/patient/verify-otp",
    "/patient/forgot-password/email",
    "/patient/forgot-password/verify",
    "/patient/forgot-password/create-password",
    "/patient/information",
    "/patient/profileUnderReview",
  ];
  const shouldShowNavbar = !authPaths.includes(location.pathname);

  // Get page title for doctor dashboard
  const pageTitleMapping = {
    "/doctor/dashboard": "Dashboard",
    "/doctor/patients": "Patients",
    "/doctor/appointments": "Appointments",
    "/doctor/payments": "Payment History",
    "/doctor/emr-sharing": "Shared Patient EMR",
    "/doctor/profile": "Profile",
    "/doctor/set-availability": "Set Availability",
  };
  const pageTitle = pageTitleMapping[location.pathname] || "Doctor Portal";

  // Handle select changes for public navbar
  const handleSelectChange = (value) => {
    setIsMobileMenuOpen(false);
    if (value === "doctor") {
      navigate("/doctor/login");
    } else if (value === "patient") {
      navigate("/patient/login");
    }
  };

  const handleSignupSelectChange = (value) => {
    setIsMobileMenuOpen(false);
    if (value === "doctor") {
      navigate("/doctor/signup");
    } else if (value === "patient") {
      navigate("/patient/signup");
    }
  };

  const handleLanguageChange = (language) => {
    i18n.changeLanguage(language);
    setCurrentLanguage(language);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const handleProfileAction = (action) => {
    if (action === "profile") {
      if (isDoctorRoute) {
        navigate("/doctor/profile");
      } else if (isPatientRoute) {
        navigate("/patient/profile");
      }
    } else if (action === "logout") {
      logOutUser();
    }
  };

  // Patient navbar links
  const patientNavLinks = [
    { path: "/patient/find-doctor", label: "Find Doctor" },
    { path: "/patient/payments", label: "Payment History" },
    { path: "/patient/prescription", label: "Prescription" },
    { path: "/patient/appointment", label: "Appointment" },
  ];

  const LanguageSelector = ({ className }) => (
    <Select onValueChange={handleLanguageChange} value={currentLanguage}>
      <SelectTrigger className={className || "w-28"}>
        <SelectValue>
          {currentLanguage === "en"
            ? "English"
            : currentLanguage === "es"
              ? "Español"
              : "English"}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectItem value="en">English</SelectItem>
          <SelectItem value="es">Español</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  );

  // If we shouldn't show navbar, return null
  if (!shouldShowNavbar) return null;

  // Public Navbar
  if (isPublicRoute) {
    return (
      <nav className="bg-white shadow-md">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-24">
            <Link to="/" className="flex-shrink-0">
              <DocMobilLogo className="h-8 w-32 sm:h-10 sm:w-40 md:h-12 md:w-48" />
            </Link>

            <div className="hidden lg:flex items-center gap-8">
              <div className="flex gap-8">
                <Link
                  to="/"
                  className="text-gray-600 hover:text-blue-600 transition-colors text-md md:text-md font-bold"
                >
                  {t(`home`)}
                </Link>
                <Link
                  to="/about"
                  className="text-gray-600 hover:text-blue-600 transition-colors text-sm md:text-base font-bold"
                >
                  {t(`about-us`)}
                </Link>
              </div>

              <LanguageSelector />

              <div className="flex gap-4 ml-4">
                <Select onValueChange={handleSelectChange}>
                  <SelectTrigger className="w-24 md:w-28">
                    <SelectValue placeholder="Login" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value="doctor">Doctor</SelectItem>
                      <SelectItem value="patient">Patient</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>

                <SelectActive onValueChange={handleSignupSelectChange}>
                  <SelectActiveTrigger className="w-24 md:w-28">
                    <SelectActiveValue placeholder="Signup" />
                  </SelectActiveTrigger>
                  <SelectActiveContent>
                    <SelectActiveGroup>
                      <SelectActiveItem value="doctor">Doctor</SelectActiveItem>
                      <SelectActiveItem value="patient">
                        Patient
                      </SelectActiveItem>
                    </SelectActiveGroup>
                  </SelectActiveContent>
                </SelectActive>
              </div>
            </div>

            <div className="lg:hidden">
              <button
                onClick={toggleMobileMenu}
                className="p-2 text-gray-600 hover:text-blue-600"
              >
                {isMobileMenuOpen ? (
                  <XIcon className="h-6 w-6" />
                ) : (
                  <MenuIcon className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {isMobileMenuOpen && (
          <div className="lg:hidden absolute w-full bg-white shadow-lg z-50">
            <div className="px-4 py-4 space-y-4">
              <Link
                to="/"
                className="block py-2 px-4 text-gray-600 hover:text-blue-600"
                onClick={toggleMobileMenu}
              >
                Home
              </Link>
              <Link
                to="/about"
                className="block py-2 px-4 text-gray-600 hover:text-blue-600"
                onClick={toggleMobileMenu}
              >
                About Us
              </Link>

              <LanguageSelector className="w-full" />

              <div className="pt-4 space-y-4 border-t border-gray-200">
                <Select onValueChange={handleSelectChange}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Login" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value="doctor">Doctor</SelectItem>
                      <SelectItem value="patient">Patient</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>

                <SelectActive onValueChange={handleSignupSelectChange}>
                  <SelectActiveTrigger className="w-full">
                    <SelectActiveValue placeholder="Signup" />
                  </SelectActiveTrigger>
                  <SelectActiveContent>
                    <SelectActiveGroup>
                      <SelectActiveItem value="doctor">Doctor</SelectActiveItem>
                      <SelectActiveItem value="patient">
                        Patient
                      </SelectActiveItem>
                    </SelectActiveGroup>
                  </SelectActiveContent>
                </SelectActive>
              </div>
            </div>
          </div>
        )}
      </nav>
    );
  }

  // Doctor Navbar
  if (isDoctorRoute) {
    return (
      <nav className="bg-white border border-solid border-b[1px] -ml-[1px] border-[#DFE0E2] min-h-16 flex">
        <div className="flex items-center justify-between w-full min-h-full px-8">
          <div className="flex items-center gap-4">
            {/* <img
              alt="Doc Mobile Logo"
              src={DocMobileLogo}
              className="h-8 cursor-pointer"
              onClick={() => navigate("/doctor/dashboard")}
            /> */}
            <h1 className="text-[#1E1E1E] text-[24px] font-bold">
              {pageTitle}
            </h1>
          </div>

          <div className="flex items-center justify-center flex-row gap-4">
            <NotificationImg className="w-6 h-6" />
            <AvatarDropdown />
          </div>
        </div>
      </nav>
    );
  }

  // Patient Navbar
  if (isPatientRoute) {
    return (
      <nav className="bg-white border-b border-[#DFE0E2] sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <img
                  alt="Doc Mobile Logo"
                  src={DocMobileLogo || DefaultAvatar}
                  className="h-12 cursor-pointer"
                  onClick={() => navigate("/patient/find-doctor")}
                />
              </div>

              <div className="hidden md:block ml-10">
                <div className="flex space-x-4">
                  {patientNavLinks.map((link) => (
                    <Link
                      key={link.path}
                      to={link.path}
                      className="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium"
                    >
                      {link.label}
                    </Link>
                  ))}
                </div>
              </div>
            </div>

            <div className="hidden md:block">
              <div className="flex items-center space-x-4">
                <NotificationImg className="w-6 h-6 text-gray-700 hover:text-primary cursor-pointer" />
                <AvatarDropdown />
              </div>
            </div>

            <div className="md:hidden flex items-center">
              <NotificationImg className="w-6 h-6 text-gray-700 hover:text-primary cursor-pointer mr-4" />

              <button
                onClick={toggleMobileMenu}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-primary focus:outline-none"
              >
                {isMobileMenuOpen ? (
                  <CrossIcon className="block h-6 w-6" />
                ) : (
                  <MenuIcon className="block h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {isMobileMenuOpen && (
          <div className="md:hidden absolute w-full bg-white shadow-lg z-50">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white">
              {patientNavLinks.map((link) => (
                <Link
                  key={link.path}
                  to={link.path}
                  className="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {link.label}
                </Link>
              ))}

              <div className="pt-4 pb-3 border-t border-gray-200">
                <div className="flex items-center px-5">
                  <div className="flex-shrink-0">
                    <div className="flex items-center gap-1">
                      <Avatar>
                        <AvatarImage
                          src={user?.profilePicture || DefaultAvatar}
                        />
                        <AvatarFallback>
                          {user?.firstName?.charAt(0) || "U"}
                        </AvatarFallback>
                      </Avatar>
                      <ChevronDown className="h-4 w-4 text-gray-500" />
                    </div>
                  </div>
                  <div className="ml-3 space-y-2">
                    <button
                      className="block w-full text-left px-3 py-2 text-gray-700 hover:text-primary rounded-md text-base font-medium"
                      onClick={() => {
                        handleProfileAction("profile");
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      <User className="inline-block mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </button>
                    <button
                      className="block w-full text-left px-3 py-2 text-gray-700 hover:text-primary rounded-md text-base font-medium"
                      onClick={() => {
                        handleProfileAction("logout");
                        setIsMobileMenuOpen(false);
                      }}
                    >
                      <LogOut className="inline-block mr-2 h-4 w-4" />
                      <span>Log out</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </nav>
    );
  }
  return null;
};

export default Navbar;
