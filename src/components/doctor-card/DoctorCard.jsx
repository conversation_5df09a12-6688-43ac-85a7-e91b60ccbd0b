import React from "react";
import { memo } from "react";
import { motion } from "framer-motion";
import { ReactComponent as VerifyGolden } from "../../assets/svgs/VerifiedGolden.svg";
import ProfilePicture from "../../assets/images/IDCard.jpeg";
import { ReactComponent as LocationIcon } from "../../assets/svgs/LocationIcon.svg";
import { Button } from "../ui/button";
import { useNavigate } from "react-router-dom";
import { doctorSpecialties } from "../../constants/doctorSpecialties";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "../../components/ui/avatar";
import DefaultAvatar from "../../assets/images/DefaultAvatar.png";
import { getSingleCountryLabel } from "../../helper/country-helper";

const SpecialitiesMap = new Map(
  doctorSpecialties.map(({ name, Icon }) => [name, Icon]),
);

const DoctorCard = ({ doctor, isexpendable }) => {
  const navigate = useNavigate();

  const profileImage = doctor?.profilePicture?.url || ProfilePicture;
  const fullName =
    `${doctor?.firstName || ""} ${doctor?.lastName || ""}`.trim() ||
    "Unknown Doctor";
  const country = doctor?.country || "N/A";
  const specialities = Array.isArray(doctor?.speciality)
    ? doctor.speciality
    : [];

  const credentials =
    doctor?.qualifications?.length > 0
      ? doctor.qualifications.map((qual) => qual.degreeName).join(", ")
      : "No credentials provided";

  const experience = doctor?.yearsOfExperience || 0;

  const renderSpeciality = (spc, index) => {
    const Icon = SpecialitiesMap.get(spc) || (() => null);
    return (
      <motion.div
        key={`${spc}-${index}`}
        className="flex items-center gap-2"
        initial={{ opacity: 0, x: -5 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ delay: index * 0.1 }}
      >
        <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
        <h1 className="font-medium text-xs sm:text-[13px] md:text-[14px] text-[#1E1E1EB2]">
          {spc}
        </h1>
      </motion.div>
    );
  };

  if (isexpendable) {
    return (
      <motion.div
        className="flex border border-[#E7E8E9] rounded-xl w-full max-w-6xl hover:bg-[#0052FD08] flex-col sm:flex-row p-2 sm:p-3"
        whileHover={{
          boxShadow: "0 10px 25px rgba(0, 82, 253, 0.1)",
          borderColor: "#0052FD40",
        }}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="w-full sm:w-2/12 flex items-start justify-center sm:justify-end p-2 sm:p-3 md:p-4">
          <div className="h-16 w-16 sm:h-20 sm:w-20 md:h-24 md:w-24 rounded-2xl overflow-hidden border border-[#0052FD]">
            <div className="w-full h-full object-contain relative">
              <div className="absolute inset-0 bg-[#0052FD] opacity-10 rounded-2xl"></div>
              <img
                src={profileImage || DefaultAvatar}
                alt=""
                className="h-full w-full object-cover rounded-2xl"
              />
            </div>
          </div>
        </div>

        <div className="w-full sm:w-10/12 p-2 sm:p-3 md:p-4">
          <div className="flex items-center justify-between flex-col sm:flex-row gap-2 sm:gap-0">
            <h1 className="font-semibold text-sm sm:text-base md:text-lg lg:text-xl text-center sm:text-left">
              {fullName}
            </h1>
            <motion.div whileHover={{ rotate: 10, scale: 1.1 }}>
              <VerifyGolden className="w-5 h-5 sm:w-6 sm:h-6" />
            </motion.div>
          </div>

          <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1EB2] mt-1 text-center sm:text-left">
            {credentials}
          </h4>
          {console.log("doctor Crediantials", doctor)}

          <div className="flex flex-col sm:flex-row items-center sm:items-start justify-center sm:justify-start gap-2 sm:gap-4 md:gap-6 mt-2">
            {specialities.map(renderSpeciality)}
          </div>

          <div className="flex flex-col sm:flex-row items-center sm:items-start justify-center sm:justify-start gap-4 sm:gap-6 md:gap-8 mt-2 sm:mt-3">
            {country !== "N/A" && (
              <div className="flex items-center justify-center sm:justify-start gap-1 sm:gap-2">
                <LocationIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                <h1 className="font-bold text-[#1E1E1EB2] text-xs sm:text-sm md:text-base">
                  {getSingleCountryLabel(country)}
                </h1>
              </div>
            )}
          </div>

          <div className="flex flex-wrap items-center justify-center sm:justify-start gap-2 sm:gap-3 md:gap-4 mt-2 sm:mt-3">
            <motion.div
              className="flex items-center justify-center bg-[#CCDCFF4D] py-1 px-2 sm:py-1.5 sm:px-3 md:py-2 md:px-4 rounded-full"
              whileHover={{ scale: 1.05, backgroundColor: "#CCDCFF80" }}
            >
              <h1 className="font-bold text-[#0052FD] text-xs sm:text-sm md:text-base">
                {`${experience} Years Experience`}
              </h1>
            </motion.div>
          </div>

          <div className="flex items-center justify-center gap-2 sm:gap-3 w-full sm:w-3/4 md:w-2/3 lg:w-1/2 mt-3 sm:mt-4">
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              className="w-1/2"
            >
              <Button
                variant="secondary"
                className="w-full text-xs sm:text-sm md:text-base py-1 sm:py-2"
                onClick={() =>
                  navigate("/patient/doctor-profile", {
                    state: {
                      doctorId: doctor?.id,
                    },
                  })
                }
              >
                View Profile
              </Button>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              className="w-1/2"
            >
              <Button
                variant="primary"
                className="w-full text-xs sm:text-sm md:text-base py-1 sm:py-2"
                onClick={() =>
                  navigate("/patient/book-appointment", {
                    state: {
                      doctorId: doctor?.id,
                    },
                  })
                }
              >
                Book Appointment
              </Button>
            </motion.div>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      className="flex-shrink-0 border border-[#E7E8E9] rounded-xl p-3 sm:p-4 w-[280px] sm:w-[300px] md:w-[320px] hover:bg-[#0052FD08]"
      whileHover={{
        y: -5,
        boxShadow: "0 10px 25px rgba(0, 82, 253, 0.1)",
        borderColor: "#0052FD40",
      }}
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <div className="flex items-center justify-between">
        <motion.div whileHover={{ rotate: 10, scale: 1.1 }}>
          <VerifyGolden className="w-6 h-6 sm:w-7 sm:h-7" />
        </motion.div>
      </div>

      <div className="flex w-full items-center justify-center flex-col">
        <motion.div
          className="h-[70px] w-[70px] sm:h-[80px] sm:w-[80px] md:h-[90px] md:w-[90px] rounded-full overflow-hidden border border-[#0052FD] -mt-6 sm:-mt-7 md:-mt-8"
          whileHover={{ scale: 1.05 }}
        >
          <Avatar className="w-full h-full object-cover ">
            <AvatarImage src={profileImage || DefaultAvatar} />
            <AvatarFallback>{fullName}</AvatarFallback>
          </Avatar>
        </motion.div>

        <h1 className="font-semibold text-sm sm:text-[15px] md:text-[16px] mt-2 sm:mt-3">
          {fullName}
        </h1>

        <div className="mt-1 sm:mt-2">{specialities.map(renderSpeciality)}</div>

        <motion.div
          className="flex items-center justify-center bg-[#CCDCFF4D] py-1 px-3 sm:py-2 sm:px-4 rounded-full mt-1 sm:mt-2"
          whileHover={{ scale: 1.05, backgroundColor: "#CCDCFF80" }}
        >
          <h1 className="font-bold text-[#0052FD] text-xs sm:text-[13px] md:text-[14px]">
            {`${experience} Years Experience`}
          </h1>
        </motion.div>

        {country !== "N/A" && (
          <div className="flex items-center justify-center gap-1 mt-1 sm:mt-2">
            <LocationIcon className="w-4 h-4 sm:w-5 sm:h-5" />
            <h1 className="font-bold text-[#1E1E1EB2] text-xs sm:text-[13px] md:text-[14px]">
              {getSingleCountryLabel(country)}
            </h1>
          </div>
        )}
      </div>

      <div className="flex items-center justify-center gap-2 w-full mt-2 sm:mt-3">
        <motion.div
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          className="w-2/4"
        >
          <Button
            variant="secondary"
            className="w-full text-xs sm:text-sm py-1 sm:py-2"
            onClick={() =>
              navigate("/patient/doctor-profile", {
                state: {
                  doctorId: doctor?.id,
                },
              })
            }
          >
            View Profile
          </Button>
        </motion.div>
        <motion.div
          whileHover={{ scale: 1.03 }}
          whileTap={{ scale: 0.97 }}
          className="w-2/4"
        >
          <Button
            variant="primary"
            className="w-full text-xs sm:text-sm py-1 sm:py-2"
            onClick={() =>
              navigate("/patient/book-appointment", {
                state: {
                  doctorId: doctor?.id,
                },
              })
            }
          >
            Book Appointment
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );
};

export default memo(DoctorCard);
