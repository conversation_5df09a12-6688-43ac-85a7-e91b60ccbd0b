import { createContext, useContext, useState } from "react";
import Nudge from "./Nudge";

const NudgeContext = createContext();

export const useNudge = () => {
  const context = useContext(NudgeContext);
  if (!context) {
    throw new Error("useNudge must be used within a NudgeProvider");
  }
  return context;
};

export const NudgeProvider = ({ children }) => {
  const [nudgeConfig, setNudgeConfig] = useState({
    isVisible: false,
    message: "",
    progress: 0,
    onAction: () => {},
    onDismiss: () => {},
    actionText: "Complete",
    showProgress: true,
    variant: "gradient",
    position: "top-right",
  });

  const showNudge = (config) => {
    setNudgeConfig({
      ...nudgeConfig,
      ...config,
      isVisible: true,
    });
  };

  const hideNudge = () => {
    setNudgeConfig({
      ...nudgeConfig,
      isVisible: false,
    });
  };

  return (
    <NudgeContext.Provider value={{ showNudge, hideNudge }}>
      {children}
      <Nudge {...nudgeConfig} />
    </NudgeContext.Provider>
  );
};
