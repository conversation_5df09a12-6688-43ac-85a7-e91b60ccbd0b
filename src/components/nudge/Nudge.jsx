import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { X, ArrowRight } from "lucide-react";
import { Button } from "../../components/ui/button";
import { Progress } from "../../components/ui/progress";

const Nudge = ({
  message = "Complete your profile for personalized experience",
  progress = 70,
  onAction,
  onDismiss,
  showProgress = true,
  variant = "gradient",
  isVisible = true,
  position = "top-right",
}) => {
  const [visible, setVisible] = useState(isVisible);

  const handleDismiss = () => {
    setVisible(false);
    if (onDismiss) onDismiss();
  };

  const handleAction = () => {
    if (onAction) onAction();
  };

  const positionClasses = {
    "top-right": "top-20 right-4",
    "top-left": "top-4 left-4",
    "bottom-right": "bottom-4 right-4",
    "bottom-left": "bottom-4 left-4",
  };

  const variantClasses = {
    gradient: "bg-gradient-to-r from-orange-500 via-amber-500 to-yellow-400",
    blue: "bg-blue-600",
    green: "bg-green-600",
    neutral: "bg-gray-800",
  };

  return (
    <AnimatePresence>
      {visible && (
        <motion.div
          initial={{ opacity: 0, y: -20, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -20, scale: 0.95 }}
          transition={{ duration: 0.2 }}
          className={`fixed ${
            positionClasses[position]
          } z-50 shadow-lg rounded-lg overflow-hidden max-w-xs w-full sm:w-auto`}
        >
          <div
            className={`${variantClasses[variant]} p-4 text-white relative flex flex-col sm:flex-row items-start sm:items-center gap-3`}
          >
            <div className="flex-1 pr-8">
              {showProgress && (
                <div className="mb-2">
                  <Progress value={progress} className="h-2 bg-white/30" />
                  <span className="text-sm font-medium mt-1 inline-block text-[#000000]">
                    {progress}%
                  </span>
                </div>
              )}
              <p className="text-sm sm:text-base font-medium text-[#000000]">
                {message}
              </p>
            </div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                onClick={handleAction}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-2 h-10 w-10 flex items-center justify-center"
              >
                <ArrowRight className="h-5 w-5" />
              </Button>
            </motion.div>

            <motion.button
              onClick={handleDismiss}
              className="absolute top-2 right-2 text-white/80 hover:text-white"
              aria-label="Dismiss"
              whileHover={{ scale: 1.1, rotate: 90 }}
              transition={{ duration: 0.2 }}
            >
              <X className="h-4 w-4 text-[#000000]" />
            </motion.button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default Nudge;
