/* eslint-disable */
import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>bsTrigger,
} from "../../components/ui/tabs";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../../components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import { Input } from "../../components/ui/input";
import { Textarea } from "../../components/ui/textarea";
import { Button } from "../../components/ui/button";
import { CirclePlus, Edit } from "lucide-react";
import { useSelector } from "react-redux";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import DatePicker from "../date-picker/DatePicker";

const patientLogsSchema = z.object({
  date: z.string().min(1, "Date is required"),
  time: z.string().min(1, "Time is required"),
  log: z
    .string()
    .min(1, "Notes are required")
    .max(300, "Notes must be less than 300 characters"),
});

const PatientLogsDialog = ({ onAddLog, user }) => {
  const currentDate = new Date();
  const currentTime = currentDate.toLocaleTimeString("en-US", {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
  });

  const form = useForm({
    resolver: zodResolver(patientLogsSchema),
    defaultValues: {
      date: currentDate.toISOString(),
      time: currentTime,
      log: "",
    },
  });

  const onSubmit = (data) => {
    // Ensure date is properly formatted as ISO string
    const formattedData = {
      ...data,
      date: data.date instanceof Date ? data.date.toISOString() : data.date,
    };

    onAddLog(formattedData);

    // Reset form with fresh date/time
    const newCurrentDate = new Date();
    const newCurrentTime = newCurrentDate.toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
    });

    form.reset({
      date: newCurrentDate.toISOString(),
      time: newCurrentTime,
      log: "",
    });
  };

  return (
    <DialogContent className="sm:max-w-[500px]">
      <DialogHeader>
        <DialogTitle>Add Patient Log</DialogTitle>
        <DialogDescription className="sr-only">
          Add a new log entry. Date and time are automatically set to current
          values.
        </DialogDescription>
      </DialogHeader>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date</FormLabel>
                  <FormControl>
                    <div className="pointer-events-none opacity-60">
                      <DatePicker
                        field={field}
                        label=""
                        className="bg-muted cursor-not-allowed"
                      />
                    </div>
                  </FormControl>
                  <FormDescription className="sr-only">
                    Current date (auto-filled)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="time"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Time</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="time"
                      disabled={true}
                      className="bg-muted"
                    />
                  </FormControl>
                  <FormDescription className="sr-only">
                    Current time (auto-filled)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="log"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="How are you feeling? Any side effects or observations..."
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Add any notes about how you're feeling (max 300 characters)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <DialogFooter className="gap-2">
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" variant={"primary"}>
              Add Log
            </Button>
          </DialogFooter>
        </form>
      </Form>
    </DialogContent>
  );
};

export default PatientLogsDialog;
