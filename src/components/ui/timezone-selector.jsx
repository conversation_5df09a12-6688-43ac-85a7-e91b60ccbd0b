import React, { useState, useMemo } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./select";
import { Clock } from "lucide-react";

// Common timezones grouped by region
const TIMEZONE_GROUPS = {
  "North America": [
    { value: "America/New_York", label: "Eastern Time (ET)", offset: "UTC-5/-4" },
    { value: "America/Chicago", label: "Central Time (CT)", offset: "UTC-6/-5" },
    { value: "America/Denver", label: "Mountain Time (MT)", offset: "UTC-7/-6" },
    { value: "America/Los_Angeles", label: "Pacific Time (PT)", offset: "UTC-8/-7" },
    { value: "America/Anchorage", label: "Alaska Time (AKT)", offset: "UTC-9/-8" },
    { value: "Pacific/Honolulu", label: "Hawaii Time (HST)", offset: "UTC-10" },
  ],
  "Europe": [
    { value: "Europe/London", label: "Greenwich Mean Time (GMT)", offset: "UTC+0/+1" },
    { value: "Europe/Paris", label: "Central European Time (CET)", offset: "UTC+1/+2" },
    { value: "Europe/Berlin", label: "Central European Time (CET)", offset: "UTC+1/+2" },
    { value: "Europe/Rome", label: "Central European Time (CET)", offset: "UTC+1/+2" },
    { value: "Europe/Madrid", label: "Central European Time (CET)", offset: "UTC+1/+2" },
    { value: "Europe/Moscow", label: "Moscow Time (MSK)", offset: "UTC+3" },
  ],
  "Asia": [
    { value: "Asia/Dubai", label: "Gulf Standard Time (GST)", offset: "UTC+4" },
    { value: "Asia/Karachi", label: "Pakistan Standard Time (PKT)", offset: "UTC+5" },
    { value: "Asia/Kolkata", label: "India Standard Time (IST)", offset: "UTC+5:30" },
    { value: "Asia/Dhaka", label: "Bangladesh Standard Time (BST)", offset: "UTC+6" },
    { value: "Asia/Bangkok", label: "Indochina Time (ICT)", offset: "UTC+7" },
    { value: "Asia/Shanghai", label: "China Standard Time (CST)", offset: "UTC+8" },
    { value: "Asia/Tokyo", label: "Japan Standard Time (JST)", offset: "UTC+9" },
    { value: "Asia/Seoul", label: "Korea Standard Time (KST)", offset: "UTC+9" },
  ],
  "Australia & Pacific": [
    { value: "Australia/Perth", label: "Australian Western Time (AWST)", offset: "UTC+8" },
    { value: "Australia/Adelaide", label: "Australian Central Time (ACST)", offset: "UTC+9:30/+10:30" },
    { value: "Australia/Sydney", label: "Australian Eastern Time (AEST)", offset: "UTC+10/+11" },
    { value: "Pacific/Auckland", label: "New Zealand Time (NZST)", offset: "UTC+12/+13" },
  ],
  "Africa": [
    { value: "Africa/Cairo", label: "Eastern European Time (EET)", offset: "UTC+2" },
    { value: "Africa/Johannesburg", label: "South Africa Standard Time (SAST)", offset: "UTC+2" },
    { value: "Africa/Lagos", label: "West Africa Time (WAT)", offset: "UTC+1" },
  ],
  "South America": [
    { value: "America/Sao_Paulo", label: "Brasília Time (BRT)", offset: "UTC-3/-2" },
    { value: "America/Argentina/Buenos_Aires", label: "Argentina Time (ART)", offset: "UTC-3" },
    { value: "America/Santiago", label: "Chile Time (CLT)", offset: "UTC-4/-3" },
  ],
};

const TimezoneSelector = ({ 
  value, 
  onValueChange, 
  placeholder = "Select timezone...",
  className = "",
  disabled = false 
}) => {
  const [searchTerm, setSearchTerm] = useState("");

  // Get user's current timezone as default
  const userTimezone = useMemo(() => {
    try {
      return Intl.DateTimeFormat().resolvedOptions().timeZone;
    } catch (error) {
      return "UTC";
    }
  }, []);

  // Flatten all timezones for search
  const allTimezones = useMemo(() => {
    const flattened = [];
    Object.entries(TIMEZONE_GROUPS).forEach(([region, timezones]) => {
      timezones.forEach(tz => {
        flattened.push({ ...tz, region });
      });
    });
    return flattened;
  }, []);

  // Filter timezones based on search term
  const filteredTimezones = useMemo(() => {
    if (!searchTerm) return TIMEZONE_GROUPS;
    
    const filtered = {};
    Object.entries(TIMEZONE_GROUPS).forEach(([region, timezones]) => {
      const matchingTimezones = timezones.filter(tz => 
        tz.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tz.value.toLowerCase().includes(searchTerm.toLowerCase()) ||
        tz.offset.toLowerCase().includes(searchTerm.toLowerCase())
      );
      if (matchingTimezones.length > 0) {
        filtered[region] = matchingTimezones;
      }
    });
    return filtered;
  }, [searchTerm]);

  // Get display text for selected timezone
  const getSelectedTimezoneDisplay = (selectedValue) => {
    if (!selectedValue) return placeholder;
    
    const timezone = allTimezones.find(tz => tz.value === selectedValue);
    if (timezone) {
      return `${timezone.label} (${timezone.offset})`;
    }
    return selectedValue;
  };

  // Get current time in selected timezone
  const getCurrentTimeInTimezone = (timezoneValue) => {
    if (!timezoneValue) return "";
    
    try {
      const now = new Date();
      const timeString = now.toLocaleTimeString("en-US", {
        timeZone: timezoneValue,
        hour: "2-digit",
        minute: "2-digit",
        hour12: true,
      });
      return ` • ${timeString}`;
    } catch (error) {
      return "";
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <Select 
        value={value} 
        onValueChange={onValueChange}
        disabled={disabled}
      >
        <SelectTrigger className="w-full">
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <SelectValue placeholder={placeholder}>
              {getSelectedTimezoneDisplay(value)}
            </SelectValue>
          </div>
        </SelectTrigger>
        <SelectContent className="max-h-[300px]">
          {/* User's current timezone at the top */}
          {!value && (
            <>
              <SelectItem value={userTimezone} className="font-medium">
                <div className="flex flex-col">
                  <span>🌍 Your Current Timezone</span>
                  <span className="text-sm text-muted-foreground">
                    {allTimezones.find(tz => tz.value === userTimezone)?.label || userTimezone}
                    {getCurrentTimeInTimezone(userTimezone)}
                  </span>
                </div>
              </SelectItem>
              <div className="border-t my-1" />
            </>
          )}
          
          {/* Grouped timezones */}
          {Object.entries(filteredTimezones).map(([region, timezones]) => (
            <div key={region}>
              <div className="px-2 py-1.5 text-sm font-semibold text-muted-foreground bg-muted/50">
                {region}
              </div>
              {timezones.map((timezone) => (
                <SelectItem key={timezone.value} value={timezone.value}>
                  <div className="flex flex-col">
                    <span>{timezone.label}</span>
                    <span className="text-sm text-muted-foreground">
                      {timezone.offset}{getCurrentTimeInTimezone(timezone.value)}
                    </span>
                  </div>
                </SelectItem>
              ))}
            </div>
          ))}
        </SelectContent>
      </Select>
      
      {/* Show selected timezone info */}
      {value && (
        <div className="text-sm text-muted-foreground flex items-center gap-2">
          <Clock className="h-3 w-3" />
          <span>
            Current time: {getCurrentTimeInTimezone(value).replace(" • ", "")}
          </span>
        </div>
      )}
    </div>
  );
};

export default TimezoneSelector;
