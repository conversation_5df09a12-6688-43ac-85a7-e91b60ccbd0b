import * as React from "react";

import { cn } from "../../lib/utils";

const Input = React.forwardRef<
  HTMLInputElement,
  React.ComponentProps<"input"> & { hasError?: boolean }
>(({ className, type, hasError, ...props }, ref) => {
  return (
    <input
      type={type}
      className={cn(
        "flex h-9 w-full items-center justify-between rounded-lg border bg-transparent px-3 py-1 text-input-text-color text-[14px] font-bold shadow-sm transition-colors font-DMSans placeholder:text-[#1E1E1E80] focus-visible:outline-primary focus-visible:ring-0 focus-visible:ring-primary disabled:cursor-not-allowed disabled:opacity-50 md:text-sm [&>span]:line-clamp-1",
        hasError ? "border-destructive" : "border-border",
        className,
      )}
      ref={ref}
      {...props}
    />
  );
});
Input.displayName = "Input";

export { Input };
