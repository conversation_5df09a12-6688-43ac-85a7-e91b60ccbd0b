import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "../../lib/utils";
import { X } from "lucide-react";

const badgeVariants = cva(
  "inline-flex items-center rounded-2xl border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default:
          "border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",
        secondary:
          "border-transparent bg-[#E7E8E9] text-secondary-foreground rounded-md hover:bg-[#E7E8E9] p-2",
        destructive:
          "border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",
        outline: "text-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  },
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  onClose?: () => void;
}

function Badge({
  className,
  variant,
  onClose,
  children,
  ...props
}: BadgeProps) {
  return (
    <div className={cn(badgeVariants({ variant }), className)} {...props}>
      {children}
      {onClose && (
        <button
          onClick={onClose}
          className="ml-1 p-0.5 rounded-full hover:bg-opacity-20 hover:bg-gray-500 focus:outline-none"
          aria-label="Close"
        >
          <X className="h-3 w-3 text-white bg-border rounded-full" />
        </button>
      )}
    </div>
  );
}

export { Badge, badgeVariants };
