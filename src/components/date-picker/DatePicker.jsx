import React, { useState, useMemo } from "react";
import { format } from "date-fns";
import { Calendar } from "../ui/calendar";
import { Button } from "../ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { cn } from "../../lib/utils";

import { ReactComponent as CalenderIcon } from "../../assets/svgs/CalendarIcon.svg";
import { FormControl, FormItem, FormLabel, FormMessage } from "../ui/form";

const DatePicker = ({ field, label, value, onChange, className }) => {
  const fieldValue = field?.value ?? value;
  const fieldOnChange = field?.onChange ?? onChange;

  const [open, setOpen] = useState(false);
  const [yearDropdownOpen, setYearDropdownOpen] = useState(false); // Add state for year dropdown
  const [calendarDate, setCalendarDate] = useState(
    fieldValue ? new Date(fieldValue) : new Date(),
  );

  const selectedDate = fieldValue ? new Date(fieldValue) : null;
  const currentYear = new Date().getFullYear();

  // Generate years in descending order (newest to oldest)
  const years = useMemo(() => {
    // Create array from current year down to 1900
    const yearsArray = [];
    for (let year = currentYear; year >= 1900; year--) {
      yearsArray.push(year);
    }
    return yearsArray;
  }, [currentYear]);

  const handleYearChange = (yearStr) => {
    const year = Number.parseInt(yearStr, 10);
    const newCalendarDate = new Date(calendarDate);
    newCalendarDate.setFullYear(year);
    setCalendarDate(newCalendarDate);

    if (selectedDate) {
      const newSelectedDate = new Date(selectedDate);
      newSelectedDate.setFullYear(year);
      fieldOnChange(newSelectedDate.toISOString());
    }

    // Close the year dropdown after selection
    setYearDropdownOpen(false);
  };

  const handleDateSelect = (date) => {
    fieldOnChange(date ? date.toISOString() : "");
    setOpen(false);
  };

  const TriggerButton = (
    <Button
      variant="outline"
      className={cn(
        "w-full text-left font-normal h-9",
        !selectedDate ? "text-[#1E1E1E80]" : "text-[#1E1E1E]",
        className,
      )}
    >
      <div className="flex items-center w-full font-bold">
        <CalenderIcon className="mr-2" />
        <span>
          {selectedDate && !isNaN(selectedDate.getTime())
            ? format(selectedDate, "PPP")
            : "Select date"}
        </span>
      </div>
    </Button>
  );

  const CalendarContent = (
    <PopoverContent className="w-auto p-0">
      <div className="flex items-center justify-between p-2">
        <Select
          value={(selectedDate
            ? selectedDate.getFullYear()
            : calendarDate.getFullYear()
          ).toString()}
          onValueChange={handleYearChange}
          open={yearDropdownOpen}
          onOpenChange={setYearDropdownOpen}
        >
          <SelectTrigger className="w-[120px]">
            <SelectValue>
              {(selectedDate
                ? selectedDate.getFullYear()
                : calendarDate.getFullYear()
              ).toString()}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {years.map((year) => (
              <SelectItem key={year} value={year.toString()}>
                {year}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
      <Calendar
        mode="single"
        selected={selectedDate}
        onSelect={handleDateSelect}
        month={calendarDate}
        onMonthChange={setCalendarDate}
        disabled={(date) => date > new Date() || date < new Date("1900-01-01")}
        initialFocus
      />
    </PopoverContent>
  );

  return (
    <Popover open={open} onOpenChange={setOpen}>
      {label ? (
        <FormItem>
          <FormLabel>{label}</FormLabel>
          <FormControl>
            <PopoverTrigger asChild>{TriggerButton}</PopoverTrigger>
          </FormControl>
          <FormMessage />
          {CalendarContent}
        </FormItem>
      ) : (
        <>
          <PopoverTrigger asChild>{TriggerButton}</PopoverTrigger>
          {CalendarContent}
        </>
      )}
    </Popover>
  );
};

export default DatePicker;
