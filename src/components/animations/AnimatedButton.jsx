import { motion } from "framer-motion";
import { Button } from "../ui/button";
import { forwardRef } from "react";

const AnimatedButton = forwardRef(
  ({ children, className, variant, size, onClick, ...props }, ref) => {
    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        <Button
          ref={ref}
          className={className}
          variant={variant}
          size={size}
          onClick={onClick}
          {...props}
        >
          {children}
        </Button>
      </motion.div>
    );
  },
);

AnimatedButton.displayName = "AnimatedButton";

export default AnimatedButton;
