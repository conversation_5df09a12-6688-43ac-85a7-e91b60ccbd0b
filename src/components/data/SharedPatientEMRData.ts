/* eslint-disable */
// Shared Patient EMR data with realistic information
const patients: string[] = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];

const doctors: string[] = [
  "Dr. <PERSON>",
  "Dr. <PERSON>",
  "Dr. <PERSON>",
  "Dr. <PERSON>",
  "Dr. <PERSON>",
  "Dr. <PERSON>",
  "Dr. <PERSON>",
  "Dr. <PERSON>",
  "Dr. <PERSON>",
  "Dr. <PERSON>",
];

const locations: string[] = [
  "New York",
  "Los Angeles",
  "Chicago",
  "Houston",
  "San Francisco",
  "Boston",
  "Miami",
  "Seattle",
  "Denver",
  "Atlanta",
];

const medicalConcerns: string[] = [
  "Gastrointestinal Disorder",
  "Cardiology Checkup",
  "Orthopedic Consultation",
  "Neurology Exam",
  "General Checkup",
  "Dermatology Consultation",
  "Ophthalmology Exam",
  "Dental Checkup",
  "Pediatric Consultation",
  "Gynecology Exam",
];

const appointmentTimes: string[] = [
  "09:00 AM",
  "10:30 AM",
  "11:30 AM",
  "01:00 PM",
  "02:30 PM",
  "03:45 PM",
  "05:00 PM",
];

const getRandomItem = <T>(arr: T[]): T =>
  arr[Math.floor(Math.random() * arr.length)];

// Function to get a different random item than the provided one
const getDifferentRandomItem = <T>(arr: T[], excludeItem: T): T => {
  let item = getRandomItem(arr);
  while (item === excludeItem) {
    item = getRandomItem(arr);
  }
  return item;
};

// Function to generate random date within a range
const getRandomDate = (start: Date, end: Date): Date => {
  return new Date(
    start.getTime() + Math.random() * (end.getTime() - start.getTime()),
  );
};

// Set the current date to 21/5/2025
const today = new Date(2025, 4, 21); // Month is 0-indexed, so 4 = May

// Get the current week's Monday and Sunday
const getWeekRange = (date: Date) => {
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
  const monday = new Date(date);
  monday.setDate(diff);
  monday.setHours(0, 0, 0, 0);

  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);
  sunday.setHours(23, 59, 59, 999);

  return { start: monday, end: sunday };
};

const thisWeekRange = getWeekRange(today);

// Get last week's range
const lastWeekStart = new Date(thisWeekRange.start);
lastWeekStart.setDate(lastWeekStart.getDate() - 7);
const lastWeekEnd = new Date(thisWeekRange.start);
lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
lastWeekEnd.setHours(23, 59, 59, 999);

// Get next week's range
const nextWeekStart = new Date(thisWeekRange.end);
nextWeekStart.setDate(nextWeekStart.getDate() + 1);
const nextWeekEnd = new Date(nextWeekStart);
nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
nextWeekEnd.setHours(23, 59, 59, 999);

// Get date ranges for random date generation
const oneMonthAgo = new Date(today);
oneMonthAgo.setMonth(today.getMonth() - 1);
const oneMonthFromNow = new Date(today);
oneMonthFromNow.setMonth(today.getMonth() + 1);

interface SharedEMR {
  id: string;
  patient: string;
  location: string;
  medicalConcern: string;
  date: Date;
  formattedDate: string;
  time: string;
  sharedBy: string;
  sharedTo: string;
  sharedDate: Date;
  formattedSharedDate: string;
}

// Create shared EMR records with a realistic distribution
const createSharedEMRs = (): SharedEMR[] => {
  const records: SharedEMR[] = [];

  // Create records for last week
  for (let i = 0; i < 50; i++) {
    const appointmentDate = getRandomDate(lastWeekStart, lastWeekEnd);
    const sharedDate = new Date(appointmentDate);
    sharedDate.setHours(sharedDate.getHours() + Math.floor(Math.random() * 48)); // Shared within 48 hours

    records.push(createSharedEMR(i, appointmentDate, sharedDate));
  }

  // Create records for this week
  for (let i = 50; i < 150; i++) {
    const appointmentDate = getRandomDate(
      thisWeekRange.start,
      thisWeekRange.end,
    );
    const sharedDate = new Date(appointmentDate);
    sharedDate.setHours(sharedDate.getHours() + Math.floor(Math.random() * 48)); // Shared within 48 hours

    records.push(createSharedEMR(i, appointmentDate, sharedDate));
  }

  // Create records for next week
  for (let i = 150; i < 200; i++) {
    const appointmentDate = getRandomDate(nextWeekStart, nextWeekEnd);
    const sharedDate = new Date(appointmentDate);
    sharedDate.setHours(sharedDate.getHours() - Math.floor(Math.random() * 48)); // Shared before appointment

    records.push(createSharedEMR(i, appointmentDate, sharedDate));
  }

  // Create some additional records in the broader date range
  for (let i = 200; i < 300; i++) {
    const appointmentDate = getRandomDate(oneMonthAgo, oneMonthFromNow);
    const sharedDate = new Date(appointmentDate);

    // 50% chance of sharing before appointment, 50% chance after
    if (Math.random() > 0.5) {
      sharedDate.setHours(
        sharedDate.getHours() - Math.floor(Math.random() * 48),
      ); // Shared before appointment
    } else {
      sharedDate.setHours(
        sharedDate.getHours() + Math.floor(Math.random() * 48),
      ); // Shared after appointment
    }

    records.push(createSharedEMR(i, appointmentDate, sharedDate));
  }

  return records;
};

// Helper function to create a single shared EMR record
const createSharedEMR = (
  index: number,
  appointmentDate: Date,
  sharedDate: Date,
): SharedEMR => {
  // Format dates as DD/MM/YYYY for display
  const formattedDate = `${appointmentDate.getDate().toString().padStart(2, "0")}/${(appointmentDate.getMonth() + 1).toString().padStart(2, "0")}/${appointmentDate.getFullYear()}`;
  const formattedSharedDate = `${sharedDate.getDate().toString().padStart(2, "0")}/${(sharedDate.getMonth() + 1).toString().padStart(2, "0")}/${sharedDate.getFullYear()}`;

  // Get a doctor who shared the EMR
  const sharedBy = getRandomItem(doctors);

  // Get a different doctor who received the EMR
  const sharedTo = getDifferentRandomItem(doctors, sharedBy);

  return {
    id: `EMR${10000 + index}`,
    patient: getRandomItem(patients),
    location: getRandomItem(locations),
    medicalConcern: getRandomItem(medicalConcerns),
    date: appointmentDate,
    formattedDate: formattedDate,
    time: getRandomItem(appointmentTimes),
    sharedBy: sharedBy,
    sharedTo: sharedTo,
    sharedDate: sharedDate,
    formattedSharedDate: formattedSharedDate,
  };
};

const SharedPatientEMRData: SharedEMR[] = createSharedEMRs();

export default SharedPatientEMRData;
