/* eslint-disable */
// Patient data with realistic information
const firstNames: string[] = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];

const lastNames: string[] = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];

const medicalConcerns: string[] = [
  "Gastrointestinal Disorder",
  "Hypertension",
  "Diabetes Type 2",
  "Migraine",
  "Lower Back Pain",
  "Anxiety Disorder",
  "Asthma",
  "Allergic Rhinitis",
  "Arthritis",
  "Eczema",
  "Insomnia",
  "Depression",
  "Hypothyroidism",
  "Urinary Tract Infection",
  "Common Cold",
];

const appointmentStatuses: string[] = ["Upcoming", "Completed", "Canceled"];
const appointmentTimes: string[] = [
  "09:00 AM",
  "10:30 AM",
  "11:30 AM",
  "01:00 PM",
  "02:30 PM",
  "03:45 PM",
  "05:00 PM",
];

const getRandomItem = <T>(arr: T[]): T =>
  arr[Math.floor(Math.random() * arr.length)];

// Function to generate random date within a range
const getRandomDate = (start: Date, end: Date): Date => {
  return new Date(
    start.getTime() + Math.random() * (end.getTime() - start.getTime()),
  );
};

// Function to generate random email based on name
const generateEmail = (firstName: string, lastName: string): string => {
  const domains = [
    "gmail.com",
    "yahoo.com",
    "outlook.com",
    "hotmail.com",
    "icloud.com",
  ];
  const randomNum = Math.floor(Math.random() * 100);
  return `${firstName.toLowerCase()}${lastName.toLowerCase()}${randomNum}@${getRandomItem(domains)}`;
};

// Function to generate random phone number
const generatePhoneNumber = (): string => {
  const countryCode = "+92";
  const areaCode = Math.floor(Math.random() * 900) + 100;
  const firstPart = Math.floor(Math.random() * 9000) + 1000;
  const secondPart = Math.floor(Math.random() * 9000) + 1000;
  return `${countryCode} ${areaCode} ${firstPart}${secondPart}`;
};

// Set the current date to 21/5/2025
const today = new Date(2025, 4, 21); // Month is 0-indexed, so 4 = May

// Get the current week's Monday and Sunday
const getWeekRange = (date: Date) => {
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
  const monday = new Date(date);
  monday.setDate(diff);
  monday.setHours(0, 0, 0, 0);

  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);
  sunday.setHours(23, 59, 59, 999);

  return { start: monday, end: sunday };
};

const thisWeekRange = getWeekRange(today);

// Get last week's range
const lastWeekStart = new Date(thisWeekRange.start);
lastWeekStart.setDate(lastWeekStart.getDate() - 7);
const lastWeekEnd = new Date(thisWeekRange.start);
lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
lastWeekEnd.setHours(23, 59, 59, 999);

// Get next week's range
const nextWeekStart = new Date(thisWeekRange.end);
nextWeekStart.setDate(nextWeekStart.getDate() + 1);
const nextWeekEnd = new Date(nextWeekStart);
nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
nextWeekEnd.setHours(23, 59, 59, 999);

// Get date ranges for random date generation
const oneMonthAgo = new Date(today);
oneMonthAgo.setMonth(today.getMonth() - 1);
const oneMonthFromNow = new Date(today);
oneMonthFromNow.setMonth(today.getMonth() + 1);

interface Patient {
  patientId: string;
  firstName: string;
  lastName: string;
  email: string;
  contactNumber: string;
  medicalConcern: string;
  appointmentStatus: string;
  date: Date;
  formattedDate: string;
  time: string;
}

// Helper function to determine status based on date
const getStatusBasedOnDate = (date: Date, currentDate: Date): string => {
  if (date < currentDate) {
    // Past appointments are Completed
    return "Completed";
  } else {
    // Future appointments are Upcoming
    return "Upcoming";
  }
};

// Create patients with a realistic distribution
const createPatients = (): Patient[] => {
  const patients: Patient[] = [];

  // Create patients for last week (mostly completed)
  for (let i = 0; i < 50; i++) {
    const appointmentDate = getRandomDate(lastWeekStart, lastWeekEnd);
    const status = getStatusBasedOnDate(appointmentDate, today);

    patients.push(createPatient(i, appointmentDate, status));
  }

  // Create patients for this week (mix of completed and upcoming)
  for (let i = 50; i < 150; i++) {
    const appointmentDate = getRandomDate(
      thisWeekRange.start,
      thisWeekRange.end,
    );
    const status = getStatusBasedOnDate(appointmentDate, today);

    patients.push(createPatient(i, appointmentDate, status));
  }

  // Create patients for next week (all upcoming)
  for (let i = 150; i < 200; i++) {
    const appointmentDate = getRandomDate(nextWeekStart, nextWeekEnd);
    const status = getStatusBasedOnDate(appointmentDate, today);

    patients.push(createPatient(i, appointmentDate, status));
  }

  // Create some additional patients in the broader date range
  for (let i = 200; i < 300; i++) {
    const appointmentDate = getRandomDate(oneMonthAgo, oneMonthFromNow);
    const status = getStatusBasedOnDate(appointmentDate, today);

    patients.push(createPatient(i, appointmentDate, status));
  }

  return patients;
};

// Helper function to create a single patient
const createPatient = (
  index: number,
  appointmentDate: Date,
  status: string,
): Patient => {
  // Format date as DD/MM/YYYY for display
  const formattedDate = `${appointmentDate.getDate().toString().padStart(2, "0")}/${(appointmentDate.getMonth() + 1).toString().padStart(2, "0")}/${appointmentDate.getFullYear()}`;

  const firstName = getRandomItem(firstNames);
  const lastName = getRandomItem(lastNames);

  return {
    patientId: `P${10000 + index}`,
    firstName: firstName,
    lastName: lastName,
    email: generateEmail(firstName, lastName),
    contactNumber: generatePhoneNumber(),
    medicalConcern: getRandomItem(medicalConcerns),
    appointmentStatus: status,
    date: appointmentDate,
    formattedDate: formattedDate,
    time: getRandomItem(appointmentTimes),
  };
};

const PatientsData: Patient[] = createPatients();

export default PatientsData;
