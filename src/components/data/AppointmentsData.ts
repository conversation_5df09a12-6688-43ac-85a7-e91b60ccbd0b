/* eslint-disable */
// Split patients into first and last names
const firstNames: string[] = [
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];

const lastNames: string[] = [
  "<PERSON><PERSON>",
  "<PERSON><PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "Taylor",
  "Anderson",
  "Thomas",
];

const locations: string[] = [
  "New York",
  "Los Angeles",
  "Chicago",
  "Houston",
  "San Francisco",
  "Boston",
  "Miami",
  "Seattle",
  "Denver",
  "Atlanta",
];

const services: string[] = [
  "Gastrointestinal Disorder",
  "Cardiology Checkup",
  "Orthopedic Consultation",
  "Neurology Exam",
  "General Checkup",
  "Dermatology Consultation",
  "Ophthalmology Exam",
  "Dental Checkup",
  "Pediatric Consultation",
  "Gynecology Exam",
];

const statuses: string[] = ["Completed", "Upcoming", "Canceled"];
const paymentStatuses: string[] = ["Complete", "Pending", "Failed"];
const appointmentTimes: string[] = [
  "09:00 AM",
  "10:30 AM",
  "11:30 AM",
  "01:00 PM",
  "02:30 PM",
  "03:45 PM",
  "05:00 PM",
];

const getRandomItem = <T>(arr: T[]): T =>
  arr[Math.floor(Math.random() * arr.length)];

// Function to generate random date within a range
const getRandomDate = (start: Date, end: Date): Date => {
  return new Date(
    start.getTime() + Math.random() * (end.getTime() - start.getTime()),
  );
};

// Set the current date to 21/5/2025
const today = new Date(2025, 4, 21); // Month is 0-indexed, so 4 = May

// Get the current week's Monday and Sunday
const getWeekRange = (date: Date) => {
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
  const monday = new Date(date);
  monday.setDate(diff);
  monday.setHours(0, 0, 0, 0);

  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);
  sunday.setHours(23, 59, 59, 999);

  return { start: monday, end: sunday };
};

const thisWeekRange = getWeekRange(today);

// Get last week's range
const lastWeekStart = new Date(thisWeekRange.start);
lastWeekStart.setDate(lastWeekStart.getDate() - 7);
const lastWeekEnd = new Date(thisWeekRange.start);
lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
lastWeekEnd.setHours(23, 59, 59, 999);

// Get next week's range
const nextWeekStart = new Date(thisWeekRange.end);
nextWeekStart.setDate(nextWeekStart.getDate() + 1);
const nextWeekEnd = new Date(nextWeekStart);
nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
nextWeekEnd.setHours(23, 59, 59, 999);

// Get date ranges for random date generation
const oneMonthAgo = new Date(today);
oneMonthAgo.setMonth(today.getMonth() - 1);
const oneMonthFromNow = new Date(today);
oneMonthFromNow.setMonth(today.getMonth() + 1);

interface Appointment {
  id: string;
  firstName: string;
  lastName: string;
  location: string;
  service: string;
  status: string;
  date: Date;
  formattedDate: string;
  time: string;
  paymentStatus: string;
}

// Helper function to determine status based on date
const getStatusBasedOnDate = (date: Date, currentDate: Date): string => {
  if (date < currentDate) {
    // Past appointments are either Completed or Canceled
    return Math.random() > 0.2 ? "Completed" : "Canceled";
  } else {
    // Future appointments are either Upcoming or Canceled
    return Math.random() > 0.1 ? "Upcoming" : "Canceled";
  }
};

// Create appointments with a realistic distribution
const createAppointments = (): Appointment[] => {
  const appointments: Appointment[] = [];

  // Create appointments for last week (mostly completed)
  for (let i = 0; i < 50; i++) {
    const appointmentDate = getRandomDate(lastWeekStart, lastWeekEnd);
    const status = getStatusBasedOnDate(appointmentDate, today);

    appointments.push(createAppointment(i, appointmentDate, status));
  }

  // Create appointments for this week (mix of completed and upcoming)
  for (let i = 50; i < 150; i++) {
    const appointmentDate = getRandomDate(
      thisWeekRange.start,
      thisWeekRange.end,
    );
    const status = getStatusBasedOnDate(appointmentDate, today);

    appointments.push(createAppointment(i, appointmentDate, status));
  }

  // Create appointments for next week (all upcoming)
  for (let i = 150; i < 200; i++) {
    const appointmentDate = getRandomDate(nextWeekStart, nextWeekEnd);
    const status = getStatusBasedOnDate(appointmentDate, today);

    appointments.push(createAppointment(i, appointmentDate, status));
  }

  // Create some additional appointments in the broader date range
  for (let i = 200; i < 300; i++) {
    const appointmentDate = getRandomDate(oneMonthAgo, oneMonthFromNow);
    const status = getStatusBasedOnDate(appointmentDate, today);

    appointments.push(createAppointment(i, appointmentDate, status));
  }

  return appointments;
};

// Helper function to create a single appointment
const createAppointment = (
  index: number,
  appointmentDate: Date,
  status: string,
): Appointment => {
  // Format date as DD/MM/YYYY for display
  const formattedDate = `${appointmentDate.getDate().toString().padStart(2, "0")}/${(appointmentDate.getMonth() + 1).toString().padStart(2, "0")}/${appointmentDate.getFullYear()}`;

  // Determine payment status based on appointment status
  let paymentStatus = getRandomItem(paymentStatuses);
  if (status === "Completed") {
    // Completed appointments are more likely to have Complete payment status
    paymentStatus = Math.random() > 0.2 ? "Complete" : paymentStatus;
  } else if (status === "Upcoming") {
    // Upcoming appointments are more likely to have Pending payment status
    paymentStatus = Math.random() > 0.3 ? "Pending" : paymentStatus;
  }

  return {
    id: `12345${index + 1}B`,
    firstName: getRandomItem(firstNames),
    lastName: getRandomItem(lastNames),
    location: getRandomItem(locations),
    service: getRandomItem(services),
    status: status,
    date: appointmentDate,
    formattedDate: formattedDate,
    time: getRandomItem(appointmentTimes),
    paymentStatus: paymentStatus,
  };
};

const AppointmentsData: Appointment[] = createAppointments();

export default AppointmentsData;
