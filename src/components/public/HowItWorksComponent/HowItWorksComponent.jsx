import React from "react";
import { ReactComponent as Signup } from "../../../assets/svgs/SignupIcon.svg";
import { ReactComponent as ScheduleappointmentIcon } from "../../../assets/svgs/ScheduleappointmentIcon.svg";
import { ReactComponent as VirtualHomeIcon } from "../../../assets/svgs/VirtualHomeIcon.svg";
import { ReactComponent as FindahealthcareIcon } from "../../../assets/svgs/FindahealthcareIcon.svg";
import { useTranslation } from "react-i18next";

const HowItWorksComponent = () => {
  const { t } = useTranslation();
  return (
    <section className="py-8 sm:py-12 md:py-16 lg:py-20 bg-[#FAFAFA]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid gap-8 md:grid-cols-2 items-start">
          <div className="text-center md:text-left">
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-[55px] font-bold font-cormorant-garamond mb-4 sm:mb-6">
              {t("how-does-it-work-heading")}
            </h2>
            <p className="text-base sm:text-lg md:text-xl lg:text-[24px] text-[#********] mb-6 sm:mb-8">
              {t("how-does-it-work-desc")}
            </p>
          </div>

          <div className="relative h-[400px] overflow-y-auto pb-4 space-y-6 sm:space-y-8 [-ms-overflow-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
            <div className="group flex flex-col sm:flex-row items-start gap-4 sm:gap-6 w-full p-4 text-[#********] bg-[#ffffff]  hover:bg-[#0052FD] hover:text-[#ffffff] rounded-xl transition-colors duration-300">
              <div className="flex-1 ">
                <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-[32px] font-medium mb-2 sm:mb-3 font-cormorant-garamond text-[#000000] group-hover:text-[#ffffff]">
                  {t("signup-and-create-account-heading")}
                </h3>
                <p className="text-sm sm:text-base md:text-lg lg:text-[24px] group-hover:text-[#ffffff]">
                  {t("signup-and-create-account-desc")}
                </p>
              </div>
              <div className="w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 flex items-center justify-center rounded-xl bg-[#0052FD08] group-hover:bg-white">
                <Signup className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0 object-contain group-hover:text-white" />
              </div>
            </div>

            <div className="group flex flex-col sm:flex-row items-start gap-4 sm:gap-6 w-full p-4 text-[#********] bg-[#ffffff]  hover:bg-[#0052FD] hover:text-[#ffffff] rounded-xl transition-colors duration-300">
              <div className="flex-1">
                <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-[32px] font-medium mb-2 sm:mb-3 font-cormorant-garamond text-[#000000] group-hover:text-[#ffffff]">
                  {t("complete-your-profile-heading")}
                </h3>
                <p className="text-sm sm:text-base md:text-lg lg:text-[24px] group-hover:text-[#ffffff]">
                  {t("complete-your-profile-desc")}
                </p>
              </div>
              <div className="w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 flex items-center justify-center rounded-xl bg-[#0052FD08] group-hover:bg-white">
                <Signup className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0 object-contain group-hover:text-white" />
              </div>
            </div>

            <div className="group flex flex-col sm:flex-row items-start gap-4 sm:gap-6 w-full p-4 text-[#********] bg-[#ffffff]  hover:bg-[#0052FD] hover:text-[#ffffff] rounded-xl transition-colors duration-300">
              <div className="flex-1">
                <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-[32px] font-medium mb-2 sm:mb-3 font-cormorant-garamond text-[#000000] group-hover:text-[#ffffff]">
                  {t("find-healthcare-professional-heading")}
                </h3>
                <p className="text-sm sm:text-base md:text-lg lg:text-[24px] group-hover:text-[#ffffff]">
                  {t("find-healthcare-professional-desc")}
                </p>
              </div>
              <div className="w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 flex items-center justify-center rounded-xl bg-[#0052FD08] group-hover:bg-white">
                <FindahealthcareIcon className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0 object-contain group-hover:text-white" />
              </div>
            </div>

            <div className="group flex flex-col sm:flex-row items-start gap-4 sm:gap-6 w-full p-4 text-[#********] bg-[#ffffff]  hover:bg-[#0052FD] hover:text-[#ffffff] rounded-xl transition-colors duration-300">
              <div className="flex-1">
                <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-[32px] font-medium mb-2 sm:mb-3 font-cormorant-garamond text-[#000000] group-hover:text-[#ffffff]">
                  {t("schedule-an-appointment-heading")}
                </h3>
                <p className="text-sm sm:text-base md:text-lg lg:text-[24px] group-hover:text-[#ffffff]">
                  {t("schedule-an-appointment-desc")}
                </p>
              </div>
              <div className="w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 flex items-center justify-center rounded-xl bg-[#0052FD08] group-hover:bg-white">
                <ScheduleappointmentIcon className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0 object-contain group-hover:text-white" />
              </div>
            </div>

            <div className="group flex flex-col sm:flex-row items-start gap-4 sm:gap-6 w-full p-4 text-[#********] bg-[#ffffff]  hover:bg-[#0052FD] hover:text-[#ffffff] rounded-xl transition-colors duration-300">
              <div className="flex-1">
                <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-[32px] font-medium mb-2 sm:mb-3 font-cormorant-garamond text-[#000000] group-hover:text-[#ffffff]">
                  {t("virtual-home-heading")}
                </h3>
                <p className="text-sm sm:text-base md:text-lg lg:text-[24px] group-hover:text-[#ffffff]">
                  {t("virtual-home-desc")}
                </p>
              </div>
              <div className="w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 flex items-center justify-center rounded-xl bg-[#0052FD08] group-hover:bg-white">
                <VirtualHomeIcon className="w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 flex-shrink-0 object-contain group-hover:text-white" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorksComponent;
