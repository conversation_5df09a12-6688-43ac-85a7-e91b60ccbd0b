import React from "react";
import { useTranslation } from "react-i18next";

const AboutUsHeaderComponent = () => {
  const { t, i18n } = useTranslation();
  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className="mb-3 sm:mb-4 text-primary font-bold text-lg sm:text-xl md:text-[24px]">
          {t(`about-us`)}
        </div>
        <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-[72px] font-bold tracking-tight mb-4 sm:mb-6 font-cormorant-garamond">
          {t(`elevate-your-health-journey-heading`)}
        </h1>
        <p className="mx-auto text-base sm:text-lg md:text-xl lg:text-[24px] text-[#00000099] mb-6 sm:mb-8 max-w-md sm:max-w-lg md:max-w-xl lg:max-w-2xl">
          {t(`elevate-your-health-journey-desc`)}
        </p>
        {/* <Button size="lg" className="rounded-full px-6 py-3 text-sm sm:text-base md:text-lg">
        Book Appointment
      </Button> */}
      </div>
    </section>
  );
};

export default AboutUsHeaderComponent;
