import React from "react";
import Dentist from "../../../assets/images/Dentist.png";
import Cardiologist from "../../../assets/images/Cardiologist.png";
import Pulmonogist from "../../../assets/images/Pulmonogist.png";

import ToothIcon from "../../../assets/images/ToothIcon.png";
import CardiologistIcon from "../../../assets/images/cardiologistIcon.png";
import PulmonologistIcon from "../../../assets/images/pulmonologistIcon.png";
import { useTranslation } from "react-i18next";

const ServicesComponent = () => {
  const { t } = useTranslation();

  return (
    <div className="bg-[#FAFAFA] py-8 px-4">
      <div className="mb-8 md:mb-12 text-center md:text-start px-8">
        <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-[55px] font-bold font-cormorant-garamond">
          {t("medicalServices")}
        </h2>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 justify-items-center w-full gap-6 sm:gap-8 mx-auto max-w-7xl">
        {[
          {
            topImage: Dentist,
            title: `${t("dentist")}`,
            icon: ToothIcon,
            description: `${t("dentist_desc")}`,
            subtitle: "For Your Dental Issues",
          },
          {
            topImage: Cardiologist,
            title: `${t("cardiologist")}`,
            icon: CardiologistIcon,
            description: `${t("cardiologist_desc")}`,
            subtitle: "For Your Heart Issues",
          },
          {
            topImage: Pulmonogist,
            title: `${t("pulmonologist")}`,
            icon: PulmonologistIcon,
            description: `${t("pulmonologist_desc")}`,
            subtitle: "For Your Respiratory Issues",
          },
        ].map((service, index) => (
          <div
            key={index}
            className="max-w-md" // Removed w-full
          >
            <div className="overflow-hidden h-full rounded-lg">
              <div className="relative">
                <img
                  alt={service.title}
                  src={service.topImage}
                  className="w-full h-48 sm:h-64 md:h-72 lg:h-[383px] object-cover"
                />
                <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-1/2 z-10">
                  <div className="flex h-16 w-16 sm:h-20 sm:w-20 md:h-24 md:w-24 items-center justify-center rounded-full bg-[#0052FD] border-4 sm:border-[6px] border-white">
                    <img
                      src={service.icon}
                      alt={`${service.title} icon`}
                      className="h-8 w-8 sm:h-10 sm:w-10 md:h-12 md:w-12 object-contain"
                    />
                  </div>
                </div>
              </div>

              <div className="p-4 sm:p-6 text-center mt-4 sm:mt-6">
                <h3 className="mb-2 text-xl sm:text-2xl md:text-3xl lg:text-[32px] font-bold font-cormorant-garamond">
                  {service.title}
                </h3>
                <p className="mb-4 sm:mb-6 text-sm sm:text-base lg:text-[16px] text-[#00000099]">
                  {service.description}
                </p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ServicesComponent;
