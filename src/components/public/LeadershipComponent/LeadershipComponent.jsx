import React from "react";
import EzechielFenelon from "../../../assets/images/EzechielFenelon.png";
import StephannDubois from "../../../assets/images/StephannDubois.png";
import YvesNithder from "../../../assets/images/YvesNithder.png";
import Sheila<PERSON><PERSON><PERSON> from "../../../assets/images/SheilaLaplanche.png";
import AlishaOutridge from "../../../assets/images/AlishaOutridge.png";
import LuisRoyeroMeneses from "../../../assets/images/LuisRoyeroMeneses.png";
import { useTranslation } from "react-i18next";

const LeadershipComponent = () => {
  const { t } = useTranslation();
  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-24">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-8 sm:mb-10 md:mb-12">
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-[64px] font-cormorant-garamond font-bold mb-3 sm:mb-4 md:mb-6">
            {t(`our-leadership-heading`)}
          </h2>
          <p className="text-base sm:text-lg md:text-xl lg:text-[24px] text-[#00000099] mx-auto max-w-2xl lg:max-w-3xl">
            {t(`our-leadership-desc`)}
          </p>
        </div>
        <div className="grid gap-6 sm:gap-8 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5">
          {[
            {
              image: EzechielFenelon,
              name: "Ezechiel Fenelon",
              title: "CEO",
              href: "https://www.linkedin.com/in/eze-fenelon-48a85a13/",
            },
            {
              image: LuisRoyeroMeneses,
              name: "Luis Royero Meneses",
              title: "CMO",
              href: "https://www.linkedin.com/in/luis-royero-meneses/",
            },
            {
              image: YvesNithder,
              name: "Yves-Nithder PIERRE",
              title: "CFO",
              href: "https://www.linkedin.com/in/yves-nithder-pierre-35480666/",
            },
            {
              image: AlishaOutridge,
              name: "Alisha Outridge",
              title: "CTO",
              href: "https://www.linkedin.com/in/alishaoutridge/",
            },
            {
              image: SheilaLaplanche,
              name: "Sheila Laplanche",
              title: "CXO",
              href: "https://www.linkedin.com/in/sheilalaplanche/",
            },
          ].map((leader, index) => (
            <div key={index} className="flex flex-col items-center group">
              <a
                href={leader.href}
                target="_blank"
                rel="noopener noreferrer"
                className="flex flex-col items-center"
              >
                <div className="relative h-[180px] w-[180px] sm:h-[200px] sm:w-[200px] md:h-[150px] md:w-[150px] mb-4">
                  <div className="absolute inset-0 rounded-full overflow-hidden border-2 border-[#0052FD] group-hover:border-[#0052FD] transition-colors duration-300">
                    <img
                      src={leader.image}
                      alt={leader.name}
                      className="h-full w-full object-cover rounded-full transition-transform duration-300 group-hover:scale-105"
                      loading="lazy"
                    />
                  </div>
                </div>

                <div className="text-center">
                  <h3 className="font-bold text-lg sm:text-xl md:text-[20px] text-[#1E1E1E] mb-1 group-hover:text-[#0052FD] transition-colors duration-300">
                    {leader.name}
                  </h3>
                  <p className="font-medium text-base sm:text-lg md:text-xl text-[#1E1E1EB2] group-hover:text-[#0052FD]">
                    {leader.title}
                  </p>
                </div>
              </a>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default LeadershipComponent;
