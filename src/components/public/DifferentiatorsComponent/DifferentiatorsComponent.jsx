import React from "react";

import Affordable from "../../../assets/images/Affordable.png";
import PatientFocused from "../../../assets/images/PatientFocused.png";
import Comprehensive from "../../../assets/images/Comprehensive.png";
import Sustaining from "../../../assets/images/Sustaining.png";

import HybridIcon from "../../../assets/images/HybridIcon.png";
import IntegratedMarketIcon from "../../../assets/images/IntegratedMarketIcon.png";
import ProximityDrivenIcon from "../../../assets/images/ProximityDrivenIcon.png";
import UnmatchedScalabilityIcon from "../../../assets/images/UnmatchedScalabilityIcon.png";

import { useTranslation } from "react-i18next";

const DifferentiatorsComponent = () => {
  const { t } = useTranslation();
  return (
    <section className="py-8 sm:py-12 md:py-16">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid gap-8 lg:gap-12 grid-cols-1 md:grid-cols-2 md:items-center">
          <div className="text-center md:text-left">
            <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-[55px] font-bold mb-4 sm:mb-6 font-cormorant-garamond">
              {/* Here's what makes us different from others */}
              {t("what_makes_us_different")}
            </h2>
            <p className="mb-6 sm:mb-8 text-base sm:text-lg md:text-xl lg:text-[24px] text-[#00000099]">
              {t("what_makes_us_different_desc")}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 md:gap-8 justify-center md:justify-start">
              <div className="text-center md:text-left">
                <h3 className="text-4xl sm:text-5xl md:text-6xl font-bold text-primary mb-1 sm:mb-2">
                  {t("patient_serve_no")}
                </h3>
                <p className="text-xs sm:text-sm md:text-[14px] text-muted-foreground">
                  {t("patient_serve_text")}
                </p>
              </div>
              <div className="text-center md:text-left">
                <h3 className="text-4xl sm:text-5xl md:text-6xl font-bold text-primary mb-1 sm:mb-2">
                  1000+
                </h3>
                <p className="text-xs sm:text-sm md:text-[14px] text-muted-foreground">
                  Medical tests conducted
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 bg-[#FAFAFA] p-4 sm:p-6 rounded-xl">
            {[
              {
                icon: HybridIcon,
                title: `${t("hybrid-patient-centric-model-heading")}`,
                description: `${t("hybrid-patient-centric-model-desc")}`,
              },
              {
                icon: IntegratedMarketIcon,
                title: `${t("integrated-marketplace-for-holistic-care-heading")}`,
                description: `${t("integrated-marketplace-for-holistic-care-desc")}`,
              },
              {
                icon: ProximityDrivenIcon,
                title: `${t("proximity-driven-heading")}`,
                description: `${t("proximity-driven-desc")}`,
              },
              {
                icon: UnmatchedScalabilityIcon,
                title: `${t("unmatched-scalability-heading")}`,
                description: `${t("unmatched-scalability-desc")}`,
              },
            ].map((feature, index) => (
              <div
                key={index}
                className="flex flex-col sm:flex-row items-center sm:items-start text-center sm:text-left my-3 sm:my-4 gap-4 sm:gap-6 md:gap-8"
              >
                <div className="h-10 w-10 sm:h-12 sm:w-12 flex-shrink-0 rounded-lg flex items-center justify-center">
                  <img
                    src={feature.icon}
                    alt={`${feature.title} icon`}
                    className="h-16 w-16 sm:h-16 sm:w-16 md:h-24 md:w-24 object-contain"
                  />
                </div>
                <div>
                  <h3 className="text-xl sm:text-2xl md:text-[28px] font-semibold mb-1 font-cormorant-garamond">
                    {feature.title}
                  </h3>
                  <p className="text-sm sm:text-base md:text-[18px] text-[#00000099]">
                    {feature.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default DifferentiatorsComponent;
