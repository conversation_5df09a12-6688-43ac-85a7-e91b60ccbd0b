import React from "react";
import { Card, CardContent, CardHeader } from "../../ui/card";
import Post1 from "../../../assets/images/Post1.png";
import Post2 from "../../../assets/images/Post2.png";
import { useTranslation } from "react-i18next";

const LatestPosts = () => {
  const { t } = useTranslation();

  return (
    <section className="py-8 sm:py-12 md:py-16 lg:py-20 ">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-[55px] font-bold mb-6 sm:mb-8 md:mb-12 font-cormorant-garamond text-center">
          {t(`latest-posts-heading`)}
        </h2>
        <div className="flex items-center justify-center w-full">
          <div className="grid gap-6 sm:gap-8 grid-cols-1 md:grid-cols-2 max-w-4xl lg:max-w-5xl">
            <Card className="">
              <div className="aspect-video overflow-hidden">
                <img
                  src={Post1}
                  alt="Medical supplies"
                  className="h-full w-full object-cover transition-transform hover:scale-105 duration-300"
                  loading="lazy"
                />
              </div>
              <CardContent className="p-4 sm:p-6">
                <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-[36px] font-bold mb-2 sm:mb-3 font-cormorant-garamond">
                  {t(`latest-posts-article-1-heading`)}
                </h3>
                <p className="text-sm sm:text-base md:text-lg lg:text-[20px] text-[#00000099] mb-3 sm:mb-4">
                  {t(`latest-posts-article-1-desc`)}
                </p>
                {/* <Link to="#" className="text-primary text-xs sm:text-sm font-medium">
                Read full article
              </Link> */}
              </CardContent>
            </Card>

            <Card className="">
              <div className="aspect-video overflow-hidden">
                <img
                  src={Post2}
                  alt="Doctor with mask"
                  className="h-full w-full object-cover transition-transform hover:scale-105 duration-300"
                  loading="lazy"
                />
              </div>
              <CardContent className="p-4 sm:p-6">
                <h3 className="text-xl sm:text-2xl md:text-3xl lg:text-[36px] font-bold mb-2 sm:mb-3 font-cormorant-garamond">
                  {t(`latest-posts-article-2-heading`)}
                </h3>
                <p className="text-sm sm:text-base md:text-lg lg:text-[20px] text-[#00000099] mb-3 sm:mb-4">
                  {t(`latest-posts-article-2-desc`)}
                </p>
                {/* <Link to="#" className="text-primary text-xs sm:text-sm font-medium">
                Read full article
              </Link> */}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default LatestPosts;
