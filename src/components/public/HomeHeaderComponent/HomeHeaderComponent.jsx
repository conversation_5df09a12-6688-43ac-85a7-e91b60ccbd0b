import React from "react";
import HeaderImage from "../../../assets/images/HeaderImg.png";
import HeaderVideo from "../../../assets/videos/HeaderBgVideo.mp4";
import { useTranslation } from "react-i18next";

const HomeHeaderComponent = () => {
  const { t } = useTranslation();

  return (
    <section className="relative overflow-hidden bg-black min-h-[600px]">
      {/* Video background */}
      <video
        autoPlay
        loop
        muted
        playsInline
        className="absolute inset-0 w-full h-full object-cover opacity-50"
      >
        <source src={HeaderVideo} type="video/mp4" />
        {/* Fallback image in case video doesn't load */}
        <img src={HeaderImage} alt="Fallback header background" />
      </video>

      {/* Overlay */}
      <div className="absolute inset-0 bg-black/20" />

      {/* Content - Updated container for proper centering */}
      <div className="absolute inset-0 flex items-center justify-center z-10">
        <div className="container mx-auto px-8 text-center text-white md:px-6">
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl md:text-[48px] font-cormorant-garamond max-w-4xl mx-auto">
            {t("missionText")}
          </h1>
        </div>
      </div>
    </section>
  );
};

export default HomeHeaderComponent;
