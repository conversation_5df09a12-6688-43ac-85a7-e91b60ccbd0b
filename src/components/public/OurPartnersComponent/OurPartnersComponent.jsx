import React from "react";
import Partner1 from "../../../assets/images/Partner1.png";
import Partner2 from "../../../assets/images/Partner2.png";
import Partner3 from "../../../assets/images/Partner3.png";
import MIT<PERSON>ogo from "../../../assets/images/MITLogo.png";

import { useTranslation } from "react-i18next";

const OurPartnersComponent = () => {
  const { t } = useTranslation();

  const partners = [
    { src: Partner1, alt: "Partner 1" },
    { src: Partner2, alt: "Partner 2" },
    { src: Partner3, alt: "Partner 3" },
    { src: MITLogo, alt: "Partner 4" },
    { src: Partner1, alt: "Partner 1" },
    { src: Partner2, alt: "Partner 2" },
    { src: Partner3, alt: "Partner 3" },
    { src: MITLogo, alt: "Partner 4" },
  ];

  return (
    <section className="py-8 sm:py-12 md:py-16 overflow-hidden">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-[55px] font-bold text-center mb-6 sm:mb-8 md:mb-12 font-cormorant-garamond">
          {t("our-partners")}
        </h2>

        <div className="relative w-full">
          <div className="animate-marquee whitespace-nowrap ">
            {partners.map((partner, index) => (
              <div
                key={index}
                className="inline-flex items-center justify-center mx-4 sm:mx-6 md:mx-8 lg:mx-12 w-32 sm:w-40 md:w-48 lg:w-56"
              >
                <img
                  src={partner.src}
                  alt={partner.alt}
                  className="h-12 sm:h-14 md:h-16 lg:h-20 object-contain transition-transform hover:scale-105"
                  loading="lazy"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes marquee {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }
        .animate-marquee {
          animation: marquee 20s linear infinite;
          display: inline-block;
        }
      `}</style>
    </section>
  );
};

export default OurPartnersComponent;
