import React from "react";
import { Card, CardContent } from "../../../components/ui/card";
import Affordable from "../../../assets/images/Affordable.png";
import { useTranslation } from "react-i18next";

const AboutUsOurValuesComponent = () => {
  const { t, i18n } = useTranslation();
  return (
    <section className="bg-[#F4F6F9] p-8 my-16">
      <div className="container mx-auto px-4 md:px-6">
        <h2 className="text-[48px] font-cormorant-garamond font-bold text-center mb-12">
          {t(`our-values-heading`)}
        </h2>
        <div className="grid gap-8 md:grid-cols-2">
          {[
            {
              icon: Affordable,
              title: `${t("your-wellness-heading")}`,
              description: `${t("your-wellness-desc")}`,
            },
            {
              icon: Affordable,
              title: `${t("your-on-demand-health-heading")}`,
              description: `${t("your-on-demand-health-desc")}`,
            },
            {
              icon: Affordable,
              title: `${t("your-trusted-partners-heading")}`,
              description: `${t("your-trusted-partners-desc")}`,
            },
            {
              icon: Affordable,
              title: `${t("experience-efficiency-credibility-heading")}`,
              description: `${t("experience-efficiency-credibility-desc")}`,
            },
          ].map((value, index) => (
            <Card key={index} className="overflow-hidden">
              <CardContent className="p-6 flex gap-4">
                <div className="flex-shrink-0">
                  <div className="h-[48px] w-[48px] rounded-lg flex items-center justify-center ">
                    <img
                      src={value.icon}
                      alt={`${value.title} icon`}
                      className="h-20 w-20 object-contain"
                    />
                  </div>
                </div>
                <div>
                  <h3 className="text-[28px] font-cormorant-garamond font-bold mb-2">
                    {value.title}
                  </h3>
                  <p className="text-[18px] text-[#00000099]">
                    {value.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="mt-12 text-center">
          {/* <Button>Book Appointment</Button> */}
        </div>
      </div>
    </section>
  );
};

export default AboutUsOurValuesComponent;
