import React from "react";
import WhoWeAre from "../../../assets/images/WhoWeAre.png";
import WhoWeAreBgVideo from "../../../assets/videos/WhoWeAreBgVideo.mp4";
import { useTranslation } from "react-i18next";

const AboutUsWhoWeAreComponent = () => {
  const { t, i18n } = useTranslation();
  return (
    <section className="">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-[64px] font-bold mb-6 sm:mb-8 font-cormorant-garamond">
          {t(`who-we-are-heading`)}
        </h2>
        <p className="mx-auto text-lg sm:text-xl md:text-2xl lg:text-[24px] text-[#00000090] mb-8 sm:mb-10 md:mb-12 max-w-md sm:max-w-lg md:max-w-xl lg:max-w-2xl">
          {t(`who-we-are-desc`)}
        </p>

        <div className="relative mx-auto max-w-4xl lg:max-w-6xl">
          {/* Replaced img with video element */}
          <video
            autoPlay
            loop
            muted
            playsInline
            className="rounded-lg shadow-lg w-full h-auto object-cover"
          >
            <source src={WhoWeAreBgVideo} type="video/mp4" />
            {/* Fallback image if video doesn't load */}
            <img
              src={WhoWeAre}
              alt="Healthcare Team"
              className="rounded-lg shadow-lg w-full h-auto"
              loading="lazy"
            />
          </video>

          <div className="absolute left-2 sm:left-4 md:left-6 lg:left-10 top-2 sm:top-4 md:top-6 lg:top-10 bg-[#E3E7E990] p-3 sm:p-4 md:p-6 rounded-lg shadow-lg w-48 sm:w-56 md:w-64 lg:w-80">
            <div className="text-primary font-bold text-3xl sm:text-4xl md:text-5xl lg:text-[72px] mb-1 sm:mb-2">
              {t(`patient_serve_no`)}
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground mb-3 sm:mb-4 md:mb-6">
              {t(`patient_serve_text`)}
            </div>
            <div className="text-primary font-bold text-3xl sm:text-4xl md:text-5xl lg:text-[72px] mb-1 sm:mb-2">
              {t(`medical_text_no`)}
            </div>
            <div className="text-xs sm:text-sm text-muted-foreground">
              {t(`medical_text_text`)}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutUsWhoWeAreComponent;
