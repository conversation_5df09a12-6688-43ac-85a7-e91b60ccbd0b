import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Textarea } from "../../components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import { Loader2 } from "lucide-react";
import { useToast } from "../../hooks/use-toast";
import SignPlaceholder from "../../assets/svgs/SignPlaceholder.svg";
import {
  createImagingForm_apiCalls,
  updateImagingForm_apiCalls,
  getImagingFormByAppointmentId_apiCalls,
} from "../../api/api_calls/appointmentforms/imagingForm_apiCalls";

// Define the form schema with Zod (Backend DTO compliant)
const formSchema = z.object({
  id: z.string().optional(),
  appointmentId: z.string().min(1, {
    message: "Valid appointment ID is required",
  }),
  servicesRequested: z
    .string()
    .min(1, { message: "Services requested are required" }),
  specialInstructions: z.string().optional(),
  diagnosis: z
    .string()
    .min(1, { message: "Diagnosis/Indications are required" }),
});

const Imaging = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [imagingData, setImagingData] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const location = useLocation();
  const { toast } = useToast();

  // Get appointmentId from location state
  const appointmentId = location.state?.appointmentId || location.state?.appointment?.id;

  console.log("🔍 Imaging Component - appointmentId:", appointmentId);
  console.log("🔍 Imaging Component - location.state:", location.state);

  // Initialize form with React Hook Form and Zod validation
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: "",
      appointmentId: appointmentId || "",
      servicesRequested: "",
      specialInstructions: "",
      diagnosis: "",
    },
  });

  // Update form appointmentId when it changes
  useEffect(() => {
    if (appointmentId) {
      form.setValue("appointmentId", appointmentId);
      console.log("📝 Updated form appointmentId:", appointmentId);
    }
  }, [appointmentId, form]);

  // Fetch existing imaging form data
  useEffect(() => {
    const fetchImagingForm = async () => {
      if (!appointmentId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await getImagingFormByAppointmentId_apiCalls({ appointmentId });

        if (response?.data) {
          setImagingData(response.data);
          setIsEditMode(true);

          // Populate form with existing data
          form.reset({
            id: response.data.id || "",
            appointmentId: response.data.appointmentId || appointmentId,
            servicesRequested: response.data.servicesRequested || "",
            specialInstructions: response.data.specialInstructions || "",
            diagnosis: response.data.diagnosis || "",
          });
        }
      } catch (error) {
        console.error("Failed to fetch imaging form:", error);
        // If no form exists, we'll create a new one
        setIsEditMode(false);
      } finally {
        setLoading(false);
      }
    };

    fetchImagingForm();
  }, [appointmentId, form]);

  // Handle form submission
  const onSubmit = async (data) => {
    console.log("🚀 Imaging Form Submission Started");
    console.log("📝 Form data received:", data);
    console.log('🆔 appointmentId imaging: ',appointmentId);
    console.log("🔄 isEditMode:", isEditMode);
    console.log("📋 imagingData:", imagingData);

    if (!appointmentId) {
      console.error("❌ No appointmentId provided");
      toast({
        title: "Error",
        description: "No appointment ID provided",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const formData = {
        ...data,
        appointmentId,
      };

      console.log("📦 Final form data to submit:", formData);

      let response;
      if (isEditMode && (imagingData?.id || data.id)) {
        // Update existing imaging form
        response = await updateImagingForm_apiCalls({
          ...formData,
          id: data.id || imagingData.id,
        });
        toast({
          title: "Imaging form updated",
          description: "The imaging form has been successfully updated.",
          variant: "success",
        });
      } else {
        // Create new imaging form (remove id from formData for creation)
        const { id, ...createData } = formData;
        response = await createImagingForm_apiCalls(createData);
        toast({
          title: "Imaging form created",
          description: "The imaging form has been successfully created.",
          variant: "success",
        });
        setIsEditMode(true);
        setImagingData(response.data);
      }

      console.log("Imaging form operation successful:", response);
    } catch (error) {
      console.error("Error submitting imaging form:", error);

      toast({
        title: "Submission failed",
        description: `There was an error ${isEditMode ? 'updating' : 'creating'} the imaging form. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading imaging form...</span>
      </div>
    );
  }

  return (
    <div className="w-full">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* Services and Instructions */}
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="space-y-6">
              <FormField
                control={form.control}
                name="servicesRequested"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      Services Requested <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        className="w-full"
                        placeholder="Please specify the imaging services needed (e.g., X-ray, MRI, CT scan, etc.)"
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="specialInstructions"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      Special Instructions
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        className="w-full"
                        placeholder="Any special instructions for the imaging procedure (e.g., contrast, positioning, etc.)"
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="diagnosis"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      Diagnosis/Indications{" "}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        className="w-full"
                        placeholder="Enter the diagnosis or clinical indications for this imaging request"
                        rows={3}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Signature and Date */}
          <div className="flex items-center justify-between flex-row">
            <div className="">
              <p className="text-xl font-bold text-[#1E1E1E] mb-2">Signature</p>
              <div className="font-script text-lg">
                <img alt="" src={SignPlaceholder} />
              </div>
            </div>

            <div>
              <p className="font-[600] text-[16px]">Expire Date</p>
              <p className="font-[600] text-[16px]">1/08/2024</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              className="bg-gray-200 text-gray-700"
              onClick={() => form.reset()}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-blue-600 text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditMode ? "Updating..." : "Creating..."}
                </>
              ) : (
                isEditMode ? "Update Imaging Form" : "Create Imaging Form"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default Imaging;
