import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { RadioGroup, RadioGroupItem } from "../../components/ui/radio-group-2";
import { Input } from "../../components/ui/input";
import { Button } from "../../components/ui/button";
import { Textarea } from "../../components/ui/textarea";
import { useToast } from "../../hooks/use-toast";
import { Loader2, CalendarIcon } from "lucide-react";
import { Calendar } from "../../components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../components/ui/popover";
import { format } from "date-fns";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../../components/ui/accordion-availability";
import {
  createRosForm_apiCalls,
  updateRosForm_apiCalls,
  getRosFormByAppointmentId_apiCalls,
} from "../../api/api_calls/appointmentforms/rosForm_apiCalls";
import { fieldConfigs, rosFormSchema } from "../../schemas/rosFieldConfigs";



const ROS = ({ appointmentId }) => {
  const location = useLocation();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [existingRosForm, setExistingRosForm] = useState(null);
  const { toast } = useToast();

  // Get appointmentId from props or location state as fallback
  const finalAppointmentId =
    appointmentId ||
    location.state?.appointmentId ||
    location.state?.appointment?.id;

  // Helper function to format date for form (convert ISO to YYYY-MM-DD)
  const formatDateForForm = (isoDate) => {
    if (!isoDate) return null;
    try {
      const date = new Date(isoDate);
      if (isNaN(date.getTime())) return null;
      return date.toISOString().split("T")[0];
    } catch (error) {
      console.error("Error formatting date for form:", error);
      return null;
    }
  };

  const form = useForm({
    resolver: zodResolver(rosFormSchema),
    defaultValues: {
      appointmentId: finalAppointmentId || "",

      // GENERAL
      recentWeightChange: null,
      weakness: null,
      fatigue: null,
      fever: null,

      // SKIN
      skinRashes: null,
      skinLumps: null,
      skinSores: null,
      skinItching: null,
      skinDryness: null,
      skinChangeInColor: null,
      skinChangeInHair: null,
      skinChangeInMoles: null,

      // HEENT - Head
      headache: null,
      headInjury: null,
      dizziness: null,
      lightheadedness: null,

      // HEENT - Eyes
      visionChange: null,
      glassesOrContactLenses: null,
      eyePain: null,
      eyeRedness: null,
      excessiveTearing: null,
      doubleOrBlurredVision: null,
      spots: null,
      specks: null,
      flashingLights: null,
      glaucoma: null,
      cataracts: null,

      // HEENT - Ears
      hearingLoss: null,
      tinnitus: null,
      vertigo: null,
      earaches: null,
      earInfection: null,
      earDischarge: null,
      hearingDecreased: null,
      hearingAidsUse: null,

      // HEENT - Nose and Sinuses
      nasalDischarge: null,
      nasalItching: null,
      frequentColds: null,
      hayfever: null,
      nasalStuffiness: null,
      nosebleeds: null,
      sinusPressurePain: null,

      // HEENT - Throat
      dentalCondition: null,
      gumProblemsBleeding: null,
      dentures: null,
      denturesFit: null,
      lastDentalExam: null,
      soreTongue: null,
      dryMouth: null,
      frequentSoreThroats: null,
      hoarseness: null,

      // NECK
      swollenGlands: null,
      thyroidProblems: null,
      goiter: null,
      neckLumps: null,
      neckPainStiffness: null,

      // BREASTS
      breastLumps: null,
      breastPainDiscomfort: null,
      nippleDischarge: null,
      selfExamPractices: null,

      // RESPIRATORY
      cough: null,
      sputum: null,
      sputumColor: null,
      sputumQuantity: null,
      sputumBlood: null,
      shortnessOfBreath_respiratory: null,
      wheezing: null,
      pleuriticPain: null,
      lastChestXray: null,
      asthma: null,
      bronchitis: null,
      emphysema: null,
      pneumonia: null,
      tuberculosis: null,

      // CARDIOVASCULAR
      heartTrouble: null,
      highBloodPressure: null,
      rheumaticFever: null,
      heartMurmurs: null,
      chestPain: null,
      palpitations: null,
      shortnessOfBreath_cardio: null,
      orthopnea: null,
      paroxysmalNocturnalDyspnea: null,
      edema: null,
      ekgOther: null,

      // GASTROINTESTINAL
      unintentionalWeightChange: null,
      troubleSwallowing: null,
      heartburn: null,
      appetite: null,
      specialDiet: null,
      nausea: null,
      stoolColor: null,
      stoolSize: null,
      changeInBowelHabits: null,
      painWithDefecation: null,
      rectalBleeding: null,
      blackOrTarryStools: null,
      hemorrhoids: null,
      constipation: null,
      diarrhea: null,
      abdominalPain: null,
      foodIntolerance: null,
      excessiveBelching: null,
      jaundice: null,
      liverGallbladderTrouble: null,
      hepatitis: null,

      // PERIPHERAL VASCULAR
      claudication: null,
      legCramps: null,
      varicoseVeins: null,
      pastClots: null,
      calfLegFeetSwelling: null,
      fingertipColorChangeCold: null,
      swellingRednessTendernessPeripheral: null,

      // URINARY
      frequencyOfUrination: null,
      polyuria: null,
      nocturia: null,
      urgency: null,
      burningPainDuringUrination: null,
      hematuria: null,
      urinaryInfections: null,
      kidneyOrFlankPain: null,
      kidneyStones: null,
      ureteralColic: null,
      suprapubicPain: null,
      incontinence: null,
      reducedUrinaryCaliberOrForce: null,
      hesitancy: null,
      dribbling: null,

      // GENITAL - MALE
      hernias: null,
      penileDischarge: null,
      testicularPainOrMasses: null,
      scrotalPainOrSwelling: null,

      // STD History
      stdHistory: null,
      stdTreatment: null,

      // Sexual Habits
      sexualInterest: null,
      sexualFunction: null,
      sexualSatisfaction: null,
      birthControlMethodUse: null,
      condomUse: null,
      sexualProblems: null,
      hivInfectionConcerns: null,

      // GENITAL - FEMALE
      ageAtMenarche: null,
      periodFrequency: null,
      durationOfPeriods: null,
      amountOfBleeding: null,
      periodRegularity: null,
      bleedingBetweenPeriods: null,
      bleedingAfterIntercourse: null,
      lastMenstrualPeriod: null,
      dysmenorrhea: null,
      premenstrualTension: null,
      ageAtMenopause: null,
      menopausalSymptoms: null,
      postMenopausalBleeding: null,
      vaginalDischarge: null,
      itching_female: null,
      sores_female: null,
      lumps_female: null,
      historyOfSexuallyTransmittedInfections_female: null,
      treatmentOfSexuallyTransmittedInfections_female: null,
      numberOfPregnancies: null,
      numberOfDeliveries: null,
      typeOfDeliveries: null,
      numberOfAbortions: null,
      birthControlMethods: null,
      complicationsOfPregnancy: null,

      // SEXUAL HABITS (FEMALE-SPECIFIC)
      sexualInterest_female: null,
      sexualFunction_female: null,
      sexualSatisfaction_female: null,
      anyProblemsIncludingDyspareunia_female: null,
      concernsAboutHivInfection_female: null,

      // MUSCULOSKELETAL
      muscleOrJointPain: null,
      stiffness: null,
      arthritis: null,
      gout: null,
      backache: null,
      muscleLocationDescription: null,
      swelling_muscle: null,
      redness_muscle: null,
      painMusculoskeletal: null,
      tenderness_muscle: null,
      weaknessMusculoskeletal: null,
      numbnessInLimb: null,
      limitationOfMotionOrActivity: null,
      timingMorning: null,
      timingEvening: null,
      duration: null,
      historyOfTrauma: null,
      neckOrLowBackPain: null,
      systemicJointPain: null,

      // PSYCHIATRIC
      nervousness: null,
      tension: null,
      feelingDownSadDepressed: null,
      memoryChange: null,
      suicidePlansOrAttempts: null,
      suicidalThoughts: null,
      pastCounseling: null,
      psychiatricAdmissions: null,
      onPsychiatricMedications: null,

      // NEUROLOGIC
      changeInMoodNeurologic: null,
      changeInAttention: null,
      changeInSpeech: null,
      changeInOrientation: null,
      changeInMemoryNeurologic: null,
      changeInInsight: null,
      changeInJudgement: null,
      headacheNeurologic: null,
      dizzinessNeurologic: null,
      vertigoNeurologic: null,
      fainting: null,
      blackouts: null,
      weaknessNeurologic: null,
      paralysis: null,
      numbnessOrLossOfSensation: null,
      tinglingPinsAndNeedles: null,
      tremorsOrOtherInvoluntaryMovement: null,
      seizures: null,

      // HEMATOLOGIC
      anemia: null,
      easyBruisingOrBleeding: null,
      pastTransfusions: null,
      transfusionReactions: null,

      // ENDOCRINE
      thyroidTrouble: null,
      heatOrColdIntolerance: null,
      excessiveSweating: null,
      excessiveThirstOrHunger: null,
      polyuriaEndocrine: null,
      changeInGloveOrShoeSize: null,
    },
  });

  // Update form appointmentId when it changes (only if form is not already populated)
  useEffect(() => {
    if (finalAppointmentId && !existingRosForm) {
      form.setValue("appointmentId", finalAppointmentId);
      console.log("📝 Updated form appointmentId:", finalAppointmentId);
    }
  }, [finalAppointmentId, form, existingRosForm]);

  // Fetch existing ROS form data
  useEffect(() => {
    const fetchRosForm = async () => {
      if (!finalAppointmentId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log(
          "🔍 Fetching ROS form for appointmentId:",
          finalAppointmentId,
        );

        const response = await getRosFormByAppointmentId_apiCalls({
          appointmentId: finalAppointmentId,
        });

        console.log("📋 ROS form API response:", response);

        if (response?.data) {
          setExistingRosForm(response.data);
          console.log("✅ ROS form data loaded:", response.data);

          // Populate form with existing data - reset form with all values
          const formData = { ...response.data };

          // Remove fields that shouldn't be in the form
          delete formData.id;
          delete formData.createdAt;
          delete formData.updatedAt;

          // Ensure appointmentId is set
          formData.appointmentId = finalAppointmentId;

          // Format date fields for form compatibility
          if (formData.lastDentalExam) {
            formData.lastDentalExam = formatDateForForm(formData.lastDentalExam);
          }
          if (formData.lastChestXray) {
            formData.lastChestXray = formatDateForForm(formData.lastChestXray);
          }
          if (formData.lastMenstrualPeriod) {
            formData.lastMenstrualPeriod = formatDateForForm(formData.lastMenstrualPeriod);
          }

          // Reset form with all the data at once
          form.reset(formData);

          console.log("📝 Form populated with existing data:", formData);
          // console.log("📝 Sample skin field values in form data:", {
          //   skinRashes: formData,
          //   skinItching: formData.skinItching,
          //   skinDryness: formData.skinDryness,
          //   recentWeightChange: formData.recentWeightChange
          // });

          // Verify form values after reset
          setTimeout(() => {
            console.log("📝 Form values after reset:", {
              skinRashes: form.getValues("skinRashes"),
              skinItching: form.getValues("skinItching"),
              skinDryness: form.getValues("skinDryness"),
              recentWeightChange: form.getValues("recentWeightChange")
            });
          }, 100);
        } else {
          console.log("ℹ️ No existing ROS form found for this appointment");
          // Reset form with default values including appointmentId
          form.reset({
            appointmentId: finalAppointmentId,
            // All other fields will use their default values from the form definition
          });
        }
      } catch (error) {
        console.error("❌ Failed to fetch ROS form:", error);
        // Don't show error toast for missing form, it's expected for new forms
      } finally {
        setLoading(false);
      }
    };

    fetchRosForm();
  }, [finalAppointmentId, form]);

  // Form submission handler
  const onSubmit = async (data) => {
    console.log('ros data :',data)
    if (!finalAppointmentId) {
      toast({
        title: "Error",
        description: "No appointment ID provided",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      console.log("🚀 Submitting ROS form data:", data);

      let response;
      if (existingRosForm?.id) {
        // Update existing form
        response = await updateRosForm_apiCalls({
          ...data,
          id: existingRosForm.id,
        });
        console.log("✅ ROS form updated successfully:", response);
      } else {
        // Create new form
        response = await createRosForm_apiCalls(data);
        console.log("✅ ROS form created successfully:", response);
      }

      toast({
        title: "Success",
        description: `Review of Systems form has been ${existingRosForm?.id ? "updated" : "created"} successfully.`,
        variant: "default",
      });

      // Update existing form state if it was a create operation
      if (!existingRosForm?.id && response?.data) {
        setExistingRosForm(response.data);
      }
    } catch (error) {
      console.error("❌ Error submitting ROS form:", error);
      toast({
        title: "Error",
        description:
          "There was a problem submitting the form. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderRadioField = (name, label) => {
    return (
      <div className="w-full grid grid-cols-4 gap-4 items-center">
        <FormField
          control={form.control}
          name={name}
          render={({ field }) => (
            <>
              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormLabel
                  id={`${name}-label`}
                  className="text-[16px] font-[600]"
                  title="Click the same option again to unselect"
                >
                  {label}
                </FormLabel>
              </FormItem>

              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormControl>
                  <RadioGroup
                    onValueChange={(value) => {
                      const newValue =
                        value === "yes" ? true : value === "no" ? false : null;
                      if (
                        (value === "yes" && field.value === true) ||
                        (value === "no" && field.value === false)
                      ) {
                        field.onChange(null);
                      } else {
                        field.onChange(newValue);
                      }
                    }}
                    value={
                      field.value === true
                        ? "yes"
                        : field.value === false
                          ? "no"
                          : ""
                    }
                    className="w-full grid grid-cols-1 md:grid-cols-2 gap-2"
                    aria-labelledby={`${name}-label`}
                  >
                    <FormItem className="col-span-1 flex items-center space-x-2 space-y-0 border border-[#E7E8E9] px-8 py-4 rounded-lg hover:bg-gray-50">
                      <FormControl>
                        <RadioGroupItem
                          value="yes"
                          id={`${name}-yes`}
                          onClick={() => {
                            if (field.value === true) {
                              field.onChange(null);
                            }
                          }}
                        />
                      </FormControl>
                      <FormLabel
                        htmlFor={`${name}-yes`}
                        className="text-[16px] font-[600] leading-none cursor-pointer"
                      >
                        Yes
                      </FormLabel>
                    </FormItem>

                    <FormItem className="col-span-1 flex items-center space-x-2 space-y-0 border border-[#E7E8E9] px-4 py-2 rounded-lg hover:bg-gray-50">
                      <FormControl>
                        <RadioGroupItem
                          value="no"
                          id={`${name}-no`}
                          onClick={() => {
                            if (field.value === false) {
                              field.onChange(null);
                            }
                          }}
                        />
                      </FormControl>
                      <FormLabel
                        htmlFor={`${name}-no`}
                        className="text-[16px] font-[600] leading-none cursor-pointer"
                      >
                        No
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            </>
          )}
        />
      </div>
    );
  };
  // Function to create an input field
  const renderInputField = (name, label, placeholder = "Enter value") => {
    return (
      <div className="w-full grid grid-cols-4 gap-4 items-center">
        <FormField
          control={form.control}
          name={name}
          render={({ field }) => (
            <>
              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormLabel className="text-[16px] font-[600] px-2">
                  {label}
                </FormLabel>
              </FormItem>
              <FormItem className="col-span-2">
                <FormControl>
                  <Input id={name} placeholder={placeholder} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            </>
          )}
        />
      </div>
    );
  };

  // Custom Date Picker Component
  const CustomDatePicker = ({ value, onChange, testId }) => {
    const [date, setDate] = useState();
    const [open, setOpen] = useState(false);

    // Initialize from prop value
    useEffect(() => {
      if (value) {
        try {
          // Handle different date formats
          let dateObj;
          if (value.includes('/')) {
            // Parse MM/DD/YYYY format to Date object
            const [month, day, year] = value.split("/");
            if (month && day && year) {
              dateObj = new Date(year, month - 1, day);
            }
          } else if (value.includes('-')) {
            // Handle YYYY-MM-DD format
            dateObj = new Date(value);
          }

          if (dateObj && !isNaN(dateObj.getTime())) {
            setDate(dateObj);
          }
        } catch (error) {
          console.error("Error parsing date:", error);
          setDate(undefined);
        }
      } else {
        setDate(undefined);
      }
    }, [value]);

    const handleDateSelect = (selectedDate) => {
      setDate(selectedDate);
      setOpen(false);
      if (selectedDate) {
        // Format as YYYY-MM-DD for form compatibility
        const year = selectedDate.getFullYear();
        const month = (selectedDate.getMonth() + 1).toString().padStart(2, "0");
        const day = selectedDate.getDate().toString().padStart(2, "0");
        onChange(`${year}-${month}-${day}`);
      } else {
        onChange(null);
      }
    };

    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={`w-full justify-start text-left font-normal ${!date ? "text-muted-foreground" : ""}`}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? format(date, "MM/dd/yyyy") : "Pick a date"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={date}
            onSelect={handleDateSelect}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    );
  };

  // Helper function to render radio fields from array
  const renderRadioFields = (fields) => {
    if (!fields) return null;
    return fields.map((field) => renderRadioField(field.name, field.label));
  };

  // Helper function to render input fields from array
  const renderInputFields = (fields) => {
    if (!fields) return null;
    return fields.map((field) => renderInputField(field.name, field.label, field.placeholder));
  };

  // Function to create a date field
  const renderDateField = (name, label, placeholder = "Select date") => {
    return (
      <div key={name} className="w-full grid grid-cols-4 gap-4 items-center">
        <FormField
          control={form.control}
          name={name}
          render={({ field }) => (
            <>
              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormLabel className="text-[16px] font-[600] px-2">
                  {label}
                </FormLabel>
              </FormItem>
              <FormItem className="col-span-2">
                <FormControl>
                  <CustomDatePicker
                    value={field.value}
                    onChange={field.onChange}
                    testId={name}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            </>
          )}
        />
      </div>
    );
  };

  // Helper function to render date fields from array
  const renderDateFields = (fields) => {
    if (!fields) return null;
    return fields.map((field) => renderDateField(field.name, field.label, field.placeholder));
  };

  // Helper function to render all field types for a section
  const renderSectionFields = (sectionConfig) => {
    if (!sectionConfig) return null;
    return (
      <>
        {sectionConfig.radio && renderRadioFields(sectionConfig.radio)}
        {sectionConfig.input && renderInputFields(sectionConfig.input)}
        {sectionConfig.date && renderDateFields(sectionConfig.date)}
      </>
    );
  };

  // Function to create a textarea field
  const renderTextareaField = (name, label, placeholder = "Enter notes") => {
    return (
      <div className="w-full grid grid-cols-4 gap-4 items-center">
        <FormField
          control={form.control}
          name={name}
          render={({ field }) => (
            <>
              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormLabel className="text-[16px] font-[600] px-2">
                  {label}
                </FormLabel>
              </FormItem>
              <FormItem className="col-span-2">
                <FormControl>
                  <Textarea id={name} placeholder={placeholder} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            </>
          )}
        />
      </div>
    );
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading ROS form...</span>
      </div>
    );
  }

  return (
    <div className="container mx-auto">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="">
          <Accordion type="multiple" className="w-full space-y-4">
            {/* General */}
            <AccordionItem value="general" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">General</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.general.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Skin */}
            <AccordionItem value="skin" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Skin</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.skin.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* HEENT */}
            <AccordionItem value="heent-head" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">HEENT</h3>
              </AccordionTrigger>
              <AccordionContent className="">
                <div className="space-y-6 px-4">
                  <div className="space-y-4">
                    <h2 className="text-xl font-semibold my-4">Head</h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioFields(fieldConfigs.heentHead.radio)}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold my-4">Eyes</h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioFields(fieldConfigs.heentEyes.radio)}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold my-4">Ears</h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioFields(fieldConfigs.heentEars.radio)}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold my-4">
                      Nose and Sinuses
                    </h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioFields(fieldConfigs.heentNose.radio)}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold mb-4">
                      Throat (Mouth & Pharynx)
                    </h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioFields(fieldConfigs.heentThroat.radio)}
                      {renderInputFields(fieldConfigs.heentThroat.input)}
                      {renderDateFields(fieldConfigs.heentThroat.date)}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Neck */}
            <AccordionItem value="neck" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Neck</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.neck.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Breasts */}
            <AccordionItem value="breasts" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Breasts</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.breasts.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Respiratory */}
            <AccordionItem value="respiratory" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Respiratory</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.respiratory.radio)}

                  <h3 className="text-base font-semibold">
                    If yes, provide sputum details
                  </h3>
                  {renderInputFields(fieldConfigs.respiratory.input)}
                  {renderDateFields(fieldConfigs.respiratory.date)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Cardiovascular */}
            <AccordionItem value="cardiovascular" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Cardiovascular</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.cardiovascular.radio)}
                  {renderInputFields(fieldConfigs.cardiovascular.input)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Gastrointestinal */}
            <AccordionItem
              value="gastrointestinal"
              className="border rounded-lg"
            >
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Gastrointestinal</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.gastrointestinal.radio)}

                  <h3 className="text-base font-semibold">Bowel Movements</h3>
                  {renderInputFields(fieldConfigs.gastrointestinal.input)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Peripheral Vascular */}
            <AccordionItem
              value="peripheral-vascular"
              className="border rounded-lg"
            >
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Peripheral Vascular</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.peripheralVascular.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Urinary */}
            <AccordionItem value="urinary" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Urinary</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.urinary.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Genital (Male) */}
            <AccordionItem value="genital-male" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Genital (Male)</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.genitalMale.radio)}

                  <h3 className="text-base font-semibold">
                    History of Sexually Transmitted infections
                  </h3>
                  {renderRadioFields(fieldConfigs.stdHistory.radio)}
                  {renderInputFields(fieldConfigs.stdHistory.input)}

                  <h3 className="text-base font-semibold">Sexual Habits</h3>
                  {renderRadioFields(fieldConfigs.sexualHabits.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Genital (Female) */}
            <AccordionItem value="genital-female" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Genital (Female)</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderInputFields(fieldConfigs.genitalFemale.input)}
                  {renderDateFields(fieldConfigs.genitalFemale.date)}
                  {renderRadioFields(fieldConfigs.genitalFemale.radio)}

                  <h3 className="text-base font-semibold">Sexual Habits</h3>
                  {renderRadioFields(fieldConfigs.sexualHabitsFemale.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Musculoskeletal */}
            <AccordionItem
              value="musculoskeletal"
              className="border rounded-lg"
            >
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Musculoskeletal</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.musculoskeletal.radio)}
                  {renderInputFields(fieldConfigs.musculoskeletal.input)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Psychiatric  */}
            <AccordionItem value="psychiatric" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Psychiatric</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.psychiatric.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Neurologic */}
            <AccordionItem value="neurologic" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Neurologic</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.neurologic.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Hematologic */}
            <AccordionItem value="hematologic" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Hematologic</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.hematologic.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Endocrine */}
            <AccordionItem value="endocrine" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Endocrine</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioFields(fieldConfigs.endocrine.radio)}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <div className="flex justify-end gap-4 py-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => form.reset()}
              disabled={isSubmitting}
            >
              Reset
            </Button>
            <Button type="submit" disabled={isSubmitting} variant="primary">
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {existingRosForm?.id ? "Updating..." : "Creating..."}
                </>
              ) : existingRosForm?.id ? (
                "Update ROS Form"
              ) : (
                "Create ROS Form"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
export default ROS;
