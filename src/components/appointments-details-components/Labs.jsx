"use client";
import { useEffect, useState, useCallback } from "react";
import { <PERSON><PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import { Checkbox } from "../../components/ui/checkbox";
import { User } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "../../hooks/use-toast";
import { Loader2 } from "lucide-react";
import { Calendar } from "../../components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../components/ui/popover";
import { CalendarIcon, Clock } from "lucide-react";
import { format } from "date-fns";
import {
  createLabForm_api,
  updateLabForm_api,
  getLabFormByAppointmentId_api,
} from "../../api/api_calls/appointmentforms/labform_apiCalls";

const labsFormSchema = z.object({
  // Basic fields
  paymentResponsibility: z.string().optional(),
  fastingRequired: z.boolean().optional(),

  // Test arrays - matching backend field names
  chemistry1Tests: z.array(z.string()).default([]),
  hematologyTests: z.array(z.string()).default([]),
  coagulationTests: z.array(z.string()).default([]),
  immunologyTests: z.array(z.string()).default([]),
  cardiacFunctionAndLipidTests: z.array(z.string()).default([]),
  toleranceTests: z.array(z.string()).default([]),
  nutritionalStatusTests: z.array(z.string()).default([]),
  endocrineAndTumorMarkers: z.array(z.string()).default([]),
  serumToxicologyTests: z.array(z.string()).default([]),
  bloodGases: z.array(z.string()).default([]),

  // Blood gas fields
  o2Device: z.string().nullable().optional(),
  o2Therapy: z.string().nullable().optional(),
  temprature: z.string().nullable().optional(), // Note: backend has typo "temprature"

  // Therapeutic drug monitoring - individual date fields matching backend
  digoxinLastDose: z.string().nullable().optional(),
  lithiumLastDose: z.string().nullable().optional(),
  phenobarbitalLastDose: z.string().nullable().optional(),
  phenytoinLastDose: z.string().nullable().optional(),
  primidoneLastDose: z.string().nullable().optional(),
  valproicAcidLastDose: z.string().nullable().optional(),
  cyclosporineLastDose: z.string().nullable().optional(),
  vancomycinLastDose: z.string().nullable().optional(),
  preDoseLastDose: z.string().nullable().optional(),
  postDoseLastDose: z.string().nullable().optional(),
  gentamicinLastDose: z.string().nullable().optional(),
  tobramycinLastDose: z.string().nullable().optional(),
  preDoseLevelLastDose: z.string().nullable().optional(),
  extendedIntervalPediatricsLastDose: z.string().nullable().optional(),
  postDoseLevelLastDose: z.string().nullable().optional(),
  extendedIntervalLastDose: z.string().nullable().optional(),
  hr22PostLevelNeonatesLastDose: z.string().nullable().optional(),
});

const Labs = ({ appointmentId }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [existingLabForm, setExistingLabForm] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [userCountry, setUserCountry] = useState("us");
  const { toast } = useToast();
  const [therapeuticTestValues, setTherapeuticTestValues] = useState({});

  const form = useForm({
    resolver: zodResolver(labsFormSchema),
    defaultValues: {
      // Basic fields
      paymentResponsibility: "",
      fastingRequired: false,

      // Test arrays - matching backend field names
      chemistry1Tests: [],
      hematologyTests: [],
      coagulationTests: [],
      immunologyTests: [],
      cardiacFunctionAndLipidTests: [],
      toleranceTests: [],
      nutritionalStatusTests: [],
      endocrineAndTumorMarkers: [],
      serumToxicologyTests: [],
      bloodGases: [],

      // Blood gas fields
      o2Device: "",
      o2Therapy: "",
      temprature: "", // Note: backend has typo "temprature"

      // Therapeutic drug monitoring - individual date fields
      digoxinLastDose: null,
      lithiumLastDose: null,
      phenobarbitalLastDose: null,
      phenytoinLastDose: null,
      primidoneLastDose: null,
      valproicAcidLastDose: null,
      cyclosporineLastDose: null,
      vancomycinLastDose: null,
      preDoseLastDose: null,
      postDoseLastDose: null,
      gentamicinLastDose: null,
      tobramycinLastDose: null,
      preDoseLevelLastDose: null,
      extendedIntervalPediatricsLastDose: null,
      postDoseLevelLastDose: null,
      extendedIntervalLastDose: null,
      hr22PostLevelNeonatesLastDose: null,
    },
  });

  const therapeuticDrugMonitoringFields = [
    { id: "digoxinLastDose", label: "Digoxin" },
    { id: "lithiumLastDose", label: "Lithium" },
    { id: "phenobarbitalLastDose", label: "Phenobarbital" },
    { id: "phenytoinLastDose", label: "Phenytoin" },
    { id: "primidoneLastDose", label: "Primidone (mysoline)" },
    { id: "valproicAcidLastDose", label: "Valproic acid (epival)" },
    { id: "cyclosporineLastDose", label: "Cyclosporine (purple tube)" },
    { id: "vancomycinLastDose", label: "Vancomycin" },
    { id: "preDoseLastDose", label: "Pre-dose" },
    { id: "postDoseLastDose", label: "Post-dose" },
    { id: "gentamicinLastDose", label: "Gentamicin" },
    { id: "tobramycinLastDose", label: "Tobramycin" },
    { id: "preDoseLevelLastDose", label: "Pre-dose level" },
    {
      id: "extendedIntervalPediatricsLastDose",
      label: "Extended interval pediatrics",
    },
    { id: "postDoseLevelLastDose", label: "Post-dose level" },
    { id: "extendedIntervalLastDose", label: "Extended interval" },
    {
      id: "hr22PostLevelNeonatesLastDose",
      label: "22hr post level - neonates",
    },
  ];

  const chemistryFields = [
    { id: "glucoseFasting", label: "Glucose- Fasting" },
    { id: "alkPhosAlp", label: "Alk Phos-ALP" },
    { id: "crp", label: "CRP" },
    { id: "glucoseRandom", label: "Glucose- Random" },
    { id: "alt", label: "ALT" },
    { id: "ammonia", label: "Ammonia" },
    { id: "electrolytesCO2", label: "Electrolytes CO2" },
    { id: "ggt", label: "GGT" },
    { id: "calciumIonized", label: "Calcium-Ionized" },
    { id: "creatinineEgfr", label: "Creatinine-eGFR" },
    { id: "ld", label: "LD" },
    { id: "osmolality", label: "Osmolality" },
    { id: "calciumTotal", label: "Calcium- Total" },
    { id: "lipase", label: "Lipase" },
    { id: "lactateGreenOnIce", label: "Lactate (Green on Ice)" },
    { id: "totalProtein", label: "Total Protein" },
    { id: "ck", label: "CK" },
    { id: "uricAcidUrate", label: "Uric Acid- Urate" },
    { id: "albumin", label: "Albumin" },
    { id: "serumPregnancy", label: "Serum Pregnancy (+-)" },
    { id: "magnesium", label: "Magnesium" },
    { id: "totalBilirubin", label: "Total Bilirubin" },
    { id: "bilirubinDirect", label: "Bilirubin- Direct" },
    { id: "phosphate", label: "Phosphate" },
  ];

  const hematologyFields = [
    { id: "cbcAutoDitt", label: "CBC & Auto Ditt" },
    { id: "monoScreen", label: "Mono Screen" },
    { id: "a1c", label: "A1C" },
  ];

  const coagulationFields = [
    { id: "ptt", label: "PTT" },
    { id: "dDimer", label: "D-Dimer" },
    { id: "fibrinogen", label: "Fibrinogen'" },
  ];

  const immunologyFields = [
    { id: "tissueTransglutamine", label: "PTT" },
    { id: "protein", label: "D-Dimer" },
    { id: "vasculitisMpoPr3", label: "Vasculitis MPO & PR3'" },
    { id: "rheumatoidFactor", label: "Rheumatoid Factor''" },
    { id: "ccpCitroineAb", label: "CCP (Citroine Ab)', value:'" },
    { id: "microglobulin", label: "Microglobulin'" },
    { id: "complementC3C4", label: "Complement C3 & C4'" },
    { id: "serumFreeLightChains", label: "Serum Free Light Chains'" },
    { id: "iggIgaIgm", label: "IgG IgA & IgM'" },
    { id: "antiGbm", label: "Anti-GBM'" },
    { id: "asot", label: "ASOT'" },
    { id: "u1Antitrypsin", label: "U-1-Antitrypsin" },
    { id: "farmersLung", label: "Farmers Lung''" },
    { id: "ama", label: "AMA'" },
    { id: "dsDNA", label: "ds DNA'" },
    { id: "haptoglobin", label: "Haptoglobin'" },
    { id: "cardiolipin", label: "Cardiolipin'" },
  ];

  const cardiacFields = [
    { label: "HS-CRP-Cardiac", id: "hsCrpCardiac" },
    { label: "Passing Non-Passing", id: "passingNonPassing" },
    { label: "BNP (Purple Tube) ", id: "bnpPurpleTube" },
    {
      label: "Lipid Profile: Cholesterol, LDL, HDL & Triglycerides",
      id: "ldlHdlTriglycerides",
    },
    { label: "Troponin ( Green Tube)", id: "troponinGreenTube" },
  ];

  const ToleranceTestsFields = [
    { label: "75 g Diabetic-Confirmatory", id: "75gDiabeticConfirmatory" },
    { label: "50 g Gestational-Screen", id: "50gGestationalScreen" },
    { label: "Lactose Tolerance Test", id: "lactoseToleranceTest" },
    {
      label: "75 g Post-partum-Screen (Gestational Diabetes Patients)",
      id: "75gPostPartumScreen",
    },
    {
      label: "75 g Gestational-Confirmatory",
      id: "75gGestationalConfirmatory",
    },
  ];

  const nutritionalStatusFields = [
    { label: "Ferritin", id: "ferritin" },
    { label: "Prealbumin", id: "prealbumin" },
    { label: "Iron Studies", id: "ironStudies" },
    { label: "Vitamin B12", id: "vitaminB12" },
  ];

  const endocrineTumorMarkersFields = [
    { label: "Prolactin", id: "prolactin" },
    { label: "DHEAS", id: "dheas" },
    { label: "Cortisol H/S", id: "cortisolHS" },
    { label: "PTH- Intact (red tube)", id: "pthIntactRedTube" },
    { label: "PSA (40 to 75 yrs)", id: "psa40To75Yrs" },
    { label: "TSH- Diagnostic", id: "tshDiagnostic" },
    { label: "Progesterone", id: "progesterone" },
    { label: "Estradiol", id: "estradiol" },
    { label: "FSH", id: "fsh" },
    { label: "LH", id: "lh" },
    { label: "CA 15-3", id: "ca153" },
    { label: "TSH- Monitor Tx", id: "tshMonitorTx" },
    { label: "CA-125", id: "ca125" },
    { label: "CEA", id: "cea" },
    { label: "AFP", id: "afp" },
    { label: "CA 19-9", id: "ca199" },
    { label: "Testosterone -Total", id: "testosteroneTotal" },
  ];

  const serumToxicologyFields = [
    { label: "Ethanol", id: "ethanol" },
    { label: "Tricyclics- Screen", id: "tricyclicsScreen" },
    { label: "Acetaminophen", id: "acetaminophen" },
    { label: "Salicylate", id: "salicylate" },
  ];

  const bloodGasFields = [
    { id: "o2Device", label: "O2 Device", placeholder: "Value", type: "input" },
    {
      id: "o2Therapy",
      label: "O2 Therapy",
      placeholder: "Value",
      type: "input",
    },
    {
      id: "temprature", // Note: backend has typo "temprature"
      label: "Temperature",
      placeholder: "Value",
      type: "input",
    },
    {
      type: "multiselect",
      id: "bloodGases",
      fields: [
        {
          id: "carboxyhemoglobinCo",
          label: "Carboxyhemoglobin-CO",
          type: "checkbox",
        },
        { id: "prealbumin", label: "Prealbumin", type: "checkbox" },
        { id: "methemoglobin", label: "Methemoglobin", type: "checkbox" },
        {
          id: "specimenArterialCapillary",
          label:
            "Specimen Arterial Capillary Central Mixed Venous Venous (Green Tube no Gel on Ice) Cord Send Cord on Ice)",
          type: "checkbox",
        },
        { id: "lactate", label: "Lactate", type: "checkbox" },
      ],
    },
  ];

  // Helper function to parse date and time into ISO string
  const parseDateTime = (dateStr, timeStr) => {
    if (!dateStr || !timeStr) return null;

    try {
      // Parse date (MM/DD/YYYY format)
      const [month, day, year] = dateStr.split("/");

      // Parse time (HH:MM AM/PM format)
      const [time, period] = timeStr.split(" ");
      const [hours, minutes] = time.split(":");
      let hour24 = parseInt(hours);

      if (period === "PM" && hour24 !== 12) {
        hour24 += 12;
      } else if (period === "AM" && hour24 === 12) {
        hour24 = 0;
      }

      // Create ISO date string
      return new Date(
        year,
        month - 1,
        day,
        hour24,
        parseInt(minutes),
      ).toISOString();
    } catch (error) {
      console.error("Error parsing date/time:", error);
      return null;
    }
  };

  // Helper function to format ISO date for form display
  const formatDateForForm = (isoDate) => {
    if (!isoDate) return null;

    try {
      return new Date(isoDate).toISOString().split("T")[0];
    } catch (error) {
      console.error("Error formatting date for form:", error);
      return null;
    }
  };

  // Helper function to format ISO date for UI display
  const formatDateTimeForUI = (isoDate) => {
    if (!isoDate) return { date: "", time: "" };

    try {
      const date = new Date(isoDate);
      return {
        date: date.toLocaleDateString("en-US"),
        time: date.toLocaleTimeString("en-US", {
          hour: "2-digit",
          minute: "2-digit",
          hour12: true,
        }),
      };
    } catch (error) {
      console.error("Error formatting date/time for UI:", error);
      return { date: "", time: "" };
    }
  };

  // Function to fetch existing lab form data
  const fetchExistingLabForm = useCallback(async () => {
    // Skip fetching if no appointmentId is provided (e.g., in consultation panel)
    if (!appointmentId) {
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      const response = await getLabFormByAppointmentId_api(appointmentId);
      if (response?.data) {
        setExistingLabForm(response.data);
        setIsEditMode(true);

        // Populate form with existing data - matching backend field names
        const labData = response.data;
        form.reset({
          // Basic fields
          paymentResponsibility: labData.paymentResponsibility || "",
          fastingRequired: labData.fastingRequired || false,

          // Test arrays - matching backend field names
          chemistry1Tests: labData.chemistry1Tests || [],
          hematologyTests: labData.hematologyTests || [],
          coagulationTests: labData.coagulationTests || [],
          immunologyTests: labData.immunologyTests || [],
          cardiacFunctionAndLipidTests:
            labData.cardiacFunctionAndLipidTests || [],
          toleranceTests: labData.toleranceTests || [],
          nutritionalStatusTests: labData.nutritionalStatusTests || [],
          endocrineAndTumorMarkers: labData.endocrineAndTumorMarkers || [],
          serumToxicologyTests: labData.serumToxicologyTests || [],
          bloodGases: labData.bloodGases || [],

          // Blood gas fields
          o2Device: labData.o2Device ?? null,
          o2Therapy: labData.o2Therapy ?? null,
          temprature: labData.temprature ?? null, // Note: backend has typo

          // Therapeutic drug monitoring - individual date fields
          digoxinLastDose: formatDateForForm(labData.digoxinLastDose),
          lithiumLastDose: formatDateForForm(labData.lithiumLastDose),
          phenobarbitalLastDose: formatDateForForm(
            labData.phenobarbitalLastDose,
          ),
          phenytoinLastDose: formatDateForForm(labData.phenytoinLastDose),
          primidoneLastDose: formatDateForForm(labData.primidoneLastDose),
          valproicAcidLastDose: formatDateForForm(labData.valproicAcidLastDose),
          cyclosporineLastDose: formatDateForForm(labData.cyclosporineLastDose),
          vancomycinLastDose: formatDateForForm(labData.vancomycinLastDose),
          preDoseLastDose: formatDateForForm(labData.preDoseLastDose),
          postDoseLastDose: formatDateForForm(labData.postDoseLastDose),
          gentamicinLastDose: formatDateForForm(labData.gentamicinLastDose),
          tobramycinLastDose: formatDateForForm(labData.tobramycinLastDose),
          preDoseLevelLastDose: formatDateForForm(labData.preDoseLevelLastDose),
          extendedIntervalPediatricsLastDose: formatDateForForm(
            labData.extendedIntervalPediatricsLastDose,
          ),
          postDoseLevelLastDose: formatDateForForm(
            labData.postDoseLevelLastDose,
          ),
          extendedIntervalLastDose: formatDateForForm(
            labData.extendedIntervalLastDose,
          ),
          hr22PostLevelNeonatesLastDose: formatDateForForm(
            labData.hr22PostLevelNeonatesLastDose,
          ),
        });

        // Populate therapeutic test values for UI display
        const therapeuticValues = {};
        therapeuticDrugMonitoringFields.forEach((field) => {
          const dateValue = labData[field.id];
          if (dateValue) {
            therapeuticValues[field.id] = formatDateTimeForUI(dateValue);
          }
        });
        setTherapeuticTestValues(therapeuticValues);
      }
    } catch (error) {
      // If lab form doesn't exist, that's fine - we'll create a new one
      console.log("No existing lab form found, creating new one");
    } finally {
      setIsLoading(false);
    }
  }, [appointmentId, form]);

  const onSubmit = async (data) => {
    setIsSubmitting(true);

    try {
      // Convert therapeutic test values to backend format (individual date fields)
      const therapeuticData = {};

      // Initialize all therapeutic fields to null
      therapeuticDrugMonitoringFields.forEach((field) => {
        therapeuticData[field.id] = null;
      });

      // Convert UI date/time values to ISO strings using helper function
      Object.keys(therapeuticTestValues).forEach((testId) => {
        const test = therapeuticTestValues[testId];
        if (test && test.date && test.time) {
          therapeuticData[testId] = parseDateTime(test.date, test.time);
        }
      });

      // Prepare data for backend - matching DTO structure with all fields
      const updatedData = {
        ...data,
        ...therapeuticData, // Spread individual therapeutic date fields
      };

      // Convert empty strings to null for string fields (but preserve actual string values)
      const cleanedData = { ...updatedData };
      const stringFields = [
        "o2Device",
        "o2Therapy",
        "temprature",
        "paymentResponsibility",
      ];
      stringFields.forEach((field) => {
        if (cleanedData[field] === "") {
          cleanedData[field] = null;
        }
      });

      console.log("Lab form submitted with data:", cleanedData);

      let response;
      if (isEditMode && existingLabForm) {
        // Update existing lab form
        response = await updateLabForm_api({
          id: existingLabForm.id,
          ...cleanedData,
        });
        toast({
          description: "Lab form updated successfully",
        });
      } else if (appointmentId) {
        // Create new lab form (only if appointmentId is provided)
        response = await createLabForm_api({
          appointmentId,
          ...cleanedData,
        });
        toast({
          description: "Lab form created successfully",
        });
        setIsEditMode(true);
        setExistingLabForm(response.data);
      } else {
        // Handle case where no appointmentId is provided (e.g., consultation panel)
        console.log("Lab form data (no appointmentId):", cleanedData);
        toast({
          description: "Lab form data saved locally",
        });
      }

      console.log("Lab form API response:", response);
    } catch (error) {
      toast({
        description: error.message || "Failed to save lab form",
        variant: "destructive",
      });
      console.error("ERROR IN LAB FORM SAVE => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Fetch existing lab form data on component mount
  useEffect(() => {
    fetchExistingLabForm();
  }, [fetchExistingLabForm]);

  // Replace CustomDatePicker component with this new one
  const CustomDatePicker = ({ value, onChange, testId }) => {
    const [date, setDate] = useState();
    const [open, setOpen] = useState(false);

    // Initialize from prop value
    useEffect(() => {
      if (value) {
        // Parse MM/DD/YYYY format to Date object
        const [month, day, year] = value.split("/");
        if (month && day && year) {
          const dateObj = new Date(year, month - 1, day);
          if (!isNaN(dateObj.getTime())) {
            setDate(dateObj);
          }
        }
      } else {
        setDate(undefined);
      }
    }, [value]);

    const handleDateSelect = (selectedDate) => {
      setDate(selectedDate);
      setOpen(false);
      if (selectedDate) {
        // Format as MM/DD/YYYY
        const month = (selectedDate.getMonth() + 1).toString().padStart(2, "0");
        const day = selectedDate.getDate().toString().padStart(2, "0");
        const year = selectedDate.getFullYear();
        onChange(`${month}/${day}/${year}`);
      } else {
        onChange("");
      }
    };

    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={`w-full justify-start text-left font-normal ${!date ? "text-muted-foreground" : ""}`}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? format(date, "MM/dd/yyyy") : "Pick a date"}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={date}
            onSelect={handleDateSelect}
            initialFocus
          />
        </PopoverContent>
      </Popover>
    );
  };

  // Enhanced TimePicker Component
  const CustomTimePicker = ({ value, onChange, testId }) => {
    const [hour, setHour] = useState("12");
    const [minute, setMinute] = useState("15");
    const [period, setPeriod] = useState("AM");
    const [open, setOpen] = useState(false);
    const [isInitialized, setIsInitialized] = useState(false);

    const hours = Array.from({ length: 12 }, (_, i) =>
      (i + 1).toString().padStart(2, "0"),
    );
    const minutes = ["15", "30", "45", "60"];
    const periods = ["AM", "PM"];

    // Initialize from prop value only once or when value changes from external source
    useEffect(() => {
      if (value && value.match(/^\d{1,2}:\d{2} (AM|PM)$/) && !isInitialized) {
        const [time, period] = value.split(" ");
        const [h, m] = time.split(":");
        setHour(h.padStart(2, "0"));
        setMinute(m);
        setPeriod(period);
        setIsInitialized(true);
      } else if (!value && !isInitialized) {
        // Reset to default values when no value is provided
        setHour("12");
        setMinute("15");
        setPeriod("AM");
        setIsInitialized(true);
      }
    }, [value, isInitialized]);

    const handleTimeSelection = (newHour, newMinute, newPeriod) => {
      // Only update local state, don't call onChange yet
      if (newHour !== undefined) setHour(newHour);
      if (newMinute !== undefined) setMinute(newMinute);
      if (newPeriod !== undefined) setPeriod(newPeriod);
    };

    const handleDoneClick = () => {
      // Only call onChange when Done button is clicked
      const timeString = `${hour}:${minute} ${period}`;
      onChange(timeString);
      setOpen(false);
    };

    const handleCancel = () => {
      // Reset to original value if user cancels
      if (value && value.match(/^\d{1,2}:\d{2} (AM|PM)$/)) {
        const [time, period] = value.split(" ");
        const [h, m] = time.split(":");
        setHour(h.padStart(2, "0"));
        setMinute(m);
        setPeriod(period);
      } else {
        setHour("12");
        setMinute("15");
        setPeriod("AM");
      }
      setOpen(false);
    };

    const displayTime = value || "Select time";

    return (
      <Popover
        open={open}
        onOpenChange={(newOpen) => {
          if (!newOpen) {
            // If closing without clicking Done, reset to original values
            handleCancel();
          } else {
            setOpen(newOpen);
          }
        }}
      >
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={`w-full justify-start text-left font-normal ${!value ? "text-muted-foreground" : ""}`}
          >
            <Clock className="mr-2 h-4 w-4" />
            {displayTime}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-4" align="start">
          <div className="flex space-x-2">
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium">Hour</label>
              <select
                value={hour}
                onChange={(e) =>
                  handleTimeSelection(e.target.value, undefined, undefined)
                }
                className="border border-[#E7E8E9] rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {hours.map((h) => (
                  <option key={h} value={h}>
                    {h}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium">Minute</label>
              <select
                value={minute}
                onChange={(e) =>
                  handleTimeSelection(undefined, e.target.value, undefined)
                }
                className="border border-[#E7E8E9] rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {minutes.map((m) => (
                  <option key={m} value={m}>
                    {m}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex flex-col space-y-2">
              <label className="text-sm font-medium">Period</label>
              <select
                value={period}
                onChange={(e) =>
                  handleTimeSelection(undefined, undefined, e.target.value)
                }
                className="border border-[#E7E8E9] rounded-md p-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {periods.map((p) => (
                  <option key={p} value={p}>
                    {p}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div className="flex justify-between mt-4">
            <Button
              size="sm"
              variant="outline"
              onClick={handleCancel}
              className="text-gray-600 border-gray-300 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              size="sm"
              onClick={handleDoneClick}
              className="bg-blue-600 text-white hover:bg-blue-700"
            >
              Done
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    );
  };

  const renderCheckboxField = (testId, label, fieldName) => {
    return (
      <div className="flex items-center">
        <FormField
          control={form.control}
          name={fieldName}
          render={({ field }) => {
            const value = field.value || [];
            return (
              <FormItem className="flex items-center space-x-2 space-y-0">
                <FormControl>
                  <Checkbox
                    id={testId}
                    checked={value.includes(testId)}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        field.onChange([...value, testId]);
                      } else {
                        field.onChange(value.filter((v) => v !== testId));
                      }
                    }}
                  />
                </FormControl>
                <FormLabel
                  htmlFor={testId}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {label}
                </FormLabel>
              </FormItem>
            );
          }}
        />
      </div>
    );
  };

  const renderInputField = (
    name,
    label,
    placeholder = "Value",
    req = false,
  ) => {
    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-gray-700 font-medium">{label}</FormLabel>
            <FormControl>
              <Input
                id={name}
                placeholder={placeholder}
                className={`py-2 transition-all focus:ring-primary/50 focus:border-primary focus:ring-2`}
                {...field}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  };

  const renderSelectField = (
    name,
    label,
    options,
    placeholder = "Select an option",
  ) => {
    return (
      <FormField
        control={form.control}
        name={name}
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-gray-700 font-medium">{label}</FormLabel>
            <Select
              onValueChange={(value) => {
                // Convert string back to original type (boolean, string, etc.)
                const option = options.find(
                  (opt) => opt.value.toString() === value,
                );
                field.onChange(option ? option.value : value);
              }}
              value={field.value?.toString() || ""}
            >
              <FormControl>
                <SelectTrigger
                  className={`transition-all focus:ring-primary/50 focus:border-primary ${!field.value ? "text-[#1E1E1E80]" : "text-[#1E1E1E]"} focus:ring-2`}
                >
                  <SelectValue placeholder={placeholder} />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {options.map((option) => (
                  <SelectItem
                    key={option.value.toString()}
                    value={option.value.toString()}
                  >
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    );
  };

  const updateTherapeuticTestValue = (testId, field, value) => {
    setTherapeuticTestValues((prev) => {
      const testValues = prev[testId] || {};
      return {
        ...prev,
        [testId]: {
          ...testValues,
          [field]: value,
        },
      };
    });
  };

  const renderTherapeuticTestField = (test) => {
    const testValues = therapeuticTestValues[test.id] || {};
    const dateValue = testValues.date || "";
    const timeValue = testValues.time || "";

    const resetTherapeuticTestField = (testId) => {
      setTherapeuticTestValues((prev) => {
        const newValues = { ...prev };
        delete newValues[testId]; // Remove the entire test entry
        return newValues;
      });
    };

    return (
      <div key={test.id} className="flex flex-col space-y-2">
        <div className="flex items-center justify-between">
          <FormLabel className="text-sm font-medium">{test.label}</FormLabel>
          {(dateValue || timeValue) && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => resetTherapeuticTestField(test.id)}
              className="text-red-600 border-red-300 hover:bg-red-50 hover:border-red-400 px-3 py-1 h-8"
            >
              Reset
            </Button>
          )}
        </div>
        <div className="ml-6 flex space-x-4">
          <div className="flex-1">
            <FormLabel className="text-gray-700 font-medium">Date</FormLabel>
            <CustomDatePicker
              value={dateValue}
              onChange={(value) =>
                updateTherapeuticTestValue(test.id, "date", value)
              }
              testId={test.id}
            />
          </div>
          <div className="flex-1">
            <FormLabel className="text-gray-700 font-medium">Time</FormLabel>
            <CustomTimePicker
              value={timeValue}
              onChange={(value) =>
                updateTherapeuticTestValue(test.id, "time", value)
              }
              testId={test.id}
            />
          </div>
        </div>
      </div>
    );
  };

  // Show loading state while fetching existing data
  if (isLoading) {
    return (
      <div className="w-full space-y-6">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <p className="text-gray-600">Loading lab form...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full mx-auto p-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information */}
          <div className="border-solid border-[1px] border-[#E7E8E9] p-6 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {renderInputField(
                "paymentResponsibility",
                "Payment Responsibility *",
                "Value",
                User,
              )}

              {renderSelectField("fastingRequired", "Fasting Required *", [
                { value: true, label: "Yes" },
                { value: false, label: "No" },
              ])}
            </div>
          </div>

          {/* Chemistry Section */}
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Chemistry
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 gap-4 mb-6 p-6 border-solid border-[1px] border-[#E7E8E9] rounded-lg">
              {chemistryFields.map((test) =>
                renderCheckboxField(test.id, test.label, "chemistry1Tests"),
              )}
            </div>
          </div>

          {/* Hematology Section */}
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Hematology
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 gap-4 mb-6 p-6 border-solid border-[1px] border-[#E7E8E9] rounded-lg">
              {hematologyFields.map((test) =>
                renderCheckboxField(test.id, test.label, "hematologyTests"),
              )}
            </div>
          </div>

          {/* Coagulation Section */}
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Coagulation
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 gap-4 mb-6 p-6 border-solid border-[1px] border-[#E7E8E9] rounded-lg">
              {coagulationFields.map((test) =>
                renderCheckboxField(test.id, test.label, "coagulationTests"),
              )}
            </div>
          </div>

          {/* Immunology Section */}
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Immunology
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 gap-4 mb-6 p-6 border-solid border-[1px] border-[#E7E8E9] rounded-lg">
              {immunologyFields.map((test) =>
                renderCheckboxField(test.id, test.label, "immunologyTests"),
              )}
            </div>
          </div>

          {/* Cardiac Function and Lipids  Section */}
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Cardiac Function and Lipids
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 gap-4 mb-6 p-6 border-solid border-[1px] border-[#E7E8E9] rounded-lg">
              {cardiacFields.map((test) =>
                renderCheckboxField(
                  test.id,
                  test.label,
                  "cardiacFunctionAndLipidTests",
                ),
              )}
            </div>
          </div>

          {/* Tolerance Test Section */}
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Tolerance Test
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 gap-4 mb-6 p-6 border-solid border-[1px] border-[#E7E8E9] rounded-lg">
              {ToleranceTestsFields.map((test) =>
                renderCheckboxField(test.id, test.label, "toleranceTests"),
              )}
            </div>
          </div>

          {/* Nutritional Status Section */}
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Nutritional Status
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 gap-4 mb-6 p-6 border-solid border-[1px] border-[#E7E8E9] rounded-lg">
              {nutritionalStatusFields.map((test) =>
                renderCheckboxField(
                  test.id,
                  test.label,
                  "nutritionalStatusTests",
                ),
              )}
            </div>
          </div>

          {/* Endocrine and Tumor Markers Section */}
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Endocrine and Tumor Markers
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 gap-4 mb-6 p-6 border-solid border-[1px] border-[#E7E8E9] rounded-lg">
              {endocrineTumorMarkersFields.map((test) =>
                renderCheckboxField(
                  test.id,
                  test.label,
                  "endocrineAndTumorMarkers",
                ),
              )}
            </div>
          </div>

          {/*  Therapeutic Drug Monitoring Section */}
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Therapeutic Drug Monitoring
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-1 xl:grid-cols-1 gap-4 mb-6 p-6 border-solid border-[1px] border-[#E7E8E9] rounded-lg">
              {therapeuticDrugMonitoringFields.map((test) =>
                renderTherapeuticTestField(test),
              )}
            </div>
          </div>

          {/*  Serum Toxicology Section */}
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Serum Toxicology
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-2 gap-4 mb-6 p-6 border-solid border-[1px] border-[#E7E8E9] rounded-lg">
              {serumToxicologyFields.map((test) =>
                renderCheckboxField(
                  test.id,
                  test.label,
                  "serumToxicologyTests",
                ),
              )}
            </div>
          </div>

          {/* Blood Gas */}
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Blood Gas
            </h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-3 gap-4 mb-6 p-6 border-solid border-[1px] border-[#E7E8E9] rounded-lg">
              {bloodGasFields.map((field) => {
                if (field.type === "input") {
                  return renderInputField(
                    field.id,
                    field.label,
                    field.placeholder,
                  );
                } else if (field.type === "multiselect") {
                  return (
                    <div key={field.id} className="col-span-full">
                      <h4 className="font-medium mb-2">{field.label}</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-4">
                        {field.fields.map((subField) =>
                          renderCheckboxField(
                            subField.id,
                            subField.label,
                            "bloodGases",
                          ),
                        )}
                      </div>
                    </div>
                  );
                }
                return null;
              })}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-4 py-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                form.reset();
                setTherapeuticTestValues({});
              }}
              disabled={isSubmitting}
              className="bg-gray-100 text-gray-700 hover:bg-gray-200"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-blue-600 text-white hover:bg-blue-700"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Save"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default Labs;
