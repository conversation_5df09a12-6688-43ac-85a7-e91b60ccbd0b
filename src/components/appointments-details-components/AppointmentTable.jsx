import { Eye } from "lucide-react";
import { Input } from "../../../../components/ui/input";
import { Calendar } from "lucide-react";

const AppointmentTable = () => {
  const appointmentData = [
    {
      id: "1234588",
      status: "Upcoming",
      date: "25/01/2025",
      time: "11:30 AM",
      paymentStatus: "Completed",
    },
    {
      id: "1234588",
      status: "Completed",
      date: "25/01/2025",
      time: "11:30 AM",
      paymentStatus: "Completed",
    },
    {
      id: "1234588",
      status: "Cancelled",
      date: "25/01/2025",
      time: "11:30 AM",
      paymentStatus: "Cancelled",
    },
    {
      id: "1234588",
      status: "Completed",
      date: "25/01/2025",
      time: "11:30 AM",
      paymentStatus: "Completed",
    },
    {
      id: "1234588",
      status: "Completed",
      date: "25/01/2025",
      time: "11:30 AM",
      paymentStatus: "Completed",
    },
    {
      id: "1234588",
      status: "Completed",
      date: "25/01/2025",
      time: "11:30 AM",
      paymentStatus: "Completed",
    },
    {
      id: "1234588",
      status: "Completed",
      date: "25/01/2025",
      time: "11:30 AM",
      paymentStatus: "Completed",
    },
    {
      id: "1234588",
      status: "Completed",
      date: "25/01/2025",
      time: "11:30 AM",
      paymentStatus: "Completed",
    },
    {
      id: "1234588",
      status: "Completed",
      date: "25/01/2025",
      time: "11:30 AM",
      paymentStatus: "Completed",
    },
    {
      id: "1234588",
      status: "Completed",
      date: "25/01/2025",
      time: "11:30 AM",
      paymentStatus: "Completed",
    },
  ];

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "Completed":
        return "bg-blue-100 text-blue-600";
      case "Upcoming":
        return "bg-yellow-100 text-yellow-600";
      case "Cancelled":
        return "bg-red-100 text-red-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Appointment</h2>
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Calendar className="h-5 w-5 text-gray-400" />
          </div>
          <Input
            type="text"
            className="pl-10"
            placeholder="search by selecting date"
          />
        </div>
      </div>
      <div className="overflow-x-auto">
        <table className="w-full border-collapse">
          <thead>
            <tr className="bg-[#F8F8F8]">
              <th className="py-3 px-4 text-left font-medium text-gray-700">
                Appointment ID
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-700">
                Appointment Status
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-700">
                Date
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-700">
                Time
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-700">
                Payment Status
              </th>
              <th className="py-3 px-4 text-left font-medium text-gray-700">
                Action
              </th>
            </tr>
          </thead>
          <tbody>
            {appointmentData.map((appointment, index) => (
              <tr key={index} className="border-b border-gray-200">
                <td className="py-4 px-4">{appointment.id}</td>
                <td className="py-4 px-4">
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(appointment.status)}`}
                  >
                    {appointment.status}
                  </span>
                </td>
                <td className="py-4 px-4">{appointment.date}</td>
                <td className="py-4 px-4">{appointment.time}</td>
                <td className="py-4 px-4">
                  <span
                    className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                      appointment.paymentStatus,
                    )}`}
                  >
                    {appointment.paymentStatus}
                  </span>
                </td>
                <td className="py-4 px-4">
                  <button className="text-gray-500 hover:text-gray-700">
                    <Eye size={18} />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default AppointmentTable;
