import React from "react";
import { useState, useEffect, useCallback } from "react";
import { useLocation } from "react-router-dom";
import { But<PERSON> } from "../../components/ui/button";
import { Input } from "../../components/ui/input";
import { Textarea } from "../../components/ui/textarea";
import { Label } from "../../components/ui/label";
import { RiDeleteBin6Line } from "react-icons/ri";
import { FaRegEdit } from "react-icons/fa";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "../../hooks/use-toast";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { AlertCircle, Loader2 } from "lucide-react";
import {
  createPrescriptionForm_apiCalls,
  updatePrescriptionForm_apiCalls,
  getPrescriptionFormByAppointmentId_apiCalls,
  deletePrescriptionForm_apiCalls,
} from "../../api/api_calls/appointmentforms/prescriptionForm_apiCalls";

// Prescription form schema
const prescriptionSchema = z.object({
  drugName: z.string().min(1, { message: "Drug name is required" }),
  dosage: z.string().min(1, { message: "Dosage is required" }),
  duration: z.string().min(1, { message: "Duration is required" }),
  frequency: z.string().min(1, { message: "Frequency is required" }),
  instructions: z.string().optional(),
});

const Prescription = ({ isCompleted = false }) => {
  const location = useLocation();
  const { toast } = useToast();

  // Get appointmentId from location state
  const appointmentId = location.state?.appointmentId || location.state?.appointment?.id;

  const [showPatientFormModal, setShowPatientFormModal] = useState(false);
  const [prescriptions, setPrescriptions] = useState([]);
  const [editIndex, setEditIndex] = useState(null);
  const [loading, setLoading] = useState(true);
  const [prescriptionFormData, setPrescriptionFormData] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);

  // Fetch existing prescription form data
  const fetchPrescriptionForm = useCallback(async () => {
    if (!appointmentId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await getPrescriptionFormByAppointmentId_apiCalls({
        appointmentId,
      });

      if (response?.data) {
        // Handle both single prescription and array of prescriptions
        if (Array.isArray(response.data)) {
          // If response is an array of prescriptions
          setPrescriptions(response.data);
        } else if (response.data.prescriptions && Array.isArray(response.data.prescriptions)) {
          // If response has prescriptions array
          setPrescriptions(response.data.prescriptions);
        } else if (response.data.drugName) {
          // If response is a single prescription object
          setPrescriptions([response.data]);
        }

        setPrescriptionFormData(response.data);
        setIsEditMode(true);
      }
    } catch (error) {
      console.error("Failed to fetch prescription form:", error);
      // Don't show error toast for missing prescription form
      setIsEditMode(false);
    } finally {
      setLoading(false);
    }
  }, [appointmentId]);

  // Effect to fetch prescription form on component mount
  useEffect(() => {
    fetchPrescriptionForm();
  }, [fetchPrescriptionForm]);

  const handleAddPrescription = async (data) => {
    try {
      // Prepare form data for API - individual prescription format
      const formData = {
        appointmentId,
        drugName: data.drugName,
        dosage: data.dosage,
        duration: data.duration,
        frequency: data.frequency,
        instructions: data.instructions || "",
      };

      let response;
      let updatedPrescriptions;

      if (editIndex !== null) {
        // Update existing prescription
        const existingPrescription = prescriptions[editIndex];
        if (existingPrescription.id) {
          formData.id = existingPrescription.id;
          response = await updatePrescriptionForm_apiCalls(formData);
        }

        updatedPrescriptions = [...prescriptions];
        updatedPrescriptions[editIndex] = { ...data, id: existingPrescription.id || response?.data?.id };

        toast({
          title: "Prescription updated",
          description: `${data.drugName} has been updated successfully.`,
        });
      } else {
        // Create new prescription
        response = await createPrescriptionForm_apiCalls(formData);

        // Add the new prescription with the returned ID
        const newPrescription = { ...data, id: response?.data?.id };
        updatedPrescriptions = [...prescriptions, newPrescription];

        toast({
          title: "Prescription added",
          description: `${data.drugName} has been added to the prescription list.`,
        });
      }

      if (response?.data) {
        setPrescriptions(updatedPrescriptions);
        setIsEditMode(true);

        if (editIndex !== null) {
          setEditIndex(null);
        }
      }
    } catch (error) {
      console.error("Error saving prescription:", error);
      toast({
        title: "Error",
        description: "Failed to save prescription. Please try again.",
        variant: "destructive",
      });
    } finally {
      setShowPatientFormModal(false);
    }
  };

  const handleEditPrescription = (index) => {
    setEditIndex(index);
    setShowPatientFormModal(true);
  };

  const handleDeletePrescription = async (index) => {
    try {
      const prescriptionToDelete = prescriptions[index];
      const deletedDrug = prescriptionToDelete.drugName;

      // If the prescription has an ID, delete it from the backend
      if (prescriptionToDelete.id) {
        await deletePrescriptionForm_apiCalls({ id: prescriptionToDelete.id });
      }

      // Remove from local state
      const updatedPrescriptions = [...prescriptions];
      updatedPrescriptions.splice(index, 1);
      setPrescriptions(updatedPrescriptions);

      toast({
        title: "Prescription removed",
        description: `${deletedDrug} has been removed from the prescription list.`,
        variant: "destructive",
      });
    } catch (error) {
      console.error("Error deleting prescription:", error);
      toast({
        title: "Error",
        description: "Failed to delete prescription. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Loading state
  if (loading) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading prescription data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // No appointment ID provided
  if (!appointmentId) {
    return (
      <Card className="w-full">
        <CardContent className="p-6">
          <div className="flex flex-col items-center justify-center py-8 text-center">
            <AlertCircle className="w-12 h-12 text-muted-foreground mb-2" />
            <h3 className="text-lg font-medium">No Appointment ID</h3>
            <p className="text-sm text-muted-foreground">
              Unable to load prescription data without appointment information.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex w-full items-center justify-between flex-row">
          <div>
            <CardTitle className="text-xl font-semibold">
              Prescription
            </CardTitle>
            <CardDescription>
              <span className="text-[#1e1e1eb2]">Appointment ID:</span> {appointmentId}
            </CardDescription>
          </div>
          {!isCompleted && (
            <Button
              variant="primary"
              onClick={() => {
                setEditIndex(null);
                setShowPatientFormModal(true);
              }}
            >
              Add Medicine
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <div className="mb-4">
          <p className="text-[14px] text-muted-foreground font-medium">
            Issue Date:{" "}
            <span className="text-[14px] text-foreground">
              {new Date().toLocaleDateString()}
            </span>
          </p>
        </div>

        {prescriptions.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-8 text-center border border-dashed rounded-lg">
            <AlertCircle className="w-12 h-12 text-muted-foreground mb-2" />
            <h3 className="text-lg font-medium">No prescriptions added</h3>
            <p className="text-sm text-muted-foreground mt-1 mb-4">
              {isCompleted
                ? "No prescriptions were added for this appointment."
                : 'Click the "Add Medicine" button to prescribe medication for this patient.'
              }
            </p>
            {!isCompleted && (
              <Button
                variant="outline"
                onClick={() => {
                  setEditIndex(null);
                  setShowPatientFormModal(true);
                }}
              >
                Add First Prescription
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {prescriptions.map((prescription, index) => (
              <div
                key={index}
                className="flex items-center justify-between w-full border rounded-lg p-4 hover:bg-muted/50 transition-colors"
              >
                <div className="space-y-2">
                  <p className="text-[14px] text-muted-foreground font-medium">
                    Drug Name:{" "}
                    <span className="text-[14px] text-foreground">
                      {prescription.drugName}
                    </span>
                  </p>
                  <p className="text-[14px] text-muted-foreground font-medium">
                    Dosage:{" "}
                    <span className="text-[14px] text-foreground">
                      {prescription.dosage}
                    </span>
                  </p>
                  {prescription.instructions && (
                    <div>
                      <p className="text-[14px] text-muted-foreground font-medium">
                        Instructions:
                      </p>
                      <p className="text-[14px] text-foreground">
                        {prescription.instructions}
                      </p>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <p className="text-[14px] text-muted-foreground font-medium">
                    Duration:{" "}
                    <span className="text-[14px] text-foreground">
                      {prescription.duration}
                    </span>
                  </p>
                  <p className="text-[14px] text-muted-foreground font-medium">
                    Frequency:{" "}
                    <span className="text-[14px] text-foreground">
                      {prescription.frequency}
                    </span>
                  </p>
                </div>

                <div className="flex items-center gap-3">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleEditPrescription(index)}
                    className="h-8 w-8"
                  >
                    <FaRegEdit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeletePrescription(index)}
                    className="h-8 w-8 text-destructive hover:text-destructive/90"
                  >
                    <RiDeleteBin6Line className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {showPatientFormModal && (
        <PatientFormModal
          onSave={handleAddPrescription}
          onClose={() => {
            setShowPatientFormModal(false);
            setEditIndex(null);
          }}
          initialData={
            editIndex !== null ? prescriptions[editIndex] : undefined
          }
          isEditing={editIndex !== null}
        />
      )}
    </Card>
  );
};

const PatientFormModal = ({
  onSave,
  onClose,
  initialData,
  isEditing = false,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(prescriptionSchema),
    defaultValues: initialData || {
      drugName: "",
      dosage: "",
      duration: "",
      frequency: "",
      instructions: "",
    },
  });

  const onSubmit = async (data) => {
    try {
      setIsSubmitting(true);
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));
      onSave(data);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-background p-6 rounded-lg w-full max-w-xl max-h-[90vh] overflow-y-auto">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-2">
            <h3 className="font-bold text-xl">
              {isEditing ? "Edit Medicine Details" : "Enter Medicine Details"}
            </h3>
            <p className="text-sm text-muted-foreground">
              Fill in the details for this prescription.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="drugName" className="flex items-center">
                Drug Name <span className="text-destructive ml-1">*</span>
              </Label>
              <Input
                id="drugName"
                placeholder="Enter drug name"
                {...register("drugName")}
                className={errors.drugName ? "border-destructive" : ""}
              />
              {errors.drugName && (
                <p className="text-destructive text-sm">
                  {errors.drugName.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="duration" className="flex items-center">
                Duration <span className="text-destructive ml-1">*</span>
              </Label>
              <Input
                id="duration"
                placeholder="e.g., 7 Days, 2 Weeks"
                {...register("duration")}
                className={errors.duration ? "border-destructive" : ""}
              />
              {errors.duration && (
                <p className="text-destructive text-sm">
                  {errors.duration.message}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="dosage" className="flex items-center">
                Dosage <span className="text-destructive ml-1">*</span>
              </Label>
              <Input
                id="dosage"
                placeholder="e.g., 1 Tablet, 5ml"
                {...register("dosage")}
                className={errors.dosage ? "border-destructive" : ""}
              />
              {errors.dosage && (
                <p className="text-destructive text-sm">
                  {errors.dosage.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="frequency" className="flex items-center">
                Frequency <span className="text-destructive ml-1">*</span>
              </Label>
              <Input
                id="frequency"
                placeholder="e.g., Twice daily, 10mg"
                {...register("frequency")}
                className={errors.frequency ? "border-destructive" : ""}
              />
              {errors.frequency && (
                <p className="text-destructive text-sm">
                  {errors.frequency.message}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="instructions">Instructions (Optional)</Label>
            <Textarea
              id="instructions"
              placeholder="Add any special instructions or warnings"
              rows={4}
              {...register("instructions")}
            />
          </div>

          <div className="flex justify-end space-x-4 pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting} variant={"primary"}>
              {isSubmitting ? "Saving..." : isEditing ? "Update" : "Save"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default Prescription;
