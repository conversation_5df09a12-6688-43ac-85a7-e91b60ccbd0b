import { useState, useEffect, useCallback } from "react";
import { Loader2 } from "lucide-react";
import { getSoapFormByAppointmentId_api } from "../../api/api_calls/appointmentforms/soapform_apiCalls";

const PatientSOAPFilled = ({ appointmentId }) => {
  const [soapData, setSoapData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const SOAPSection = ({ title, items }) => (
    <section className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
      <h2 className="text-2xl font-bold text-[#1E1E1E] mb-4">{title}</h2>
      <div className="space-y-4">
        {items.map((item, index) => (
          <SOAPItem key={index} title={item.title} content={item.content} />
        ))}
      </div>
    </section>
  );

  const SOAPItem = ({ title, content }) => (
    <div className="space-y-2">
      <h3 className="text-[16px] font-medium text-[#1E1E1E]">{title}</h3>
      {Array.isArray(content) ? (
        <div className="space-y-1">
          {content.map((item, index) => (
            <p key={index} className="text-[#666970]">
              {item}
            </p>
          ))}
        </div>
      ) : (
        <p className="text-[#666970]">{content}</p>
      )}
    </div>
  );

  // Helper function to safely get value or return "N/A"
  const getValue = (value) => {
    if (value === null || value === undefined || value === "") {
      return "N/A";
    }
    return value;
  };

  // Function to fetch SOAP form data
  const fetchSoapFormData = useCallback(async () => {
    if (!appointmentId) {
      setError("No appointment ID provided");
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await getSoapFormByAppointmentId_api(appointmentId);
      if (response?.data) {
        setSoapData(response.data);
      } else {
        // No SOAP form found, but we'll still display the form with N/A values
        setSoapData(null);
      }
    } catch (error) {
      console.log("No SOAP form found:", error.message);
      // No SOAP form found, but we'll still display the form with N/A values
      setSoapData(null);
    } finally {
      setIsLoading(false);
    }
  }, [appointmentId]);

  // Fetch SOAP form data on component mount
  useEffect(() => {
    fetchSoapFormData();
  }, [fetchSoapFormData]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          <p className="text-gray-600">Loading SOAP form...</p>
        </div>
      </div>
    );
  }

  // Show error message only if there's no appointment ID
  if (error && error.includes("No appointment ID provided")) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="flex flex-col items-center gap-4 text-center">
          <p className="text-gray-600 text-lg">Unable to load SOAP form</p>
          <p className="text-gray-500 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  // Create SOAP data structure from API response (or N/A if no form exists)
  const formattedSoapData = {
    subjective: [
      {
        title: "CC (Chief Complaint)",
        content: getValue(soapData?.cc),
      },
      {
        title: "History of Present Illness (HPI)",
        content: getValue(soapData?.historyOfPatientIllness),
      },
      {
        title: "ROS (Review of Systems)",
        content: getValue(soapData?.ros),
      },
      {
        title: "PMH (Past Medical History)",
        content: getValue(soapData?.pmh),
      },
      {
        title: "PSH (Past Surgical History)",
        content: getValue(soapData?.psh),
      },
      {
        title: "Medications",
        content: getValue(soapData?.meds),
      },
      {
        title: "Allergies",
        content: getValue(soapData?.allergies),
      },
      {
        title: "FH (Family History)",
        content: getValue(soapData?.fh),
      },
      {
        title: "SH (Social History)",
        content: getValue(soapData?.sh),
      },
    ],
    objective: [
      {
        title: "Vital Signs",
        content: getValue(soapData?.vitalSigns),
      },
      {
        title: "General Appearance",
        content: getValue(soapData?.gen),
      },
      {
        title: "Cardiovascular",
        content: getValue(soapData?.heart),
      },
      {
        title: "Pulmonary",
        content: getValue(soapData?.lungs),
      },
      {
        title: "Abdomen",
        content: getValue(soapData?.abdomen),
      },
      {
        title: "Other Systems (ENT, Neck, Neuro, Extremities)",
        content: getValue(soapData?.otherSystem),
      },
      {
        title: "Osteopathic Structural Exam",
        content: getValue(soapData?.ose),
      },
    ],
    assessment: [
      {
        title: "Primary Problem (should reflect chief complaint)",
        content: getValue(soapData?.patientFirstProblem),
      },
      {
        title:
          "Secondary Problem (if present and pertinent to chief complaint)",
        content: getValue(soapData?.patientSecondProblem),
      },
      {
        title: "Additional Problems and Differentials",
        content: getValue(soapData?.patientThirdProblem),
      },
      {
        title: "Somatic Dysfunction",
        content: getValue(soapData?.somaticDysfunction),
      },
      {
        title: "Other Considerations",
        content: getValue(soapData?.other),
      },
    ],
    plan: [
      {
        title: "Symptomatic/Supportive Care",
        content: getValue(soapData?.sympotomatic),
      },
      {
        title: "Diagnostic Testing",
        content: getValue(soapData?.diagnosticTests),
      },
      {
        title: "Conditional Next Steps",
        content: getValue(soapData?.testsNextSteps),
      },
      {
        title: "Medications and Treatments",
        content: getValue(soapData?.prescribeMeds),
      },
      {
        title: "Osteopathic Manipulative Treatment",
        content: getValue(soapData?.omtPerformed),
      },
      {
        title: "Patient Education",
        content: getValue(soapData?.education),
      },
    ],
  };

  return (
    <div className="flex flex-col space-y-6 w-full mx-auto">
      <SOAPSection title="Subjective" items={formattedSoapData.subjective} />
      <SOAPSection title="Objective" items={formattedSoapData.objective} />
      <SOAPSection title="Assessment" items={formattedSoapData.assessment} />
      <SOAPSection title="Plan" items={formattedSoapData.plan} />
    </div>
  );
};

export default PatientSOAPFilled;
