import React, { useState, useEffect, useCallback } from "react";
import { useLocation } from "react-router-dom";
import { AlertCircle, Loader2 } from "lucide-react";
import { getPrescriptionFormByAppointmentId_apiCalls } from "../../api/api_calls/appointmentforms/prescriptionForm_apiCalls";
const PrescriptionFilled = () => {
  const location = useLocation();

  // Get appointmentId from location state
  const appointmentId =
    location.state?.appointmentId || location.state?.appointment?.id;

  const [prescriptions, setPrescriptions] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch existing prescription form data
  const fetchPrescriptionForm = useCallback(async () => {
    if (!appointmentId) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await getPrescriptionFormByAppointmentId_apiCalls({
        appointmentId,
      });

      if (response?.data) {
        // Handle both single prescription and array of prescriptions
        if (Array.isArray(response.data)) {
          setPrescriptions(response.data);
        } else if (
          response.data.prescriptions &&
          Array.isArray(response.data.prescriptions)
        ) {
          setPrescriptions(response.data.prescriptions);
        } else if (response.data.drugName) {
          setPrescriptions([response.data]);
        }
      }
    } catch (error) {
      console.error("Failed to fetch prescription form:", error);
      // Don't show error for missing prescription form, just show empty state
    } finally {
      setLoading(false);
    }
  }, [appointmentId]);

  // Effect to fetch prescription form on component mount
  useEffect(() => {
    fetchPrescriptionForm();
  }, [fetchPrescriptionForm]);

  // Loading state
  if (loading) {
    return (
      <div className="w-full">
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>Loading prescription data...</span>
        </div>
      </div>
    );
  }

  // No appointment ID provided
  if (!appointmentId) {
    return (
      <div className="w-full">
        <div className="flex flex-col items-center justify-center py-8 text-center">
          <AlertCircle className="w-12 h-12 text-muted-foreground mb-2" />
          <h3 className="text-lg font-medium">No Appointment ID</h3>
          <p className="text-sm text-muted-foreground">
            Unable to load prescription data without appointment information.
          </p>
        </div>
      </div>
    );
  }

  // Empty state
  if (prescriptions.length === 0) {
    return (
      <div className="w-full">
        <div className="flex w-full items-center justify-between flex-row mb-6">
          <h2 className="text-xl font-semibold">Prescription</h2>
        </div>
        <div className="flex flex-col items-center justify-center py-8 text-center border border-dashed rounded-lg">
          <AlertCircle className="w-12 h-12 text-muted-foreground mb-2" />
          <h3 className="text-lg font-medium">No prescriptions found</h3>
          <p className="text-sm text-muted-foreground">
            No prescriptions were added for this appointment.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex w-full items-center justify-between flex-row">
        <h2 className="text-xl font-semibold">Prescription</h2>
      </div>

      <div className="space-y-6 mt-6">
        {prescriptions.map((prescription, index) => (
          <div
            key={prescription.id || index}
            className="flex items-center justify-between w-full border-b-[1px] border-solid border-b-[1E1E1E80] py-4"
          >
            <div className="space-y-2">
              <p className="text-[14px] text-[#1E1E1EB2] font-medium">
                Drug Name:{" "}
                <span className="text-[14px] text-[#1E1E1E]">
                  {prescription.drugName || "Not specified"}
                </span>
              </p>
              <p className="text-[14px] text-[#1E1E1EB2] font-medium">
                Dosage: {""}
                <span className="text-[14px] text-[#1E1E1E]">
                  {prescription.dosage || "Not specified"}
                </span>
              </p>
              {prescription.instructions && (
                <div>
                  <p className="text-[14px] text-[#1E1E1EB2] font-medium">
                    Instructions:
                  </p>
                  <p className="text-[14px] text-[#1E1E1E]">
                    {prescription.instructions}
                  </p>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <p className="text-[14px] text-[#1E1E1EB2] font-medium">
                Duration:{" "}
                <span className="text-[14px] text-[#1E1E1E]">
                  {prescription.duration || "Not specified"}
                </span>
              </p>
              <p className="text-[14px] text-[#1E1E1EB2] font-medium">
                Frequency:{" "}
                <span className="text-[14px] text-[#1E1E1E]">
                  {prescription.frequency || "Not specified"}
                </span>
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-center gap-2"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default PrescriptionFilled;
