import React from "react";

const Insurance = () => {
  return (
    <div className="bg-white rounded-xl p-6 w-full border-solid border-[1px] border-border">
      <h2 className="text-xl font-semibold mb-4">Insurance Detail</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Insurance Info */}
        <div className="space-y-2">
          <p className="text-gray-600">Do You Have Insurance?</p>
          <p className="font-semibold">Yes</p>

          <p className="text-gray-600">Insurance Company Name</p>
          <p className="font-semibold">American Family Insurance</p>

          <p className="text-gray-600">Group Number</p>
          <p className="font-bold">#122344</p>
        </div>

        {/* Additional Info */}
        <div className="space-y-2">
          <p className="text-gray-600">Insurance Plan Type</p>
          <p className="font-semibold">Public Health Insurance</p>

          <p className="text-gray-600">Location</p>
          <p className="font-semibold">New York, USA</p>

          <p className="text-gray-600">Policy Number</p>
          <p className="font-bold">#234567</p>
        </div>
      </div>

      {/* Authorization and Consent */}
      <div className="mt-6 space-y-3">
        <p className="font-semibold text-gray-700">
          Authorization and Consent:{" "}
          <span className="text-sm text-gray-500">
            (Please read consent question below to the patient)
          </span>
        </p>
        <p className="text-gray-600">
          Do you authorize the release of any medical information necessary to
          process insurance claims?
        </p>
        <p className="font-semibold">Yes</p>

        <p className="text-gray-600">
          Do you understand that you will be financially responsible for all
          charges not covered by insurance?
        </p>
        <p className="font-semibold">Yes</p>
      </div>
    </div>
  );
};

export default Insurance;
