import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { getPreAppointmentFormByAppointmentId_apiCalls } from "../../api/api_calls/appointmentforms/preQuestioner_apiCalls";
import { useToast } from "../../hooks/use-toast";
import { Loader2 } from "lucide-react";
import { formatSymptomArray } from "../../helpers/symptomHelpers";

const PreQuestioner = () => {
  const [formData, setFormData] = useState(null);
  const [loading, setLoading] = useState(true);
  const location = useLocation();
  const { toast } = useToast();

  // Get appointmentId from location state or props
  const appointmentId =
    location.state?.appointmentId || location.state?.appointment?.id;
  // Fetch pre-appointment form data
  useEffect(() => {
    const fetchPreAppointmentForm = async () => {
      if (!appointmentId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await getPreAppointmentFormByAppointmentId_apiCalls({
          appointmentId,
        });

        if (response?.data) {
          setFormData(response.data);
        }
      } catch (error) {
        // Don't show error toast, just handle silently
      } finally {
        setLoading(false);
      }
    };

    fetchPreAppointmentForm();
  }, [appointmentId, toast]);
  // Reusable component for question-answer pairs

  const QuestionAnswer = ({
    question,
    answer,
    answerColor = "text-[#1E1E1EB2]",
  }) => (
    <div className="space-y-1">
      <h4 className="text-[16px] font-[600] text-[#1E1E1E]">{question}</h4>
      <p className={`text-[16px] font-[500] ${answerColor}`}>{answer}</p>
    </div>
  );

  // Reusable component for condition cards
  const ConditionCard = ({ title, items }) => (
    <div className="space-y-4">
      <h3 className="text-lg font-medium text-[#1E1E1E]">{title}</h3>
      {items.map((item, index) => (
        <div key={index} className="border border-[#E7E8E9] rounded-lg p-4">
          <div className="flex justify-between items-center">
            <p className="text-[#1E1E1E]">{item.question}</p>
            <p
              className={`font-medium ${item.answerColor || "text-[#1E1E1E]"}`}
            >
              {item.answer}
            </p>
          </div>
        </div>
      ))}
    </div>
  );

  // Reusable component for section headers
  const SectionHeader = ({ title }) => (
    <h2 className="text-[20px] font-bold text-[#1E1E1E] mb-4">{title}</h2>
  );

  // Reusable component for subsection headers
  const SubsectionHeader = ({ title }) => (
    <h3 className="text-[18px] font-[700] text-[#1E1E1E] mb-2">{title}</h3>
  );

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading pre-appointment form...</span>
      </div>
    );
  }

  // Helper functions with null safety
  const getBooleanDisplay = (value) => {
    if (value === null || value === undefined) return "N/A";
    return value ? "Yes" : "No";
  };

  const getStringDisplay = (value) => {
    if (value === null || value === undefined || value === "") return "N/A";
    return value;
  };

  const getArrayDisplay = (array) => {
    return formatSymptomArray(array);
  };

  return (
    <div className="flex flex-col space-y-6 w-full mx-auto">
      {/* Today's Visits Section */}
      <section className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
        <SectionHeader title="Today's Visits" />

        <div className="space-y-4">
          <QuestionAnswer
            question="Do you want medical Certificate?"
            answer={getBooleanDisplay(formData?.isMedCertificateRequested)}
          />

          <QuestionAnswer
            question="What are you hoping to accomplish today?"
            answer={getStringDisplay(formData?.agendaForAppointment)}
          />

          <QuestionAnswer
            question="Is there anything else you'd like to work on to improve your health?"
            answer={getStringDisplay(formData?.goalForHealth)}
          />

          <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 2xl:grid-cols-2 gap-4 mt-6">
            <ConditionCard
              title="Diabetes"
              items={[
                {
                  question: "Any problems with medications?",
                  answer: getBooleanDisplay(
                    formData?.diabetes_isProblemWithMedication,
                  ),
                  answerColor:
                    formData?.diabetes_isProblemWithMedication === true
                      ? "text-blue-500"
                      : formData?.diabetes_isProblemWithMedication === false
                        ? "text-red-500"
                        : "text-gray-500",
                },
                {
                  question: "Home glucose readings",
                  answer: getStringDisplay(
                    formData?.diabetes_homeGlucoseReadings,
                  ),
                },
              ]}
            />

            <ConditionCard
              title="High blood pressure"
              items={[
                {
                  question: "Any problems with medications?",
                  answer: getBooleanDisplay(
                    formData?.highBloodPressure_isProblemWithMedication,
                  ),
                  answerColor:
                    formData?.highBloodPressure_isProblemWithMedication === true
                      ? "text-blue-500"
                      : formData?.highBloodPressure_isProblemWithMedication ===
                          false
                        ? "text-red-500"
                        : "text-gray-500",
                },
                {
                  question: "Home BP readings",
                  answer: getStringDisplay(
                    formData?.highBloodPressure_homeBpReadings,
                  ),
                },
              ]}
            />

            <ConditionCard
              title="Depression"
              items={[
                {
                  question: "Any problems with medications?",
                  answer: getBooleanDisplay(
                    formData?.depression_isProblemWithMedication,
                  ),
                  answerColor:
                    formData?.depression_isProblemWithMedication === true
                      ? "text-blue-500"
                      : formData?.depression_isProblemWithMedication === false
                        ? "text-red-500"
                        : "text-gray-500",
                },
                {
                  question: "Any suicidal thoughts?",
                  answer: getBooleanDisplay(
                    formData?.depression_isSuicidalThoughts,
                  ),
                  answerColor:
                    formData?.depression_isSuicidalThoughts === true
                      ? "text-red-500"
                      : formData?.depression_isSuicidalThoughts === false
                        ? "text-green-500"
                        : "text-gray-500",
                },
              ]}
            />

            <ConditionCard
              title="High cholesterol"
              items={[
                {
                  question: "Any problems with medications?",
                  answer: getBooleanDisplay(
                    formData?.highCholesterol_isProblemWithMedication,
                  ),
                  answerColor:
                    formData?.highCholesterol_isProblemWithMedication === true
                      ? "text-blue-500"
                      : formData?.highCholesterol_isProblemWithMedication ===
                          false
                        ? "text-red-500"
                        : "text-gray-500",
                },
              ]}
            />
          </div>
        </div>
      </section>

      {/* Between Visits Section */}
      <section className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
        <SectionHeader title="Between Visits" />
        <QuestionAnswer
          question="Have you been to the ER, hospital, or another doctor since last seen here?"
          answer={
            formData?.otherMedVisit === true
              ? getStringDisplay(formData?.otherMedVisit_details)
              : getBooleanDisplay(formData?.otherMedVisit)
          }
        />
      </section>

      {/* Lifestyle Section */}
      <section className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
        <SectionHeader title="Lifestyle" />

        <div className="space-y-6">
          {/* Exercise */}
          <div>
            <SubsectionHeader title="Exercise:" />
            <div className="space-y-4">
              <QuestionAnswer
                question="What do you do?"
                answer={getStringDisplay(formData?.exercice)}
              />

              <QuestionAnswer
                question="Can you walk a block or climb a flight of stairs without getting short of breath?"
                answer={
                  formData?.isShortnessOfBreath === true
                    ? "No"
                    : formData?.isShortnessOfBreath === false
                      ? "Yes"
                      : "N/A"
                }
              />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <QuestionAnswer
                  question="How long?"
                  answer={getStringDisplay(formData?.exercice_howLong)}
                />
                <QuestionAnswer
                  question="How Often?"
                  answer={getStringDisplay(formData?.exercice_howOften)}
                />
              </div>
            </div>
          </div>

          {/* Smoking */}
          <div>
            <SubsectionHeader title="Smoking:" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <QuestionAnswer
                question="Are you interested in quitting?"
                answer={getBooleanDisplay(
                  formData?.smoking_isInterestedInQuitting,
                )}
              />
              <QuestionAnswer
                question="How much do you smoke?"
                answer={getStringDisplay(formData?.smoking_howMuch)}
              />
            </div>
          </div>

          {/* Falls */}
          <div>
            <SubsectionHeader title="Falls:" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <QuestionAnswer
                question="Have you fallen in the past year?"
                answer={getBooleanDisplay(formData?.fallenInPastyear)}
              />
              <QuestionAnswer
                question="Do you have problems with walking or balance?"
                answer={getBooleanDisplay(formData?.problemWithWalkingBalance)}
              />
            </div>
          </div>

          {/* Alcohol */}
          <div>
            <SubsectionHeader title="Alcohol:" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <QuestionAnswer
                question="How many drinking days do you have per week?"
                answer={getStringDisplay(formData?.alcohol_numberOfDrinks_week)}
              />
              <QuestionAnswer
                question="On average how many drinks per drinking day?"
                answer={getStringDisplay(formData?.alcohol_numberOfDrinks_day)}
              />
              <QuestionAnswer
                question="Have you had more than 4 drinks in a day in the past 3 months?"
                answer={getBooleanDisplay(formData?.alcohol_moreThanFourDrinks)}
              />
              <QuestionAnswer
                question="Are you or others concerned about your drinking?"
                answer={getBooleanDisplay(formData?.alcohol_othersConcerned)}
              />
            </div>
          </div>

          {/* Sleep */}
          <div>
            <SubsectionHeader title="Sleep:" />
            <QuestionAnswer
              question="Do you stop breathing during sleep or have concerns about sleep apnea?"
              answer={getBooleanDisplay(formData?.isSleepApnea)}
            />
          </div>

          {/* Depression screen */}
          <div>
            <SubsectionHeader title="Depression screen:" />
            <QuestionAnswer
              question="Over the last 2 weeks have you been bothered by little interest or pleasure in doing things, or feeling down, hopeless, or depressed?"
              answer={getBooleanDisplay(formData?.isFeelingDown)}
            />
          </div>

          {/* Medication */}
          <div>
            <SubsectionHeader title="Medication:" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <QuestionAnswer
                question="Do you have any trouble taking any of your medication?"
                answer={getBooleanDisplay(formData?.isProblemWithMedication)}
              />
              <QuestionAnswer
                question="If so, what sort of trouble?"
                answer={getStringDisplay(
                  formData?.problemWithMedicationDetails,
                )}
              />
            </div>
          </div>

          {/* Bladder Control */}
          <div>
            <SubsectionHeader title="Bladder Control:" />
            <QuestionAnswer
              question="Do you lose control of your urine to the point you would like to know how to treat it?"
              answer={getBooleanDisplay(formData?.isProblemBladderControl)}
            />
          </div>

          {/* End-of-life care */}
          <div>
            <SubsectionHeader title="End-of-life care:" />
            <QuestionAnswer
              question="Have you thought about or discussed your preferences for end-of-life care?"
              answer={getBooleanDisplay(formData?.discussEndOfLifeCare)}
            />
          </div>

          {/* Safety */}
          <div>
            <SubsectionHeader title="Safety:" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <QuestionAnswer
                question="Are you in a relationship where you feel unsafe or have been hurt?"
                answer={getBooleanDisplay(formData?.isUnsafeRelationship)}
              />
              <QuestionAnswer
                question="Do you regularly wear a seatbelt?"
                answer={getBooleanDisplay(formData?.isWearSeatbelt)}
              />
            </div>
          </div>

          {/* HIV testing */}
          <div>
            <SubsectionHeader title="HIV testing:" />
            <QuestionAnswer
              question="Would you like HIV testing?"
              answer={getBooleanDisplay(formData?.isHivTestRequested)}
            />
          </div>

          {/* Caffeine */}
          <div>
            <SubsectionHeader title="Caffeine:" />
            <QuestionAnswer
              question="How much caffeine do you consume per day? (e.g., coffee, tea, chocolate, soda)"
              answer={getStringDisplay(formData?.caffeinePerDay)}
            />
          </div>

          {/* Birth control */}
          <div>
            <SubsectionHeader title="Birth control:" />
            <QuestionAnswer
              question="Birth control method (if applicable)"
              answer={getStringDisplay(formData?.birthControlMethod)}
            />
          </div>
        </div>
      </section>

      {/* Update Section */}
      <section className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
        <SectionHeader title="Update" />

        <div className="space-y-6">
          <QuestionAnswer
            question="Has anything new come up in your family history? (new illness among blood relatives)"
            answer={getStringDisplay(formData?.newIllnessInFamily)}
          />

          <QuestionAnswer
            question="Have you developed any new drug allergies?"
            answer={getStringDisplay(formData?.newDrugAllergies)}
          />

          {/* Systems Review */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <SubsectionHeader title="Constitutional symptoms:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.constitutionalSymptoms)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Cardiovascular:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.cardiovascularProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Respiratory:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.respiratoryProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Neurological:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.neurologicalProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Skin:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.skinProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Musculoskeletal:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.musculoskeletalProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Endocrine:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.endocrineProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Allergic:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.allergicProblems)}
                </p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <SubsectionHeader title="Eyes:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.eyeProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Ears, nose, mouth, and throat:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.enmtProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Gastrointestinal:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.gastrointestinalProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Genitourinary:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.genitourinaryProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Sleep:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.sleepProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Psychiatric:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.psychiatricProblems)}
                </p>
              </div>

              <div>
                <SubsectionHeader title="Hematologic:" />
                <p className="text-[16px] font-[500] text-[#1E1E1EB2]">
                  {getArrayDisplay(formData?.hematologicProblems)}
                </p>
              </div>
            </div>
          </div>

          <QuestionAnswer
            question="Please identify any issues above which are new or that you specifically want to address."
            answer="Based on the symptoms reported above"
          />
        </div>
      </section>
    </div>
  );
};

export default PreQuestioner;
