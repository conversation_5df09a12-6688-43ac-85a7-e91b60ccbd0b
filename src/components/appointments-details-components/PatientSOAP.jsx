"use client";
import { useState, useEffect, useCallback } from "react";
import { Textarea } from "../../components/ui/textarea";
import { <PERSON><PERSON> } from "../../components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "../../hooks/use-toast";
import { Loader2 } from "lucide-react";
import {
  createSoapForm_api,
  updateSoapForm_api,
  getSoapFormByAppointmentId_api,
} from "../../api/api_calls/appointmentforms/soapform_apiCalls";

const formSchema = z.object({
  cc: z.string().min(1, { message: "Chief complaint is required" }),
  historyOfPatientIllness: z.string().min(1, {
    message: "History of present illness is required",
  }),
  ros: z.string().min(1, {
    message: "Review of systems is required",
  }),
  pmh: z.string().min(1, {
    message: "Past medical history is required",
  }),
  psh: z.string().min(1, {
    message: "Past surgical history is required",
  }),
  meds: z.string().min(1, {
    message: "Medications are required",
  }),
  allergies: z.string().min(1, {
    message: "Allergies are required",
  }),
  fh: z.string().min(1, {
    message: "Family history is required",
  }),
  sh: z.string().min(1, {
    message: "Social history is required",
  }),
  vitalSigns: z.string().min(1, {
    message: "Vital signs are required",
  }),
  gen: z.string().min(1, {
    message: "General appearance is required",
  }),
  heart: z.string().min(1, {
    message: "Heart examination findings are required",
  }),
  lungs: z.string().min(1, {
    message: "Lung examination findings are required",
  }),
  abdomen: z.string().min(1, {
    message: "Abdomen examination findings are required",
  }),
  otherSystem: z.string().min(1, {
    message: "Other system examination findings are required",
  }),
  ose: z.string().min(1, {
    message: "Osteopathic structural exam findings are required",
  }),
  patientFirstProblem: z.string().min(1, {
    message: "First problem assessment is required",
  }),
  patientSecondProblem: z.string().min(1, {
    message: "Second problem assessment is required",
  }),
  patientThirdProblem: z.string().min(1, {
    message: "Third problem assessment is required",
  }),
  somaticDysfunction: z.string().min(1, {
    message: "Somatic dysfunction assessment is required",
  }),
  other: z.string().min(1, {
    message: "Other assessment findings are required",
  }),
  sympotomatic: z.string().min(1, {
    message: "Symptomatic care is required",
  }),
  diagnosticTests: z.string().min(1, {
    message: "Lab or imaging findings are required",
  }),
  testsNextSteps: z.string().min(1, {
    message: "Next steps are required",
  }),
  prescribeMeds: z.string().min(1, {
    message: "Medications plan is required",
  }),
  omtPerformed: z.string().min(1, {
    message: "OMT performed is required",
  }),
  education: z.string().min(1, {
    message: "Patient education is required",
  }),
});

const PatientSOAP = ({ appointmentId }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [existingSoapForm, setExistingSoapForm] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      cc: "",
      historyOfPatientIllness: "",
      ros: "",
      pmh: "",
      psh: "",
      meds: "",
      allergies: "",
      fh: "",
      sh: "",
      vitalSigns: "",
      gen: "",
      heart: "",
      lungs: "",
      abdomen: "",
      otherSystem: "",
      ose: "",
      patientFirstProblem: "",
      patientSecondProblem: "",
      patientThirdProblem: "",
      somaticDysfunction: "",
      other: "",
      sympotomatic: "",
      diagnosticTests: "",
      testsNextSteps: "",
      prescribeMeds: "",
      omtPerformed: "",
      education: "",
    },
  });

  // Function to fetch existing SOAP form data
  const fetchExistingSoapForm = useCallback(async () => {
    if (!appointmentId) return;

    setIsLoading(true);
    try {
      const response = await getSoapFormByAppointmentId_api(appointmentId);
      if (response?.data) {
        setExistingSoapForm(response.data);
        setIsEditMode(true);

        // Populate form with existing data
        const soapData = response.data;
        form.reset({
          cc: soapData.cc || "",
          historyOfPatientIllness: soapData.historyOfPatientIllness || "",
          ros: soapData.ros || "",
          pmh: soapData.pmh || "",
          psh: soapData.psh || "",
          meds: soapData.meds || "",
          allergies: soapData.allergies || "",
          fh: soapData.fh || "",
          sh: soapData.sh || "",
          vitalSigns: soapData.vitalSigns || "",
          gen: soapData.gen || "",
          heart: soapData.heart || "",
          lungs: soapData.lungs || "",
          abdomen: soapData.abdomen || "",
          otherSystem: soapData.otherSystem || "",
          ose: soapData.ose || "",
          patientFirstProblem: soapData.patientFirstProblem || "",
          patientSecondProblem: soapData.patientSecondProblem || "",
          patientThirdProblem: soapData.patientThirdProblem || "",
          somaticDysfunction: soapData.somaticDysfunction || "",
          other: soapData.other || "",
          sympotomatic: soapData.sympotomatic || "",
          diagnosticTests: soapData.diagnosticTests || "",
          testsNextSteps: soapData.testsNextSteps || "",
          prescribeMeds: soapData.prescribeMeds || "",
          omtPerformed: soapData.omtPerformed || "",
          education: soapData.education || "",
        });
      }
    } catch (error) {
      // If SOAP form doesn't exist, that's fine - we'll create a new one
      console.log("No existing SOAP form found, creating new one");
    } finally {
      setIsLoading(false);
    }
  }, [appointmentId, form]);

  // Fetch existing SOAP form data on component mount
  useEffect(() => {
    fetchExistingSoapForm();
  }, [fetchExistingSoapForm]);

  const onSubmit = async (data) => {
    if (!appointmentId) {
      toast({
        description: "Appointment ID is required",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      console.log("SOAP form submitted with data:", data);

      let response;
      if (isEditMode && existingSoapForm) {
        // Update existing SOAP form
        response = await updateSoapForm_api({
          id: existingSoapForm.id,
          ...data,
        });
        toast({
          description: "SOAP form updated successfully",
        });
      } else {
        // Create new SOAP form
        response = await createSoapForm_api({
          appointmentId,
          ...data,
        });
        toast({
          description: "SOAP form created successfully",
        });
        setIsEditMode(true);
        setExistingSoapForm(response.data);
      }

      console.log("SOAP form API response:", response);
    } catch (error) {
      toast({
        description: error.message || "Failed to save SOAP form",
        variant: "destructive",
      });
      console.error("ERROR IN SOAP FORM SAVE => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state while fetching existing data
  if (isLoading) {
    return (
      <div className="w-full space-y-6">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <p className="text-gray-600">Loading SOAP form...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full space-y-6">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          {/* Subjective */}
          <div className="space-y-4 border-solid border-[1px] border-[#D9D9D9] p-4 rounded-lg">
            <h3 className="text-lg font-bold">Subjective</h3>

            {/* CC */}
            <FormField
              control={form.control}
              name="cc"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    CC <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder='Patient&apos;s own words about why he or she came to clinic, in "quotes" or paraphrased.'
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* History of Present Illness */}
            <FormField
              control={form.control}
              name="historyOfPatientIllness"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    History of Present Illness{" "}
                    <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Use preferred mnemonic to gather all information pertinent to cc. Written in narrative form (preferred). Bullets OK, but do NOT list finding after mnemonic."
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* ROS */}
            <FormField
              control={form.control}
              name="ros"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    ROS (Review of Systems){" "}
                    <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Relevant to chief complaint and related to differential diagnoses. (Pertinent positives and negatives are placed at end of HPI.)"
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* PMH */}
            <FormField
              control={form.control}
              name="pmh"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    PMH (Past Medical History){" "}
                    <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Patient's current medical conditions and/or resolved medical conditions. Include dates. (Specificity is important for problems related to HPI.)*** "
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* PSH */}
            <FormField
              control={form.control}
              name="psh"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    PSH (Past Surgical History){" "}
                    <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Surgical procedure/s performed on patient. Year of surgery is helpful, but not mandatory. List surgery as specific as patient is able to give. Unknown? Document: Unknown abdominal surgery, 2017"
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Meds */}
            <FormField
              control={form.control}
              name="meds"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Meds <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Medications (or supplements or vitamins) the patient is taking. DOSE and INTERVAL are mandatory. Patient doesn't know? Document: Unknown dose. Interviewer forgets to ask? Write: Not assessed."
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Allergies */}
            <FormField
              control={form.control}
              name="allergies"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Allergies <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="To medications, food and/or environment. REACTION is mandatory."
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* FH */}
            <FormField
              control={form.control}
              name="fh"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    FH (Family History) <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Medical history of first-degree relatives (father, mother, siblings, children). STATE AGE AT TIME OF DEATH IF THEY ARE DECEASED. Ages of living relatives are helpful, but not mandatory."
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* SH */}
            <FormField
              control={form.control}
              name="sh"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    SH (Social History) <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Use of tobacco products, alcohol, illicit drugs. TYPE OF TOBACCO, CURRENT OR PAST USAGE, TYPE OF ALCOHOL, AMOUNT AND FREQUENCY AND TYPE OF DRUG/S are mandatory. Also include occupation and relationship status. Others: Diet, exercise, hobbies, caffeine intake, religion, living situation—are good if you think of it, but they are not mandatory (unless pertinent to cc)."
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Objective */}
          <div className="space-y-4 border-solid border-[1px] border-[#D9D9D9] p-4 rounded-lg">
            <h3 className="text-lg font-bold">Objective</h3>

            {/* Vital Signs */}
            <FormField
              control={form.control}
              name="vitalSigns"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Vital Signs <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="HR (beats per minute) RR (breaths per minute) T (degrees Fahrenheit) BP (systolic/diastolic)—provided on Clinic door note for SP Encounter. (Note that others pertinent to the patient's complaint—Height, weight, BMI, oxygen saturation may be included.)"
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* GEN */}
            <FormField
              control={form.control}
              name="gen"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    GEN <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Brief description of patient (Distress? No distress? Anxious? Hunched over trying to breathe?. . etc.)."
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Heart */}
            <FormField
              control={form.control}
              name="heart"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Heart <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Heart sounds, S1 and S2, murmurs, rubs, gallops; pulses and capillary refill (if pertinent to cc)."
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Lungs */}
            <FormField
              control={form.control}
              name="lungs"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Lungs <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Breath sounds described and location noted in lung (ex: Anterior, apical, basilar, etc.)"
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Abdomen */}
            <FormField
              control={form.control}
              name="abdomen"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Abdomen (if needed) <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Abdominal examination findings including inspection, auscultation, percussion, and palpation."
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Other System */}
            <FormField
              control={form.control}
              name="otherSystem"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Other System- E.g.- ENT, Neck, Skin, Neuro, Extremity{" "}
                    <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Findings from exam directed by cc—COMPARISON to unaffected side, if appropriate, is mandatory. TWO SPECIAL TESTS are required, if appropriate to cc. Also: Neuro (sensation, reflexes or Cranial Nerve exam—if pertinent to cc)."
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* OSE */}
            <FormField
              control={form.control}
              name="ose"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    OSE <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Description of Osteopathic Structural Exam findings. (If OSE final, perform an OMT treatment and reassess.)"
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Assessment */}
          <div className="space-y-4 border-solid border-[1px] border-[#D9D9D9] p-4 rounded-lg">
            <h3 className="text-lg font-bold">Assessment</h3>

            {/* First Problem */}
            <FormField
              control={form.control}
              name="patientFirstProblem"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Patient&apos;s first problem (should reflect cc); state how
                    clinical findings support 1st, 2nd DDX{" "}
                    <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="a. First differential diagnosis &#10;b. Second differential diagnosis &#10;c. Third differential diagnosis"
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Second Problem */}
            <FormField
              control={form.control}
              name="patientSecondProblem"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Patient&apos;s second problem (if present; reflect cc if
                    pertinent); state how clinical findings support 1st, 2nd
                    DDX. <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="a. First differential diagnosis &#10;b. Second differential diagnosis &#10;c. Third differential diagnosis"
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Third Problem */}
            <FormField
              control={form.control}
              name="patientThirdProblem"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Patient&apos;s third problem and differentials, if
                    appropriate <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="OR current medical condition of patient or significant family history, especially if pertinent to chief complaint."
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Somatic Dysfunction */}
            <FormField
              control={form.control}
              name="somaticDysfunction"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Somatic dysfunction, body area{" "}
                    <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="(Example: Somatic Dysfunction, Thorax)—if OSE is performed and patient is treated with OMT or instructed to return for re-evaluation"
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Other Assessment */}
            <FormField
              control={form.control}
              name="other"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Other <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="lab/imaging abnormality available during encounter or abnormal physical finding (can be unrelated to cc)."
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Plan */}
          <div className="space-y-4 border-solid border-[1px] border-[#D9D9D9] p-4 rounded-lg">
            <h3 className="text-lg font-bold">Plan</h3>

            {/* Symptomatic Care */}
            <FormField
              control={form.control}
              name="sympotomatic"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Symptomatic/supportive care (fever reducers, pain relievers,
                    fluids, etc.) <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder=""
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Lab Imaging */}
            <FormField
              control={form.control}
              name="diagnosticTests"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Lab, imaging (be specific), other diagnostic test/s;
                    (include why test is being done).{" "}
                    <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder=""
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Next Steps */}
            <FormField
              control={form.control}
              name="testsNextSteps"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    If certain tests are positive or negative, include next
                    steps. <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder=""
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Medications */}
            <FormField
              control={form.control}
              name="prescribeMeds"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Prescribe meds, continue current meds, other actions
                    pertinent to current medical condition etc., etc. etc{" "}
                    <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder=""
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* OMT Performed */}
            <FormField
              control={form.control}
              name="omtPerformed"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    OMT performed (be specific) -(only if Somatic Dysfunction is
                    an assessment. May state: Return for re-evaluation){" "}
                    <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder=""
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Education */}
            <FormField
              control={form.control}
              name="education"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="text-gray-700 font-medium">
                    Education for problem that should be addressed (can be
                    unrelated to cc) <span className="text-red-600">*</span>
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder=""
                      rows={3}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <h1 className="text-2xl font-bold text-gray-800 mb-4 bg-[#0052FD08] p-4">
            Suggestion/Notes
          </h1>

          <div className="space-y-4 border-solid border-[1px] border-[#D9D9D9] p-4 rounded-lg">
            <ul className="list-disc pl-6 space-y-2 text-gray-700 mb-6">
              <li>
                Name and DOB should be included in the Subjective Box for the SP
                Encounter.
              </li>
              <li>
                All of the abbreviations used in this Road Map are COMLEX
                approved. When in doubt? Spell it out.
              </li>
              <li>
                You must always ask about prescription medications taken; also,
                dose and frequency.
              </li>
              <li>
                You must always ask about allergies to medications. You must
                always get the reaction.
              </li>
              <li>
                For lung exam: FOUR listening posts anteriorly, TWELVE
                posteriorly on bare skin (Eight upper, middle and lower in
                middle; two laterally, two axillary region). Patient must be
                instructed to breathe in and out through an open mouth.
              </li>
              <li>
                For heart exam: Four listening posts (aortic, pulmonic,
                tricuspid and mitral valves) on bare skin.
              </li>
              <li>
                <strong>PMH (example)</strong>: If patient&apos;s HPI is about
                chest pain and patient has had ischemic heart disease, student
                may document: &quot;Coronary artery disease, status post
                angioplasty of mid LAD in 2009. Follow-up nuclear stress test
                negative in 2015.&quot;
              </li>
              <li>
                Differential diagnosis will also dictate what parts of the body
                the student examines– E.g– If a patient comes in with a cough,
                both the ENT and the lung exam should be undertaken.
              </li>
              <li>
                Assessment list should include items such as: Problem that can
                kill the patient; Social and PMH items that are pertinent to
                this case or if out of normal control; Abnormal Labs, Abnormal
                X-rays, and Exam abnormalities.
              </li>
              <li>
                Most important items are listed first in the Assessment List.
                Then take others into consideration.
              </li>
            </ul>

            <h2 className="font-normal text-gray-800 mb-4 bg-[#0052FD08] p-2">
              Examples
            </h2>

            <div className="pl-4 mb-6 text-gray-700">
              <p>PMH – Hypertension – BP not at goal on this visit.</p>
              <p>
                Important item – Ischemia on EKG – incidentally found on patient
                with viral syndrome.
              </p>
              <p>
                Surprise lab – Volume depletion with prerenal azotemia with
                Creatinine of 2.0.
              </p>
              <p>Xray finding – 2cm LLL lung nodule on Chest x-ray.</p>
              <p>PE finding – New murmur on exam.</p>
            </div>

            <div className="flex justify-end space-x-4 border-t pt-4">
              <Button
                variant="outline"
                className="bg-gray-100 text-gray-700 hover:bg-gray-200"
                type="button"
                onClick={() => form.reset()}
              >
                Cancel
              </Button>
              <Button
                className="bg-blue-600 text-white hover:bg-blue-700"
                type="submit"
                disabled={isSubmitting || isLoading}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {isEditMode ? "Updating..." : "Creating..."}
                  </>
                ) : isEditMode ? (
                  "Update SOAP Form"
                ) : (
                  "Create SOAP Form"
                )}
              </Button>
            </div>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default PatientSOAP;
