import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";
import { ReactComponent as NoLogsIllustration } from "../../assets/svgs/NoLogsIllustration.svg";
import { useSelector } from "react-redux";

const PatientLogs = ({ logs = [] }) => {
  const user = useSelector((state) => state.userReducer.user);

  // Ensure logs is always an array
  const logsArray = Array.isArray(logs) ? logs : [];

  // Show empty state if no logs
  if (logsArray.length === 0) {
    return (
      <div className="space-y-6 p-4 w-full items-center justify-center flex">
        <div className="text-center py-8">
          <NoLogsIllustration />
          <h1 className="font-bold text-lg mt-4">
            No logs yet.
            <span>
              {user?.userType === "patient" ? "Add your logs" : "patient"}
            </span>
          </h1>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-4">
      <Table>
        <TableHeader className="bg-[#F8F8F8]">
          <TableRow>
            <TableHead className="font-medium text-gray-700">Date</TableHead>
            <TableHead className="font-medium text-gray-700">Notes</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {logsArray.map((log) => {
            const logDate = log.createdAt ? new Date(log.createdAt) : (log.date ? new Date(log.date) : new Date());
            const formattedDate = logDate.toLocaleDateString("en-US", {
              day: "2-digit",
              month: "2-digit",
              year: "numeric",
            });

            return (
              <TableRow key={log.id}>
                <TableCell>{formattedDate}</TableCell>

                <TableCell className="max-w-xs truncate">{log.log || log.notes}</TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};

export default PatientLogs;
