import React from "react";
import LoadingSpinner from "../../components/animations/LoadingSpinner";
import { formatDateReadable } from "../../helpers/dateTimeHelpers";
const DoctorNotes = ({ notes = [], isLoading = false }) => {
  // Show loading state if data is being fetched
  if (isLoading) {
    return (
      <div className="space-y-6 flex justify-center items-center min-h-[200px]">
        <div className="text-center">
          <LoadingSpinner size="lg" className="mb-4" />
          <p className="text-muted-foreground">Loading doctor notes...</p>
        </div>
      </div>
    );
  }
  // Show empty state if no notes
  if (notes.length === 0) {
    return (
      <div className="space-y-6">
        <div className="w-full p-4 border-solid border-[1px] border-[#E7E8E9] rounded-lg text-center">
          <p className="font-[500] text-[#666970] text-[16px]">
            No doctor notes available yet. Click the edit button to add your
            first note.
          </p>
        </div>
      </div>
    );
  }
  return (
    <div className="space-y-4">
      {notes.map((note) => {
        const formattedDate = formatDateReadable(note.createdAt);
        const formattedTime =
          note.time ||
          new Date(note.createdAt || new Date()).toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: true,
          });

        return (
          <div
            key={note.id}
            className="w-full p-4 border-solid border-[1px] border-[#E7E8E9] rounded-lg"
          >
            <div className="flex items-center justify-between mb-2">
              <h6 className="font-semibold text-gray-700 text-sm">
                {formattedDate} {formattedTime}
              </h6>
            </div>
            <p className="font-[500] text-[#666970] text-[16px]">{note.log}</p>
          </div>
        );
      })}
    </div>
  );
};
export default DoctorNotes;
