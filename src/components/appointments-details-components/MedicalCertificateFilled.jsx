import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import <PERSON><PERSON>ob<PERSON>Logo from "../../assets/svgs/DocMobilLogoWithText.svg";
import SignPlaceholder from "../../assets/svgs/SignPlaceholder.svg";
import { getMedicalCertificateByAppointmentId_apiCalls } from "../../api/api_calls/appointmentforms/medicalCertificate_apiCalls";
import { Loader2 } from "lucide-react";

const MedicalCertificateFilled = ({ appointmentId }) => {
  const [loading, setLoading] = useState(true);
  const [certificateData, setCertificateData] = useState(null);
  const location = useLocation();

  // Get appointmentId from props or location state as fallback
  const finalAppointmentId =
    appointmentId ||
    location.state?.appointmentId ||
    location.state?.appointment?.id;

  // Fetch medical certificate data
  useEffect(() => {
    const fetchMedicalCertificate = async () => {
      if (!finalAppointmentId) {
        console.log("No appointment ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log(
          "Fetching medical certificate for appointmentId:",
          finalAppointmentId,
        );

        const response = await getMedicalCertificateByAppointmentId_apiCalls({
          appointmentId: finalAppointmentId,
        });

        console.log("Medical certificate API response:", response);

        if (response?.data) {
          setCertificateData(response.data);
          console.log("Medical certificate data loaded:", response.data);
        } else {
          console.log("No medical certificate found for this appointment");
        }
      } catch (error) {
        console.error("Failed to fetch medical certificate:", error);
        // Don't set error state, just log it
      } finally {
        setLoading(false);
      }
    };

    fetchMedicalCertificate();
  }, [finalAppointmentId]);

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading medical certificate...</span>
      </div>
    );
  }

  // Use certificateData or empty placeholders to prevent crashes
  const displayData = certificateData || {
    id: null,
    appointmentId: finalAppointmentId,
    diagnosis: null,
    recommendations: null,
    createdAt: null,
    updatedAt: null,
    appointment: {
      doctor: {
        firstName: null,
        lastName: null,
        medicalLicenseNumber: null,
        contactNumber: null,
        address: null,
        signature: {
          url: null,
        },
      },
    },
  };

  return (
    <div className="p-6 border rounded-lg shadow-sm w-full mx-auto bg-white relative">
      {/* DocMobil Watermark */}
      <div className="absolute inset-0 flex items-center justify-center opacity-10 pointer-events-none">
        <img src={DocMobilLogo || "/placeholder.svg"} alt="" className="w-96" />
      </div>

      {/* Certificate Content */}
      <div className="relative z-10">
        <h1 className="text-2xl font-bold mb-2">Medical Certificate</h1>
        <h2 className="text-xl font-bold mb-4">National Hospital, Lahore</h2>

        <hr className="border-blue-500 mb-6" />

        <div className="space-y-2 mb-6">
          <p className="text-xl font-bold text-[#1E1E1E]">
            <span className="text-[#1E1E1EB2]">Date : </span>
            {displayData?.createdAt
              ? new Date(displayData.createdAt).toLocaleDateString()
              : new Date().toLocaleDateString()}
          </p>
          <p className="text-xl font-bold text-[#1E1E1E]">
            <span className="text-[#1E1E1EB2]">Certificate ID : </span>
            {displayData?.id || "N/A"}
          </p>
          <p className="text-xl font-bold text-[#1E1E1E]">
            <span className="text-[#1E1E1EB2]">Appointment ID : </span>
            {displayData?.appointmentId || finalAppointmentId || "N/A"}
          </p>
        </div>

        <p className="text-base text-[#1E1E1E] font-semibold mb-6">
          This is to certify that the patient was examined and treated at our
          clinic/hospital on{" "}
          <span className="text-[#1E1E1E] text-xl font-bold">
            {displayData?.createdAt
              ? new Date(displayData.createdAt).toLocaleDateString()
              : new Date().toLocaleDateString()}
          </span>
          . The patient has been diagnosed with the following medical condition:
        </p>

        <div className="mb-6">
          <h3 className="text-xl font-bold text-[#1E1E1E] mb-2">
            Medical Condition/Diagnosis:
          </h3>
          <p className="text-base text-[#1E1E1E] font-semibold">
            {displayData?.diagnosis || "N/A"}
          </p>
        </div>

        <div className="mb-6">
          <h3 className="text-xl font-bold text-[#1E1E1E] mb-2">
            Recommendations:
          </h3>
          <p className="text-base text-[#1E1E1E] font-semibold">
            {displayData?.recommendations || "N/A"}
          </p>
        </div>

        <div className="mt-12">
          <p className="text-xl font-bold text-[#1E1E1E] mb-2">
            Authorized Medical Officer
          </p>
          <p className="text-xl font-bold text-[#1E1E1EB2] mb-2">
            Medical Certificate Authority
          </p>
          <div className="font-script text-lg">
            <img
              alt="Medical Officer Signature"
              src={displayData?.appointment?.doctor?.signature?.url}
            />
          </div>
          <p className="text-sm text-gray-500 mt-2">
            Certificate ID: {displayData?.id || "N/A"}
          </p>
        </div>
      </div>
    </div>
  );
};

export default MedicalCertificateFilled;
