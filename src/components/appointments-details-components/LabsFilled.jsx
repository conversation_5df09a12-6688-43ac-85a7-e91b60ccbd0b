import { useState, useEffect, useCallback } from "react";
import { Loader2 } from "lucide-react";
import { getLabFormByAppointmentId_api } from "../../api/api_calls/appointmentforms/labform_apiCalls";
import { getFieldLabel } from "../../helpers/labFieldMappings";

const LabsFilled = ({ appointmentId }) => {
  const [labData, setLabData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  // Helper component for displaying individual lab items
  const LabItem = ({ title, content }) => (
    <div className="space-y-1">
      <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
        {title}
      </h3>
      <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
        {content || "N/A"}
      </p>
    </div>
  );

  // Helper component for displaying test sections
  const TestSection = ({ title, tests }) => (
    <div>
      <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">{title}</h3>
      <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6">
          {tests.map((test, index) => (
            <div key={index} className="space-y-1">
              <h3 className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                {getFieldLabel(test)}
              </h3>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  // Function to fetch lab form data
  const fetchLabFormData = useCallback(async () => {
    // Skip fetching if no appointmentId is provided (e.g., in consultation panel)
    if (!appointmentId) {
      setIsLoading(false);
      setLabData(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await getLabFormByAppointmentId_api(appointmentId);
      if (response?.data) {
        setLabData(response.data);
      } else {
        // No lab form found, but we'll still display the form with N/A values
        setLabData(null);
      }
    } catch (error) {
      console.log("No lab form found:", error.message);
      // No lab form found, but we'll still display the form with N/A values
      setLabData(null);
    } finally {
      setIsLoading(false);
    }
  }, [appointmentId]);

  // Fetch lab form data on component mount
  useEffect(() => {
    fetchLabFormData();
  }, [fetchLabFormData]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="w-full space-y-6">
        <div className="flex justify-center items-center min-h-[400px]">
          <div className="flex flex-col items-center gap-4">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <p className="text-gray-600">Loading lab form...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex justify-center w-full ">
      <div className="w-full space-y-6">
        {/* Basic Information */}
        <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 gap-4 md:gap-6">
            <LabItem
              title="Payment Responsibility"
              content={labData?.paymentResponsibility}
            />
            <LabItem
              title="Fasting Required"
              content={labData?.fastingRequired ? "Yes" : "No"}
            />
          </div>
        </div>

        {/* Chemistry Section */}
        {labData?.chemistry1Tests && labData.chemistry1Tests.length > 0 ? (
          <TestSection title="Chemistry" tests={labData.chemistry1Tests} />
        ) : (
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Chemistry
            </h3>
            <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
              <p className="text-gray-500 text-center">
                No chemistry tests selected
              </p>
            </div>
          </div>
        )}

        {/* Hematology Section */}
        {labData?.hematologyTests && labData.hematologyTests.length > 0 ? (
          <TestSection title="Hematology" tests={labData.hematologyTests} />
        ) : (
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Hematology
            </h3>
            <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
              <p className="text-gray-500 text-center">
                No hematology tests selected
              </p>
            </div>
          </div>
        )}

        {/* Coagulation Section */}
        {labData?.coagulationTests && labData.coagulationTests.length > 0 ? (
          <TestSection title="Coagulation" tests={labData.coagulationTests} />
        ) : (
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Coagulation
            </h3>
            <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
              <p className="text-gray-500 text-center">
                No coagulation tests selected
              </p>
            </div>
          </div>
        )}

        {/* Immunology Section */}
        {labData?.immunologyTests && labData.immunologyTests.length > 0 ? (
          <TestSection title="Immunology" tests={labData.immunologyTests} />
        ) : (
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Immunology
            </h3>
            <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
              <p className="text-gray-500 text-center">
                No immunology tests selected
              </p>
            </div>
          </div>
        )}

        {/* Cardiac Function and Lipids Section */}
        {labData?.cardiacFunctionAndLipidTests &&
        labData.cardiacFunctionAndLipidTests.length > 0 ? (
          <TestSection
            title="Cardiac Function and Lipids"
            tests={labData.cardiacFunctionAndLipidTests}
          />
        ) : (
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Cardiac Function and Lipids
            </h3>
            <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
              <p className="text-gray-500 text-center">
                No cardiac tests selected
              </p>
            </div>
          </div>
        )}

        {/* Tolerance Tests Section */}
        {labData?.toleranceTests && labData.toleranceTests.length > 0 ? (
          <TestSection title="Tolerance Tests" tests={labData.toleranceTests} />
        ) : (
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Tolerance Tests
            </h3>
            <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
              <p className="text-gray-500 text-center">
                No tolerance tests selected
              </p>
            </div>
          </div>
        )}

        {/* Nutritional Status Section */}
        {labData?.nutritionalStatusTests &&
        labData.nutritionalStatusTests.length > 0 ? (
          <TestSection
            title="Nutritional Status"
            tests={labData.nutritionalStatusTests}
          />
        ) : (
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Nutritional Status
            </h3>
            <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
              <p className="text-gray-500 text-center">
                No nutritional status tests selected
              </p>
            </div>
          </div>
        )}

        {/* Endocrine and Tumor Markers Section */}
        {labData?.endocrineAndTumorMarkers &&
        labData.endocrineAndTumorMarkers.length > 0 ? (
          <TestSection
            title="Endocrine and Tumor Markers"
            tests={labData.endocrineAndTumorMarkers}
          />
        ) : (
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Endocrine and Tumor Markers
            </h3>
            <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
              <p className="text-gray-500 text-center">
                No endocrine and tumor marker tests selected
              </p>
            </div>
          </div>
        )}

        {/* Therapeutic Drug Monitoring Section */}
        <div>
          <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
            Therapeutic Drug Monitoring
          </h3>
          <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
            {(() => {
              // Define therapeutic drug monitoring field IDs
              const therapeuticFieldIds = [
                "digoxinLastDose",
                "lithiumLastDose",
                "phenobarbitalLastDose",
                "phenytoinLastDose",
                "primidoneLastDose",
                "valproicAcidLastDose",
                "cyclosporineLastDose",
                "vancomycinLastDose",
                "preDoseLastDose",
                "postDoseLastDose",
                "gentamicinLastDose",
                "tobramycinLastDose",
                "preDoseLevelLastDose",
                "extendedIntervalPediatricsLastDose",
                "postDoseLevelLastDose",
                "extendedIntervalLastDose",
                "hr22PostLevelNeonatesLastDose",
              ];

              // Filter fields that have data
              const fieldsWithData = therapeuticFieldIds.filter(
                (fieldId) => labData?.[fieldId],
              );

              return fieldsWithData.length > 0 ? (
                <>
                  <h3 className="text-lg font-semibold mb-6">
                    Date & Time of last dose Required
                  </h3>
                  <div className="grid grid-cols-1 gap-4 md:gap-6">
                    {fieldsWithData.map((fieldId, index) => {
                      const dateValue = labData[fieldId];
                      const date = dateValue ? new Date(dateValue) : null;

                      return (
                        <div
                          key={index}
                          className="flex items-center justify-between flex-row w-full"
                        >
                          <div className="flex items-center w-4/6">
                            <h3 className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                              {getFieldLabel(fieldId)}
                            </h3>
                          </div>
                          <div className="flex flex-row items-center justify-between w-2/6">
                            <div className="space-y-1">
                              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                                Date
                              </h3>
                              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                                {date
                                  ? date.toLocaleDateString("en-US")
                                  : "N/A"}
                              </p>
                            </div>
                            <div className="space-y-1">
                              <h3 className="text-sm md:text-base font-medium text-[#1E1E1EB2]">
                                Time
                              </h3>
                              <p className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                                {date
                                  ? date.toLocaleTimeString("en-US", {
                                      hour: "2-digit",
                                      minute: "2-digit",
                                      hour12: true,
                                    })
                                  : "N/A"}
                              </p>
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </>
              ) : (
                <p className="text-gray-500 text-center">
                  No therapeutic drug monitoring tests selected
                </p>
              );
            })()}
          </div>
        </div>

        {/* Serum Toxicology Section */}
        {labData?.serumToxicologyTests &&
        labData.serumToxicologyTests.length > 0 ? (
          <TestSection
            title="Serum Toxicology"
            tests={labData.serumToxicologyTests}
          />
        ) : (
          <div>
            <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
              Serum Toxicology
            </h3>
            <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
              <p className="text-gray-500 text-center">
                No serum toxicology tests selected
              </p>
            </div>
          </div>
        )}

        {/* Blood Gas Section */}
        <div>
          <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
            Blood Gas
          </h3>
          <div className="w-full border border-[#E7E8E9] rounded-lg p-4 md:p-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-4 md:gap-6">
              <LabItem title="O2 Device" content={labData?.o2Device} />
              <LabItem title="O2 Therapy" content={labData?.o2Therapy} />
              <LabItem title="Temperature" content={labData?.temprature} />
            </div>

            {/* Blood Gas Tests */}
            {labData?.bloodGases && labData.bloodGases.length > 0 && (
              <div className="mt-6">
                <h4 className="font-medium mb-4">Selected Blood Gas Tests:</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-4">
                  {labData.bloodGases.map((test, index) => (
                    <div key={index} className="space-y-1">
                      <h3 className="text-sm md:text-base font-semibold text-[#1E1E1E]">
                        {getFieldLabel(test)}
                      </h3>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {(!labData?.bloodGases || labData.bloodGases.length === 0) &&
              !labData?.o2Device &&
              !labData?.o2Therapy &&
              !labData?.temprature && (
                <p className="text-gray-500 text-center">
                  No blood gas information available
                </p>
              )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default LabsFilled;
