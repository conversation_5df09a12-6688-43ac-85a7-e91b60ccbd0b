/* eslint-disable */
import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import {
  Tabs,
  Ta<PERSON>Content,
  TabsList,
  TabsTrigger,
} from "../../components/ui/tabs";
import { Dialog, DialogTrigger } from "../../components/ui/dialog";
import { Button } from "../../components/ui/button";
import DoctorNotes from "./DoctorNotes";
import PatientLogs from "./PatientLogs";
import { CirclePlus, Edit } from "lucide-react";
import { useSelector } from "react-redux";
import PatientLogsDialog from "../patient-logs-dialog/PatientLogsDialog";
import DoctorNotesDialog from "../doctor-notes-dialog/DoctorNotesDialog";
import { useToast } from "../../hooks/use-toast";
import {
  getDoctorCompliancesByAppointmentId_apiCalls,
  getPatientCompliancesByAppointmentId_apiCalls,
  createCompliance_apiCalls,
} from "../../api/api_calls/appointmentforms/compliance_apiCalls";

const PatientCompliance = () => {
  const [activeTab, setActiveTab] = useState("doctor");
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [doctorNotes, setDoctorNotes] = useState([]);
  const [patientLogs, setPatientLogs] = useState([]);
  const [isPatientDialogOpen, setIsPatientDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState({
    doctorNotes: false,
    patientLogs: false,
  });
  const user = useSelector((state) => state.userReducer.user);
  const location = useLocation();
  const { toast } = useToast();

  // Get appointmentId from location state
  const appointmentId =
    location.state?.appointmentId || location.state?.appointment?.id;

  // Unified function to fetch compliance data
  const fetchCompliances = async (type) => {
    if (!appointmentId) {
      return;
    }

    const isDoctor = type === "doctor";
    const loadingKey = isDoctor ? "doctorNotes" : "patientLogs";
    const apiCall = isDoctor
      ? getDoctorCompliancesByAppointmentId_apiCalls
      : getPatientCompliancesByAppointmentId_apiCalls;
    const setter = isDoctor ? setDoctorNotes : setPatientLogs;
    const errorMessage = `Failed to fetch ${isDoctor ? "doctor notes" : "patient logs"}. Please try again.`;

    try {
      setIsLoading((prev) => ({ ...prev, [loadingKey]: true }));
      const response = await apiCall(appointmentId);

      if (response?.data?.compliances) {
        const dataArray = Array.isArray(response.data.compliances)
          ? response.data.compliances
          : [];
        setter(dataArray);
      } else {
        setter([]);
      }
    } catch (error) {
      setter([]);
      if (
        error.message !==
        `No ${isDoctor ? "doctor" : "patient"} compliances found`
      ) {
        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading((prev) => ({ ...prev, [loadingKey]: false }));
    }
  };

  // Interface functions to maintain existing component contracts
  const fetchDoctorNotes = () => fetchCompliances("doctor");
  const fetchPatientLogs = () => fetchCompliances("patient");

  // Fetch data when component mounts or tab changes
  useEffect(() => {
    if (activeTab === "doctor" && doctorNotes.length === 0) {
      fetchDoctorNotes();
    } else if (activeTab === "patients" && patientLogs.length === 0) {
      fetchPatientLogs();
    }
  }, [activeTab, appointmentId]);

  // Unified function to handle both doctor notes and patient logs
  const handleAddCompliance = async (data, type) => {
    if (!appointmentId) {
      toast({
        title: "Error",
        description: "No appointment ID provided",
        variant: "destructive",
      });
      return;
    }

    try {
      const complianceData = {
        appointmentId,
        type,
        log: data.log,
      };

      const response = await createCompliance_apiCalls(complianceData);

      if (response?.data) {
        if (type === "doctor") {
          setDoctorNotes((prev) => [response.data, ...prev]);
          setIsDialogOpen(false);
        } else {
          setPatientLogs((prev) => [response.data, ...prev]);
          setIsPatientDialogOpen(false);
        }

        toast({
          title: "Success",
          description: `${type === "doctor" ? "Doctor note" : "Patient log"} added successfully`,
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to add ${type === "doctor" ? "doctor note" : "patient log"}. Please try again.`,
        variant: "destructive",
      });
    }
  };

  // Interface functions to maintain existing component contracts
  const handleAddNote = (note) => handleAddCompliance(note, "doctor");
  const handleAddLog = (log) => handleAddCompliance(log, "patient");

  return (
    <div className="w-full space-y-6">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-4">
          <h2 className="font-bold text-[20px] text-[#1E1E1E]">
            Patient Compliance
          </h2>

          {user?.userType === "patient" && (
            <Dialog
              open={isPatientDialogOpen}
              onOpenChange={setIsPatientDialogOpen}
            >
              <DialogTrigger asChild>
                <Button
                  variant={"primary"}
                  size={"sm"}
                  className="flex items-center gap-1"
                >
                  <CirclePlus className="h-4 w-4" />
                  <span>Add Logs</span>
                </Button>
              </DialogTrigger>
              <PatientLogsDialog onAddLog={handleAddLog} user={user} />
            </Dialog>
          )}
        </div>

        {user?.userType === "doctor" && (
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button variant={"secondary"} size={"icon"} className="h-8 w-8">
                <Edit className="h-4 w-4" />
              </Button>
            </DialogTrigger>
            <DoctorNotesDialog onAddNote={handleAddNote} />
          </Dialog>
        )}
      </div>

      <Tabs
        defaultValue="doctor"
        className="w-full"
        value={activeTab}
        onValueChange={setActiveTab}
      >
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="doctor">Doctor&apos;s Notes</TabsTrigger>
          <TabsTrigger value="patients">Patient Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="doctor">
          <DoctorNotes notes={doctorNotes} isLoading={isLoading.doctorNotes} />
        </TabsContent>

        <TabsContent value="patients" className="relative">
          {user?.userType === "patient" && activeTab === "patients" && (
            <div className="absolute top-4 left-4 z-10">
              <Dialog
                open={isPatientDialogOpen}
                onOpenChange={setIsPatientDialogOpen}
              >
                <DialogTrigger asChild>
                  <Button
                    variant={"primary"}
                    size={"sm"}
                    className="flex items-center gap-1"
                  >
                    <CirclePlus className="h-4 w-4" />
                    <span>Add Logs</span>
                  </Button>
                </DialogTrigger>
                <PatientLogsDialog onAddLog={handleAddLog} user={user} />
              </Dialog>
            </div>
          )}
          <PatientLogs logs={patientLogs} isLoading={isLoading.patientLogs} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PatientCompliance;
