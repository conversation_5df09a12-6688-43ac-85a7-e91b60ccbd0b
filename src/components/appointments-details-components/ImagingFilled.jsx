import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";

import { getImagingFormByAppointmentId_apiCalls } from "../../api/api_calls/appointmentforms/imagingForm_apiCalls";
import { Loader2 } from "lucide-react";

const ImagingFilled = () => {
  const [loading, setLoading] = useState(true);
  const [imagingData, setImagingData] = useState(null);
  const location = useLocation();

  // Get appointmentId from location state
  const appointmentId =
    location.state?.appointmentId || location.state?.appointment?.id;

  // Fetch imaging form data
  useEffect(() => {
    const fetchImagingForm = async () => {
      if (!appointmentId) {
        console.log("No appointment ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log("Fetching imaging form for appointmentId:", appointmentId);

        const response = await getImagingFormByAppointmentId_apiCalls({
          appointmentId,
        });

        console.log("Imaging form API response:", response);

        if (response?.data) {
          setImagingData(response.data);
          console.log("Imaging form data loaded:", response.data);
        } else {
          console.log("No imaging form found for this appointment");
        }
      } catch (error) {
        console.error("Failed to fetch imaging form:", error);
        // Don't set error state, just log it
      } finally {
        setLoading(false);
      }
    };

    fetchImagingForm();
  }, [appointmentId]);

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading imaging form...</span>
      </div>
    );
  }

  // Use imagingData or empty placeholders to prevent crashes
  const displayData = imagingData || {
    id: null,
    appointmentId: appointmentId,
    servicesRequested: null,
    specialInstructions: null,
    diagnosis: null,
    createdAt: null,
    updatedAt: null,
    appointment: {
      doctor: {
        firstName: null,
        lastName: null,
        medicalLicenseNumber: null,
        contactNumber: null,
        address: null,
        signature: {
          url: null,
        },
      },
    },
  };

  console.log("displayData:", location.state?.appointment);

  return (
    <div className="w-full">
      <div className="space-y-8">
        {/* Imaging Form Information */}
        <div className="flex items-center justify-center w-full flex-col border-[1px] border-[#E7E8E9] border-solid p-4 rounded-md">
          <h3 className="text-lg font-bold mb-4 w-full">
            Provider Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                Provider Name
              </h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                {displayData.appointment.doctor.firstName || "N/A"}{" "}
                {displayData.appointment.doctor.lastName || "N/A"}
              </h3>
            </div>

            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">NPI</h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                {displayData.appointment.doctor.medicalLicenseNumber || "N/A"}
              </h3>
            </div>

            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                Office Name
              </h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                Doc Mobil
              </h3>
            </div>

            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                Office Address
              </h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                {displayData.appointment.doctor.address || "N/A"}
              </h3>
            </div>


            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                Contact Number
              </h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                {displayData.appointment.doctor.contactNumber || "N/A"}
              </h3>
            </div>

            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                Office Address
              </h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                256 Ennisbrook Drive Se, Smyrna, GA 30082
              </h3>
            </div>

           
          </div>
        </div>

        {/* Imaging Form Information */}
        <div className="flex items-center justify-center w-full flex-col border-[1px] border-[#E7E8E9] border-solid p-4 rounded-md">
          <h3 className="text-lg font-bold mb-4 w-full">
            Imaging Form Details
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                Form ID
              </h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                {displayData?.id || "N/A"}
              </h3>
            </div>

            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                Appointment ID
              </h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                {displayData?.appointmentId || appointmentId || "N/A"}
              </h3>
            </div>

            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                Created Date
              </h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                {displayData?.createdAt
                  ? new Date(displayData.createdAt).toLocaleDateString()
                  : "N/A"}
              </h3>
            </div>

            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                Last Updated
              </h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                {displayData?.updatedAt
                  ? new Date(displayData.updatedAt).toLocaleDateString()
                  : "N/A"}
              </h3>
            </div>
          </div>
        </div>

        {/* Imaging Form Content */}
        <div className="flex items-center justify-center w-full flex-col border-[1px] border-[#E7E8E9] border-solid p-4 rounded-md">
          <h3 className="text-lg font-bold mb-4 w-full">
            Imaging Request Details
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-1 gap-6 w-full">
            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                Services Requested
              </h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                {displayData?.servicesRequested || "N/A"}
              </h3>
            </div>

            {displayData?.specialInstructions && (
              <div>
                <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                  Special Instructions
                </h3>
                <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                  {displayData.specialInstructions}
                </h3>
              </div>
            )}

            <div>
              <h3 className="text-sm font-medium text-[#1E1E1EB2] mb-1">
                Diagnosis/Indications
              </h3>
              <h3 className="text-sm font-semibold text-[#1E1E1E] mb-1">
                {displayData?.diagnosis || "N/A"}
              </h3>
            </div>
          </div>
        </div>

        {/* Signature and Date */}
        <div className="flex items-center justify-between flex-row">
          <div className="">
            <p className="text-xl font-bold text-[#1E1E1E] mb-2">
              Authorized Signature
            </p>
            <div className="font-script text-lg">
              <img
                alt="Medical Officer Signature"
                src={displayData?.appointment?.doctor?.signature?.url}
              />
            </div>
            {/* <p className="text-sm text-gray-500 mt-2">
              Form ID: {displayData?.id || "N/A"}
            </p> */}
          </div>

          <div>
            <p className="font-[600] text-[16px]">Created Date</p>
            <p className="font-[600] text-[16px]">
              {displayData?.createdAt
                ? new Date(displayData.createdAt).toLocaleDateString()
                : "N/A"}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImagingFilled;
