import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";

const Activity = () => {
  const activityData = [
    {
      fileName: "SOAP",
      lastUpdatedBy: "Cameron Davis",
      dateOfConsultation: "25/11/2024",
      fileUpdatedAt: "13/10/2024",
    },
    {
      fileName: "SOAP",
      lastUpdatedBy: "Cameron Davis",
      dateOfConsultation: "25/11/2024",
      fileUpdatedAt: "13/10/2024",
    },
  ];

  return (
    <div className="w-full">
      <h2 className="text-xl font-semibold mb-4">Activity</h2>
      <div className="rounded-md border">
        <Table>
          <TableHeader className="bg-[#F8F8F8]">
            <TableRow>
              <TableHead className="font-medium text-gray-700">
                File Name
              </TableHead>
              <TableHead className="font-medium text-gray-700">
                Last Updated By
              </TableHead>
              <TableHead className="font-medium text-gray-700">
                Date of Consultation
              </TableHead>
              <TableHead className="font-medium text-gray-700">
                File Updated At
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {activityData.map((activity, index) => (
              <TableRow key={index}>
                <TableCell>{activity.fileName}</TableCell>
                <TableCell>{activity.lastUpdatedBy}</TableCell>
                <TableCell>{activity.dateOfConsultation}</TableCell>
                <TableCell>{activity.fileUpdatedAt}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default Activity;
