import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { Button } from "../../components/ui/button";
import { Textarea } from "../../components/ui/textarea";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "../../hooks/use-toast";
import { Loader2 } from "lucide-react";
import {
  createMedicalCertificate_apiCalls,
  updateMedicalCertificate_apiCalls,
  getMedicalCertificateByAppointmentId_apiCalls,
} from "../../api/api_calls/appointmentforms/medicalCertificate_apiCalls";

const formSchema = z.object({
  id: z.string().optional(),
  appointmentId: z.string().min(1, {
    message: "Valid appointment ID is required",
  }),
  diagnosis: z.string().min(1, {
    message: "Diagnosis is required",
  }),
  recommendations: z.string().optional(),
});

const MedicalCertificate = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(true);
  const [certificateData, setCertificateData] = useState(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const location = useLocation();

  // Get appointmentId from location state
  const appointmentId =
    location.state?.appointmentId || location.state?.appointment?.id;

  console.log(
    "🔍 Medical Certificate Component - appointmentId:",
    appointmentId,
  );
  console.log(
    "🔍 Medical Certificate Component - location.state:",
    location.state,
  );

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: "",
      appointmentId: appointmentId || "",
      diagnosis: "",
      recommendations: "",
    },
  });

  // Update form appointmentId when it changes
  useEffect(() => {
    if (appointmentId) {
      form.setValue("appointmentId", appointmentId);
      console.log("📝 Updated form appointmentId:", appointmentId);
    }
  }, [appointmentId, form]);

  // Fetch existing medical certificate data
  useEffect(() => {
    const fetchMedicalCertificate = async () => {
      if (!appointmentId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const response = await getMedicalCertificateByAppointmentId_apiCalls({
          appointmentId,
        });

        if (response?.data) {
          setCertificateData(response.data);
          setIsEditMode(true);

          // Populate form with existing data
          form.reset({
            id: response.data.id || "",
            appointmentId: response.data.appointmentId || appointmentId,
            diagnosis: response.data.diagnosis || "",
            recommendations: response.data.recommendations || "",
          });
        }
      } catch (error) {
        console.error("Failed to fetch medical certificate:", error);
        // If no certificate exists, we'll create a new one
        setIsEditMode(false);
      } finally {
        setLoading(false);
      }
    };

    fetchMedicalCertificate();
  }, [appointmentId, form]);

  const onSubmit = async (data) => {
    console.log("🚀 Medical Certificate Form Submission Started");
    console.log("📝 Form data received:", data);
    console.log("🆔 appointmentId:", appointmentId);
    console.log("🔄 isEditMode:", isEditMode);
    console.log("📋 certificateData:", certificateData);

    if (!appointmentId) {
      console.error("❌ No appointmentId provided");
      toast({
        description: "No appointment ID provided",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);

      const formData = {
        ...data,
        appointmentId,
      };

      console.log("📦 Final form data to submit:", formData);

      let response;
      if (isEditMode && (certificateData?.id || data.id)) {
        // Update existing certificate
        response = await updateMedicalCertificate_apiCalls({
          ...formData,
          id: data.id || certificateData.id,
        });
        toast({
          description: "Medical Certificate updated successfully",
        });
      } else {
        // Create new certificate (remove id from formData for creation)
        const { id, ...createData } = formData;
        response = await createMedicalCertificate_apiCalls(createData);
        toast({
          description: "Medical Certificate created successfully",
        });
        setIsEditMode(true);
        setCertificateData(response.data);
      }

      console.log("Medical Certificate operation successful:", response);
    } catch (error) {
      toast({
        description:
          error.message ||
          `Failed to ${isEditMode ? "update" : "create"} Medical Certificate`,
        variant: "destructive",
      });
      console.error(
        `ERROR IN Medical Certificate ${isEditMode ? "UPDATE" : "CREATE"} => `,
        error,
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex justify-center items-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading medical certificate...</span>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="space-y-6">
        <div className="border-solid border-[1px] border-[#E7E8E9] p-6 rounded-lg">
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="space-y-6"
              disabled={isSubmitting}
            >
              {/* Patient Information Section */}
              <div className="mb-6">
                {/* Diagnosis/Condition */}
                <div className="mt-6">
                  <FormField
                    control={form.control}
                    name="diagnosis"
                    render={({ field, fieldState }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium">
                          Diagnosis/Condition
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Write here"
                            className={`min-h-[100px] transition-all ${
                              fieldState.error
                                ? "border-destructive focus:ring-destructive/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Recommendations/Comments */}
                <div className="mt-6">
                  <FormField
                    control={form.control}
                    name="recommendations"
                    render={({ field, fieldState }) => (
                      <FormItem>
                        <FormLabel className="text-gray-700 font-medium">
                          Recommendations/Comments
                        </FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Write here"
                            className={`min-h-[100px] transition-all ${
                              fieldState.error
                                ? "border-destructive focus:ring-destructive/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end space-x-4 mt-8">
                <Button
                  variant="outline"
                  className="bg-gray-100 text-gray-700 hover:bg-gray-200"
                  type="button"
                  onClick={() => form.reset()}
                >
                  Cancel
                </Button>
                <Button
                  className="bg-blue-600 text-white hover:bg-blue-700"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {isEditMode ? "Updating..." : "Creating..."}
                    </>
                  ) : isEditMode ? (
                    "Update Certificate"
                  ) : (
                    "Create Certificate"
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default MedicalCertificate;
