import { Input } from "../ui/input-footer";
import { <PERSON><PERSON> } from "../ui/button";
import { ReactComponent as DocMobilLogo } from "../../assets/svgs/DocMobilLogoWithText.svg";
import { <PERSON> } from "react-router-dom";

import { ReactComponent as SocialInsta } from "../../assets/svgs/insta.svg";
import { ReactComponent as SocialX } from "../../assets/svgs/twitter.svg";
import { ReactComponent as SocialLinkin } from "../../assets/svgs/fb.svg";
import { ReactComponent as FooterPlusLeftImg } from "../../assets/svgs/FooterPlusLeftImg.svg";
import { ReactComponent as FooterPlusRightImg } from "../../assets/svgs/FooterPlusRightImg.svg";

const PatientFooter = () => {
  return (
    <div className="bg-[#2B2B2B] w-full py-8 md:py-12 lg:py-16 flex items-center justify-center flex-col px-4 sm:px-6 md:px-8 lg:px-12 xl:px-20">
      <div className="bg-[#0052FD] items-center justify-center flex w-full p-6 sm:p-8 md:p-10 lg:p-12 rounded-xl sm:rounded-2xl lg:rounded-3xl flex-col relative overflow-hidden">
        <div className="hidden md:block">
          <FooterPlusLeftImg className="absolute left-0 top-5" />
          <FooterPlusRightImg className="absolute right-0 bottom-5" />
        </div>

        <h1 className="font-cormorant-garamond font-bold text-white text-2xl sm:text-3xl md:text-4xl lg:text-[48px] text-center">
          Subscribe to our newsletter
        </h1>

        <h1 className="text-white text-sm sm:text-base md:text-lg lg:text-[20px] px-0 sm:px-8 md:px-16 lg:px-32 xl:px-64 text-center mt-2 sm:mt-3 md:mt-4">
          Subscribe to our newsletter and join a community of health-conscious
          individuals on a journey towards better well-being.
        </h1>

        <div className="flex items-center justify-between flex-col sm:flex-row w-full max-w-md border-solid border-[2px] border-white rounded-full p-1 gap-2 mt-4 sm:mt-6 md:mt-8">
          <Input
            type="email"
            placeholder="Email"
            className="bg-transparent rounded-full text-white border-none shadow-none focus:border-none w-full"
          />
          <Button className="rounded-full bg-white w-full sm:w-auto mt-2 sm:mt-0">
            Submit
          </Button>
        </div>
      </div>

      <div className="flex items-center justify-between flex-col md:flex-row w-full my-6 md:my-10 lg:my-16 gap-6 md:gap-0">
        <div className="flex justify-center md:justify-start w-full md:w-auto">
          <DocMobilLogo className="h-[36px] w-[160px] md:h-[46px] md:w-[205px]" />
        </div>

        <div className="flex flex-wrap justify-center gap-4 sm:gap-6 md:space-x-4 lg:space-x-8">
          <Link
            to="/"
            className="text-white hover:text-gray-300 font-semibold transition-colors"
          >
            Home
          </Link>
          <Link
            to="/about"
            className="text-white hover:text-gray-300 font-semibold transition-colors"
          >
            About Us
          </Link>
          <Link
            to="/services"
            className="text-white hover:text-gray-300 font-semibold transition-colors"
          >
            Services
          </Link>
          <Link
            to="/blogs"
            className="text-white hover:text-gray-300 font-semibold transition-colors"
          >
            Blogs
          </Link>
        </div>

        <div className="flex flex-row gap-4 items-center justify-center md:justify-between">
          <SocialInsta className="w-6 h-6 md:w-auto md:h-auto cursor-pointer hover:opacity-80 transition-opacity" />
          <SocialLinkin className="w-6 h-6 md:w-auto md:h-auto cursor-pointer hover:opacity-80 transition-opacity" />
          <SocialX className="w-6 h-6 md:w-auto md:h-auto cursor-pointer hover:opacity-80 transition-opacity" />
        </div>
      </div>

      <div className="w-full h-px bg-[#FFFFFF33] my-4"></div>
      <div className="flex flex-col sm:flex-row items-center justify-between w-full gap-4 sm:gap-0">
        <h6 className="text-[#FFFFFFB2] text-sm text-center sm:text-left">
          © 2023 DocMobil. All Right Reserved
        </h6>

        <div className="flex flex-row gap-4 justify-center">
          <h6 className="text-[#FFFFFFB2] text-sm hover:text-white cursor-pointer">
            Terms & Conditions
          </h6>
          <h6 className="text-[#FFFFFFB2] text-sm hover:text-white cursor-pointer">
            Privacy Policy
          </h6>
        </div>
      </div>
    </div>
  );
};

export default PatientFooter;
