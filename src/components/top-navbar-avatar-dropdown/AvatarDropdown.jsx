import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import { ChevronDown, LogOut, User } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";
import { logOutUser } from "../../common/logoutUser";
import DefaultAvatar from "../../assets/images/DefaultAvatar.png";

const AvatarDropdown = () => {
  const navigate = useNavigate();
  const user = useSelector((state) => state.userReducer.user);

  const handleProfileAction = (action) => {
    if (action === "profile") {
      if (user?.userType === "doctor") {
        navigate("/doctor/profile");
      } else if (user?.userType === "patient") {
        navigate("/patient/profile");
      }
    } else if (action === "logout") {
      logOutUser();
    }
  };

  return (
    <DropdownMenu>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger className="focus:outline-none flex items-center gap-1 rounded-full p-1 transition-colors hover:bg-gray-100">
              <Avatar>
                <AvatarImage src={user?.profilePicture?.url || DefaultAvatar} />
                <AvatarFallback>
                  {user?.firstName?.charAt(0) || "U"}
                </AvatarFallback>
              </Avatar>
              <ChevronDown className="h-4 w-4 text-gray-500" />
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>Click to open menu</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={() => handleProfileAction("profile")}>
          <User className="mr-2 h-4 w-4" />
          <span>Profile</span>
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleProfileAction("logout")}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Log out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default AvatarDropdown;
