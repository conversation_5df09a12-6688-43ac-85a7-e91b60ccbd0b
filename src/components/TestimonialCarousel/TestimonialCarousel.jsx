import { useState, useEffect } from "react";
import Testmonial1 from "../../assets/images/Testomonial1.jpg";
import Testmonial2 from "../../assets/images/Testomonial2.jpg";
import Testmonial3 from "../../assets/images/Testomonial3.jpg";
import TestmonialBg from "../../assets/images/TestmonialBg.png";
import { useTranslation } from "react-i18next";

export function TestimonialCarousel() {
  const [activeIndex, setActiveIndex] = useState(0);
  const { t } = useTranslation();

  const testimonials = [
    {
      id: 1,
      name: "<PERSON><PERSON>",
      image: Testmonial1,
      text: `${t("testimonials-1")}`,
    },
    {
      id: 2,
      name: "<PERSON>",
      image: Testmonial2,
      text: `${t("testimonials-2")}`,
    },
    {
      id: 3,
      name: "<PERSON>",
      image: Testmonial3,
      text: `${t("testimonials-2")}`,
    },
  ];

  const nextTestimonial = () => {
    setActiveIndex((prev) => (prev + 1) % testimonials.length);
  };

  useEffect(() => {
    const interval = setInterval(() => {
      nextTestimonial();
    }, 8000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="py-8 sm:py-12 md:py-16">
      <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-[55px] font-bold text-center mb-8 sm:mb-12 font-cormorant-garamond">
        {t(`whats-our-patient-say-heading`)}
      </h2>
      <div
        className="py-8 sm:py-12 md:py-16 text-white relative"
        style={{
          backgroundImage: `url(${TestmonialBg})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="absolute inset-0 bg-black/50"></div>

        <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="relative mx-auto max-w-4xl">
            <div className="overflow-hidden">
              <div
                className="flex transition-transform duration-500 ease-in-out"
                style={{ transform: `translateX(-${activeIndex * 100}%)` }}
              >
                {testimonials.map((testimonial) => (
                  <div
                    key={testimonial.id}
                    className="w-full flex-shrink-0 px-2 sm:px-4"
                  >
                    <div className="bg-transparent">
                      <div className="p-4 sm:p-6 md:p-8">
                        <div className="flex flex-col md:flex-row items-center text-center md:text-start w-full gap-6 md:gap-8">
                          <div className="mb-4 md:mb-6 flex items-center justify-center flex-col gap-4 w-full md:w-2/6">
                            <div className="relative h-32 w-32 sm:h-40 sm:w-40 md:h-[190px] md:w-[190px] overflow-hidden rounded-full border-4 border-white/50">
                              <img
                                src={
                                  testimonial.image ||
                                  "https://via.placeholder.com/100"
                                }
                                alt={testimonial.name}
                                className="h-full w-full object-cover"
                                loading="lazy"
                              />
                            </div>
                            <h3 className="font-medium font-cormorant-garamond text-2xl sm:text-3xl md:text-[36px]">
                              {testimonial.name}
                            </h3>
                          </div>

                          <div className="mb-4 md:mb-6 flex items-center justify-center w-full md:w-4/6">
                            <p className="text-base sm:text-lg md:text-xl lg:text-[28px] ">
                              {testimonial.text}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="mt-6 flex justify-center gap-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setActiveIndex(index)}
                  className={`h-2 w-2 sm:h-3 sm:w-3 rounded-full transition-colors ${index === activeIndex ? "bg-white" : "bg-white/50"}`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
