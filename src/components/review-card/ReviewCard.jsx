import React from "react";
import ProfilePicture from "../../assets/images/IDCard.jpeg";

const ReviewCard = () => {
  return (
    <div className="flex w-full items-start border border-solid border-[#E7E8E9] p-4 rounded-md shadow-sm flex-col gap-4">
      <div className="flex w-full items-center justify-between flex-col sm:flex-row gap-4">
        <div className="flex items-center gap-4 w-full sm:w-auto">
          <div className="h-10 w-10 overflow-hidden rounded-full flex items-center justify-center">
            <img
              src={ProfilePicture}
              alt="Profile"
              className="object-cover w-full h-full"
            />
          </div>
          <div className="flex-1">
            <h1 className="text-base sm:text-lg font-semibold text-gray-800">
              A<PERSON>
            </h1>
            <div className="flex items-center gap-1 flex-wrap">
              <div className="flex items-center">
                {[...Array(5)].map((_, index) => (
                  <svg
                    key={index}
                    className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <span className="text-xs sm:text-sm text-gray-600">4.5</span>
              <span className="text-xs sm:text-sm text-gray-500">
                • 12 days ago
              </span>
            </div>
          </div>
        </div>
        <button className="flex items-center gap-1 bg-green-100 text-green-700 px-2 sm:px-3 py-1 rounded-full text-xs sm:text-sm font-medium w-full sm:w-auto justify-center">
          <svg
            className="w-3 h-3 sm:w-4 sm:h-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M5 13l4 4L19 7"
            />
          </svg>
          Recommended
        </button>
      </div>
      <p className="text-gray-600 text-xs sm:text-sm lg:text-base mt-2">
        This online medical platform provides accurate and reliable health
        information, making it a trusted resource for patients and caregivers.
        With its user-friendly interface, it simplifies appointment booking and
        offers seamless access to certified professionals and experts.
      </p>
    </div>
  );
};

export default ReviewCard;
