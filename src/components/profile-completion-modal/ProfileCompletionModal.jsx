import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "../../components/ui/dialog";
import { Button } from "../../components/ui/button";
import { CheckCircle } from "lucide-react";

const ProfileCompletionModal = ({ isOpen, onClose, onContinue }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-auto p-6 text-center">
        <DialogTitle className="sr-only">Profile Completion Modal</DialogTitle>
        <DialogDescription className="sr-only">
          Profile Completion Modal
        </DialogDescription>
        <div className="flex flex-col items-center justify-center space-y-4">
          <div className="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center">
            <CheckCircle className="h-12 w-12 text-green-600" />
          </div>

          <h2 className="text-2xl font-bold text-gray-800">
            Profile Completed!
          </h2>

          <p className="text-gray-600 mb-4">
            Your profile has been successfully completed. You can now continue
            to find doctors.
          </p>

          <Button
            onClick={onContinue}
            className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-6 py-2 w-full"
          >
            Continue to Find Doctors
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ProfileCompletionModal;
