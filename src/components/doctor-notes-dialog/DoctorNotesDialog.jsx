import React, { useState, useEffect } from "react";
import {
  <PERSON>alog,
  <PERSON>alogClose,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogTrigger,
} from "../../components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import { Input } from "../../components/ui/input";
import { Textarea } from "../../components/ui/textarea";
import { Button } from "../../components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import DatePicker from "../date-picker/DatePicker";

const doctorNotesSchema = z.object({
  date: z.string().min(1, "Date is required"),
  time: z.string().min(1, "Time is required"),
  log: z
    .string()
    .min(1, "Description is required")
    .max(500, "Description must be less than 500 characters"),
});

const DoctorNotesDialog = ({ onAddNote }) => {
  const currentDate = new Date();
  const currentTime = currentDate.toLocaleTimeString("en-US", {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
  });

  const form = useForm({
    resolver: zodResolver(doctorNotesSchema),
    defaultValues: {
      date: currentDate.toISOString(),
      time: currentTime,

      log: "",
    },
  });

  const onSubmit = (data) => {
    onAddNote(data);
    const newCurrentDate = new Date();
    const newCurrentTime = newCurrentDate.toLocaleTimeString("en-US", {
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
    });

    form.reset({
      date: newCurrentDate.toISOString(),
      time: newCurrentTime,

      log: "",
    });
  };

  return (
    <DialogContent className="sm:max-w-[500px]">
      <DialogHeader>
        <DialogTitle>Add Doctor&apos;s Note</DialogTitle>
        <DialogDescription className="sr-only">
          Add a new note for the patient. Date and time are automatically set to
          current values.
        </DialogDescription>
      </DialogHeader>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            {/* Date */}
            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date</FormLabel>
                  <FormControl>
                    <div className="pointer-events-none opacity-60">
                      <DatePicker
                        field={field}
                        label=""
                        className="bg-muted cursor-not-allowed"
                      />
                    </div>
                  </FormControl>
                  <FormDescription className="sr-only">
                    Current date (auto-filled)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Time */}
            <FormField
              control={form.control}
              name="time"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Time</FormLabel>
                  <FormControl>
                    <Input
                      {...field}
                      type="time"
                      disabled={true}
                      className="bg-muted"
                    />
                  </FormControl>
                  <FormDescription className="sr-only">
                    Current time (auto-filled)
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="log"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Enter detailed description..."
                    className="min-h-[100px]"
                    {...field}
                  />
                </FormControl>
                <FormDescription className="sr-only">
                  Detailed description of the note (max 500 characters)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <DialogFooter className="gap-2">
            <DialogClose asChild>
              <Button type="button" variant="outline">
                Cancel
              </Button>
            </DialogClose>
            <Button type="submit" variant={"primary"}>
              Add Note
            </Button>
          </DialogFooter>
        </form>
      </Form>
    </DialogContent>
  );
};

export default DoctorNotesDialog;
