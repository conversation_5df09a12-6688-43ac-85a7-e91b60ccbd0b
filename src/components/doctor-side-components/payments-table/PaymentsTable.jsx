import React from "react";
import { useState, useMemo } from "react";
import { Input } from "../../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationLink,
} from "../../ui/pagination";
import { Avatar, AvatarFallback, AvatarImage } from "../../ui/avatar";
import PaymentHistoryData from "../../data/PaymentHistoryData";
import { Button } from "../../ui/button";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";

const statusColors = {
  Completed: "bg-blue-100 text-blue-600",
  Canceled: "bg-red-100 text-red-600",
  Upcoming: "bg-yellow-100 text-yellow-600",
};

const paymentStatusColors = {
  Complete: "bg-blue-100 text-blue-600",
  Pending: "bg-yellow-100 text-yellow-600",
  Failed: "bg-red-100 text-red-600",
};

const PaymentsTable = () => {
  const [search, setSearch] = useState("");
  const [statusFilter, setStatusFilter] = useState(null);
  const [dateFilter, setDateFilter] = useState("All Time");
  const [paymentStatusFilter, setPaymentStatusFilter] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  // Set the current date to 21/5/2025 to match our data
  const today = new Date(2025, 4, 21); // Month is 0-indexed, so 4 = May

  // Helper function to get the start and end of a week
  const getWeekRange = (date) => {
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    const monday = new Date(date);
    monday.setDate(diff);
    monday.setHours(0, 0, 0, 0);

    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);
    sunday.setHours(23, 59, 59, 999);

    return { start: monday, end: sunday };
  };

  // Get current date and week ranges
  const thisWeekRange = getWeekRange(today);

  // Get last week's range
  const lastWeekStart = new Date(thisWeekRange.start);
  lastWeekStart.setDate(lastWeekStart.getDate() - 7);
  const lastWeekEnd = new Date(thisWeekRange.start);
  lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
  lastWeekEnd.setHours(23, 59, 59, 999);

  // Get next week's range
  const nextWeekStart = new Date(thisWeekRange.end);
  nextWeekStart.setDate(nextWeekStart.getDate() + 1);
  const nextWeekEnd = new Date(nextWeekStart);
  nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
  nextWeekEnd.setHours(23, 59, 59, 999);

  // Calculate date filter counts
  const dateFilterCounts = useMemo(() => {
    return {
      "This Week": PaymentHistoryData.filter(
        (p) => p.date >= thisWeekRange.start && p.date <= thisWeekRange.end,
      ).length,
      "Last Week": PaymentHistoryData.filter(
        (p) => p.date >= lastWeekStart && p.date <= lastWeekEnd,
      ).length,
      "Next Week": PaymentHistoryData.filter(
        (p) => p.date >= nextWeekStart && p.date <= nextWeekEnd,
      ).length,
    };
  }, []);

  // Filter data based on search, status, payment status, and date range
  const filteredData = useMemo(() => {
    return PaymentHistoryData.filter((payment) => {
      // Search filter (check patient name, ID, service, and location)
      const searchTerms = search.toLowerCase();
      const matchesSearch =
        search === "" ||
        payment.patient.toLowerCase().includes(searchTerms) ||
        payment.id.toLowerCase().includes(searchTerms) ||
        payment.service.toLowerCase().includes(searchTerms) ||
        payment.location.toLowerCase().includes(searchTerms);

      // Status filter
      const matchesStatus = statusFilter
        ? payment.status === statusFilter
        : true;

      // Payment status filter
      const matchesPaymentStatus = paymentStatusFilter
        ? payment.paymentStatus === paymentStatusFilter
        : true;

      // Date filter
      let matchesDateRange = true;
      if (dateFilter === "This Week") {
        matchesDateRange =
          payment.date >= thisWeekRange.start &&
          payment.date <= thisWeekRange.end;
      } else if (dateFilter === "Last Week") {
        matchesDateRange =
          payment.date >= lastWeekStart && payment.date <= lastWeekEnd;
      } else if (dateFilter === "Next Week") {
        matchesDateRange =
          payment.date >= nextWeekStart && payment.date <= nextWeekEnd;
      }

      return (
        matchesSearch &&
        matchesStatus &&
        matchesPaymentStatus &&
        matchesDateRange
      );
    });
  }, [search, statusFilter, paymentStatusFilter, dateFilter]);

  const clearFilters = () => {
    setSearch("");
    setDateFilter("All Time");
    setStatusFilter(null);
    setPaymentStatusFilter(null);
    setCurrentPage(1);
  };

  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex flex-col gap-4 mb-6">
        <div className="flex justify-between items-center">
          <h2 className="text-lg font-bold">List of Payments</h2>
          <div className="flex gap-4 items-center">
            <Input
              placeholder="Search payments"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-64"
            />
            <Select
              value={dateFilter}
              onValueChange={(value) => {
                setDateFilter(value);
                setCurrentPage(1); // Reset to first page when filter changes
              }}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All Time">All Time</SelectItem>
                <SelectItem value="This Week">
                  This Week ({dateFilterCounts["This Week"]})
                </SelectItem>
                <SelectItem value="Last Week">
                  Last Week ({dateFilterCounts["Last Week"]})
                </SelectItem>
                <SelectItem value="Next Week">
                  Next Week ({dateFilterCounts["Next Week"]})
                </SelectItem>
              </SelectContent>
            </Select>
            {(search ||
              dateFilter !== "All Time" ||
              statusFilter ||
              paymentStatusFilter) && (
              <Button variant="outline" onClick={clearFilters} size="sm">
                Clear Filters
              </Button>
            )}
          </div>
        </div>

        {/* <div className="flex items-center gap-6">
          <div className="flex flex-row items-center gap-2">
            <h2
              className={`text-base font-[500] ${
                statusFilter === "Completed"
                  ? "text-blue-600 underline"
                  : "text-black"
              } cursor-pointer`}
              onClick={() => handleStatusFilter("Completed")}
            >
              Completed
            </h2>
            <div
              className={`h-6 w-6 border-solid ${
                statusFilter === "Completed"
                  ? "border-blue-600"
                  : "border-black"
              } border-[1px] rounded-full flex items-center justify-center`}
            >
              <span
                className={`text-xs font-[500] ${statusFilter === "Completed" ? "text-blue-600" : "text-black"}`}
              >
                {statusCounts.Completed}
              </span>
            </div>
          </div>

          <div className="flex flex-row items-center gap-2">
            <h2
              className={`text-base font-[500] ${
                statusFilter === "Canceled"
                  ? "text-red-600 underline"
                  : "text-black"
              } cursor-pointer`}
              onClick={() => handleStatusFilter("Canceled")}
            >
              Canceled
            </h2>
            <div
              className={`h-6 w-6 border-solid ${
                statusFilter === "Canceled" ? "border-red-600" : "border-black"
              } border-[1px] rounded-full flex items-center justify-center`}
            >
              <span
                className={`text-xs font-[500] ${statusFilter === "Canceled" ? "text-red-600" : "text-black"}`}
              >
                {statusCounts.Canceled}
              </span>
            </div>
          </div>

          <div className="flex flex-row items-center gap-2">
            <h2
              className={`text-base font-[500] ${
                statusFilter === "Upcoming"
                  ? "text-yellow-600 underline"
                  : "text-black"
              } cursor-pointer`}
              onClick={() => handleStatusFilter("Upcoming")}
            >
              Upcoming
            </h2>
            <div
              className={`h-6 w-6 border-solid ${
                statusFilter === "Upcoming"
                  ? "border-yellow-600"
                  : "border-black"
              } border-[1px] rounded-full flex items-center justify-center`}
            >
              <span
                className={`text-xs font-[500] ${statusFilter === "Upcoming" ? "text-yellow-600" : "text-black"}`}
              >
                {statusCounts.Upcoming}
              </span>
            </div>
          </div>
        </div> */}
      </div>

      <Table>
        <TableHeader className="bg-[#DFE0E2]">
          <TableRow>
            <TableHead>Payment ID</TableHead>
            <TableHead>Patient Name</TableHead>
            {/* <TableHead>Location</TableHead> */}
            <TableHead>Service</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Appointment Status</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Time</TableHead>
            <TableHead>Payment Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {paginatedData.map((payment) => (
            <TableRow key={payment.id}>
              <TableCell>{payment.id}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2 flex-row">
                  <div className="h-9	w-9">
                    <Avatar className="w-full h-full object-cover ">
                      <AvatarImage
                        src={payment?.profileImage || DefaultAvatar}
                      />
                      <AvatarFallback>{`${payment.patient}`}</AvatarFallback>
                    </Avatar>
                  </div>
                  {payment.patient}
                </div>
              </TableCell>
              {/* <TableCell>{payment.location}</TableCell> */}
              <TableCell>{payment.service}</TableCell>
              <TableCell>{payment.amount}</TableCell>
              <TableCell>
                <span
                  className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    statusColors[payment.status] || "bg-gray-100 text-gray-600"
                  }`}
                >
                  {payment.status}
                </span>
              </TableCell>
              <TableCell>{payment.formattedDate}</TableCell>
              <TableCell>{payment.time}</TableCell>
              <TableCell>
                <span
                  className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    paymentStatusColors[payment.paymentStatus] ||
                    "bg-gray-100 text-gray-600"
                  }`}
                >
                  {payment.paymentStatus}
                </span>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {filteredData.length === 0 && (
        <div className="py-8 text-center text-gray-500">
          No payments found matching your filters.
        </div>
      )}

      {filteredData.length > 0 && (
        <div className="flex justify-between items-center mt-4 w-full">
          <Pagination className="mt-4">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                />
              </PaginationItem>

              {totalPages <= 5 ? (
                // If 5 or fewer pages, show all page numbers
                Array.from({ length: totalPages }, (_, i) => (
                  <PaginationItem key={i}>
                    <PaginationLink
                      isActive={currentPage === i + 1}
                      onClick={() => handlePageChange(i + 1)}
                    >
                      {i + 1}
                    </PaginationLink>
                  </PaginationItem>
                ))
              ) : (
                // If more than 5 pages, show limited page numbers with ellipsis
                <>
                  {/* First page always shown */}
                  <PaginationItem>
                    <PaginationLink
                      isActive={currentPage === 1}
                      onClick={() => handlePageChange(1)}
                    >
                      1
                    </PaginationLink>
                  </PaginationItem>

                  {/* Show ellipsis if current page is > 3 */}
                  {currentPage > 3 && (
                    <PaginationItem>
                      <span className="flex h-9 w-9 items-center justify-center text-sm">
                        ...
                      </span>
                    </PaginationItem>
                  )}

                  {/* Pages around current page */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter((page) => {
                      if (page === 1 || page === totalPages) return false; // Skip first and last pages (handled separately)
                      return Math.abs(page - currentPage) < 2; // Show pages within 1 of current page
                    })
                    .map((page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          isActive={currentPage === page}
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    ))}

                  {/* Show ellipsis if current page is < totalPages - 2 */}
                  {currentPage < totalPages - 2 && (
                    <PaginationItem>
                      <span className="flex h-9 w-9 items-center justify-center text-sm">
                        ...
                      </span>
                    </PaginationItem>
                  )}

                  {/* Last page always shown */}
                  <PaginationItem>
                    <PaginationLink
                      isActive={currentPage === totalPages}
                      onClick={() => handlePageChange(totalPages)}
                    >
                      {totalPages}
                    </PaginationLink>
                  </PaginationItem>
                </>
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
};

export default PaymentsTable;
