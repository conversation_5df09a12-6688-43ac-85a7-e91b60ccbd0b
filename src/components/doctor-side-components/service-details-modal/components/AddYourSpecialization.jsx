import React, { useState } from "react";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "../../../../components/ui/select";
import { X, Info } from "lucide-react";
import { doctorSpecialties } from "../../../../constants/doctorSpecialties";

const AddYourSpecialization = ({ specializations, setSpecializations }) => {
  const allSpecializations = doctorSpecialties.map((item) => item.name);
  const [selectedSpecialty, setSelectedSpecialty] = useState("");
  const handleAddSpecialization = (selectedValue) => {
    if (selectedValue && specializations.length < 2) {
      setSpecializations([...specializations, selectedValue]);
      setSelectedSpecialty("");
    }
  };

  const handleRemoveSpecialization = (index) => {
    const updated = specializations.filter((_, i) => i !== index);
    setSpecializations(updated);
  };

  return (
    <div className="border border-solid border-border p-4 rounded-lg">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <Select
            onValueChange={(value) => {
              setSelectedSpecialty(value);
              handleAddSpecialization(value);
            }}
            value={selectedSpecialty}
            disabled={specializations.length >= 2}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select specialty" />
            </SelectTrigger>
            <SelectContent>
              {allSpecializations
                .filter((spec) => !specializations.includes(spec))
                .map((spec, index) => (
                  <SelectItem key={index} value={spec}>
                    {spec}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
          <p className="text-gray-500 flex items-center mt-2 text-[14px] text-[#1E1E1E80]">
            <Info className="w-4 h-4 mr-1" /> You can add a maximum of two
            specializations
          </p>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mt-4">
        {specializations.map((spec, index) => (
          <div
            key={index}
            className="flex items-center gap-2 px-3 py-1 rounded-full border-[1px] border-solid border-border"
          >
            <span>{spec}</span>
            <div
              className="h-[24px] w-[24px] flex items-center justify-center bg-[#1E1E1E80] rounded-full cursor-pointer"
              onClick={() => handleRemoveSpecialization(index)}
            >
              <X className="w-4 h-4 text-white" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AddYourSpecialization;
