import React from "react";
import { useState } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "../../../ui/button";
import { Input } from "../../../ui/input";
import {
  FormField,
  FormItem,
  FormControl,
  FormMessage,
} from "../../../ui/form";
import { ReactComponent as ServicesIcon } from "../../../../assets/svgs/ServicesIcon.svg";
import { ReactComponent as PriceIcon } from "../../../../assets/svgs/PriceIcon.svg";
import { Edit, X } from "lucide-react";

const serviceSchema = z.object({
  serviceName: z.string().min(1, "Service name is required"),
  price: z.coerce
    .number({ invalid_type_error: "Price must be a number" })
    .gt(0, "Price must be more than zero"),
});

const AddYourServicesAndPrice = ({ services, setServices }) => {
  const [editingIndex, setEditingIndex] = useState(null);

  const methods = useForm({
    resolver: zodResolver(serviceSchema),
    defaultValues: { serviceName: "", price: 0 },
    mode: "onSubmit",
  });

  const {
    control,
    handleSubmit,
    reset,
    formState: { errors, isSubmitting },
  } = methods;

  const onSubmit = (values) => {
    if (editingIndex !== null) {
      const updatedServices = [...services];
      updatedServices[editingIndex] = values;
      setServices(updatedServices);
      setEditingIndex(null);
    } else {
      setServices([...services, values]);
    }
    reset();
  };

  const handleEditService = (index) => {
    const service = services[index];
    methods.setValue("serviceName", service.serviceName);
    methods.setValue("price", service.price);
    setEditingIndex(index);
  };

  const handleRemoveService = (index) => {
    setServices(services.filter((_, i) => i !== index));
    if (editingIndex === index) {
      reset();
      setEditingIndex(null);
    }
  };

  return (
    <div className="border border-solid border-border p-4 rounded-lg">
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 ">
            <FormField
              control={control}
              name="serviceName"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="relative">
                      <ServicesIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary z-10" />
                      <Input
                        {...field}
                        placeholder="Enter service name"
                        className="pl-10"
                      />
                    </div>
                  </FormControl>
                  {errors.serviceName && (
                    <FormMessage>{errors.serviceName.message}</FormMessage>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="relative">
                      <PriceIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary z-10" />
                      <Input
                        type="number"
                        {...field}
                        placeholder="Enter price"
                        className="pl-10"
                        onChange={(e) =>
                          field.onChange(
                            e.target.value === "" ? "" : Number(e.target.value),
                          )
                        }
                      />
                    </div>
                  </FormControl>
                  {errors.price && (
                    <FormMessage>{errors.price.message}</FormMessage>
                  )}
                </FormItem>
              )}
            />

            <div className="flex justify-end">
              <Button variant="primary" type="submit" disabled={isSubmitting}>
                {editingIndex !== null ? "Update" : "Add"}
              </Button>
            </div>
          </div>
        </form>
      </FormProvider>

      <div className="mt-6 flex flex-wrap gap-2">
        {services.map((service, index) => (
          <div
            key={index}
            className="flex items-center gap-3 border border-gray-300 rounded-full px-4 py-2"
          >
            <span className="text-black font-medium">
              {service.serviceName}{" "}
              <span className="text-gray-500">${service.price}</span>
            </span>
            <Edit
              size={18}
              className="cursor-pointer text-gray-500 hover:text-gray-700"
              onClick={() => handleEditService(index)}
            />

            <div
              className="h-[24px] w-[24px] flex items-center justify-center bg-[#1E1E1E80] rounded-full cursor-pointer"
              onClick={() => handleRemoveService(index)}
            >
              <X className="w-4 h-4 text-white" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AddYourServicesAndPrice;
