import React from "react";
import { useState, useEffect } from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../../../components/ui/accordion";
import AddYourSpecialization from "./components/AddYourSpecialization";
import AddYourServicesAndPrice from "./components/AddYourServicesAndPrice";
import { Button } from "../../../components/ui/button";
import { useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import ActivityLoaderComponent from "../../../components/animations/LoadingSpinner";
import {
  getDoctorConsultations_api,
  updateDoctorConsultations_api,
} from "../../../api/api_calls/consultation_apiCalls";
import { toast } from "../../../hooks/use-toast";
import { updateDoctor_api } from "../../../api/api_calls/doctor_apiCalls";
import { me_api } from "../../../api/api_calls/auth_apiCalls";
import { setUser } from "../../../redux/slices/userSlice";

const ServiceDetailsModal = () => {
  const dispatch = useDispatch();
  const user = useSelector((state) => state.userReducer.user);
  const navigate = useNavigate();

  const [services, setServices] = useState([]);
  const [specializations, setSpecializations] = useState(
    user?.speciality || [],
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    setIsLoading(true);
    try {
      const res = await getDoctorConsultations_api();
      setServices(res.data);
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN fetchServices  => ", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async () => {
    if (specializations.length < 1 || services.length < 1) {
      return;
    }

    setIsSubmitting(true);
    try {
      const updateDoctorRes = await updateDoctor_api({
        doctorData: {
          speciality: specializations,
        },
      });
      const updateConsultation = await updateDoctorConsultations_api({
        consultations: services,
      });

      const res = await me_api();
      dispatch(setUser(res.data));
      if (updateDoctorRes.data && updateConsultation.data) {
        navigate("/doctor/profileUnderReview");
      }
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN handleSubmit  => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 relative">
      {(isLoading || isSubmitting) && (
        <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-70 z-10">
          <ActivityLoaderComponent />
        </div>
      )}

      <Accordion type="single" collapsible className="w-full space-y-4">
        <AccordionItem
          value={"specializations"}
          className="rounded-lg px-0 [&[data-state=open]]:bg-white"
        >
          <AccordionTrigger className="flex items-center justify-between px-4 py-3 hover:no-underline [&[data-state=open]>div>div]:bg-blue-500 [&[data-state=open]>div>div]:rotate-0">
            <div className="flex items-center gap-2">
              <span className="font-semibold text-[16px]">Specializations</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 pt-2">
            <AddYourSpecialization
              specializations={specializations}
              setSpecializations={setSpecializations}
            />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
      <Accordion type="single" collapsible className="w-full space-y-4">
        <AccordionItem
          value={"services"}
          className="rounded-lg px-0 [&[data-state=open]]:bg-white"
        >
          <AccordionTrigger className="flex items-center justify-between px-4 py-3 hover:no-underline [&[data-state=open]>div>div]:bg-blue-500 [&[data-state=open]>div>div]:rotate-0">
            <div className="flex items-center gap-2">
              <span className="font-semibold text-[16px]">
                Services & Prices
              </span>
              <span className="text-sm text-gray-500">{`(per session)`}</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4 pt-2">
            <AddYourServicesAndPrice
              services={services}
              setServices={setServices}
            />
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <div className="flex justify-end">
        <Button
          onClick={handleSubmit}
          disabled={
            specializations.length < 1 || services.length < 1 || isSubmitting
          }
          className="bg-blue-600 text-white"
        >
          {isSubmitting ? "Submitting..." : "Submit"}
        </Button>
      </div>
    </div>
  );
};

export default ServiceDetailsModal;
