import React, { useState } from "react";
import Chart from "react-apexcharts";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../ui/select";

const AppointmentSummaryChart = () => {
  const [timeframe, setTimeframe] = useState("this-week");
  const lastWeekData = [50, 30, 20];
  const thisWeekData = [40, 40, 20];
  const thisMonthData = [150, 120, 50];

  const chartData =
    timeframe === "last-week"
      ? lastWeekData
      : timeframe === "this-week"
        ? thisWeekData
        : thisMonthData;

  const chartOptions = {
    chart: {
      type: "donut",
    },
    labels: [
      "Upcoming Appointments",
      "Completed Appointments",
      "Cancelled Appointments",
    ],
    colors: ["#2B50ED", "#10CD00", "#FDBA74"],
    legend: {
      show: false,
    },
    dataLabels: {
      enabled: true,
      formatter: (val) => `${val}%`,
    },
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md w-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold">Appointment Summary</h2>

        <Select onValueChange={setTimeframe}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="This Week" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="last-week">Last Week</SelectItem>
            <SelectItem value="this-week">This Week</SelectItem>
            <SelectItem value="this-month">This Month</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Chart
        options={chartOptions}
        series={chartData}
        type="donut"
        height={250}
      />

      <div className="mt-4 space-y-2">
        <div className="flex items-center gap-2">
          <span className="w-3 h-3 bg-[#2B50ED] rounded-full"></span>
          <p className="text-sm text-gray-700">Upcoming Appointments</p>
        </div>
        <div className="flex items-center gap-2">
          <span className="w-3 h-3 bg-[#10CD00] rounded-full"></span>
          <p className="text-sm text-gray-700">Completed Appointments</p>
        </div>
        <div className="flex items-center gap-2">
          <span className="w-3 h-3 bg-[#FDBA74] rounded-full"></span>
          <p className="text-sm text-gray-700">Cancelled Appointments</p>
        </div>
      </div>
    </div>
  );
};

export default AppointmentSummaryChart;
