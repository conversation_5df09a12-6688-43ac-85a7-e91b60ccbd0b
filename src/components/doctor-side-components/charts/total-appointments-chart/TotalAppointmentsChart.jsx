import React, { useState } from "react";
import Chart from "react-apexcharts";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../ui/select";

const TotalAppointmentsChart = () => {
  const [timeframe, setTimeframe] = useState("this-week");

  const lastWeekData = {
    categories: [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ],
    values: [15, 30, 25, 80, 20, 35, 10],
  };

  const thisWeekData = {
    categories: [
      "Monday",
      "Tuesday",
      "Wednesday",
      "Thursday",
      "Friday",
      "Saturday",
      "Sunday",
    ],
    values: [10, 40, 20, 90, 10, 40, 15],
  };

  const thisMonthData = {
    categories: ["Week 1", "Week 2", "Week 3", "Week 4"],
    values: [150, 200, 180, 220],
  };

  const chartData =
    timeframe === "last-week"
      ? lastWeekData
      : timeframe === "this-week"
        ? thisWeekData
        : thisMonthData;

  const chartOptions = {
    chart: {
      type: "bar",
      toolbar: { show: false },
    },
    xaxis: {
      categories: chartData.categories,
    },
    plotOptions: {
      bar: {
        columnWidth: "50%",
      },
    },
    colors: ["#0052FD"],
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-4">
        <div>
          <h2 className="text-lg font-semibold">Total Appointments</h2>
          <h1 className="text-[16px] font-semibold text-[#1E1E1EB2]">
            {chartData.values.reduce((a, b) => a + b, 0)}
          </h1>
        </div>

        <Select onValueChange={setTimeframe}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="This Week" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="last-week">Last Week</SelectItem>
            <SelectItem value="this-week">This Week</SelectItem>
            <SelectItem value="this-month">This Month</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Chart
        options={chartOptions}
        series={[{ data: chartData.values }]}
        type="bar"
        height={300}
      />
    </div>
  );
};

export default TotalAppointmentsChart;
