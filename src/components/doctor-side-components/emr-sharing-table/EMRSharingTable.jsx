import { useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { Input } from "../../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationLink,
} from "../../ui/pagination";
import { Button } from "../../ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "../../ui/avatar";
import { ReactComponent as Eye } from "../../../assets/svgs/eye.svg";
import { useNavigate } from "react-router-dom";
import { getSharedEmrs_api } from "../../../api/api_calls/emr_apiCalls";
import { toast } from "../../../hooks/use-toast";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";

const EMRSharingTable = () => {
  const [search, setSearch] = useState("");
  const [dateFilter, setDateFilter] = useState("All Time");
  const [currentPage, setCurrentPage] = useState(1);
  const [sharedEmrs, setSharedEmrs] = useState([]);
  const [totalRecords, setTotalRecords] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const itemsPerPage = 20;
  const navigate = useNavigate();

  // Get logged-in user from Redux store
  const user = useSelector((state) => state.userReducer.user);

  // Helper function to get date range for filters
  const getDateRange = (filter) => {
    const today = new Date();
    const getWeekRange = (date) => {
      const day = date.getDay();
      const diff = date.getDate() - day + (day === 0 ? -6 : 1);
      const monday = new Date(date);
      monday.setDate(diff);
      monday.setHours(0, 0, 0, 0);

      const sunday = new Date(monday);
      sunday.setDate(monday.getDate() + 6);
      sunday.setHours(23, 59, 59, 999);

      return { start: monday, end: sunday };
    };

    switch (filter) {
      case "This Week": {
        const range = getWeekRange(today);
        return {
          startDate: range.start.toISOString(),
          endDate: range.end.toISOString(),
        };
      }
      case "Last Week": {
        const thisWeek = getWeekRange(today);
        const lastWeekStart = new Date(thisWeek.start);
        lastWeekStart.setDate(lastWeekStart.getDate() - 7);
        const lastWeekEnd = new Date(thisWeek.start);
        lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
        lastWeekEnd.setHours(23, 59, 59, 999);
        return {
          startDate: lastWeekStart.toISOString(),
          endDate: lastWeekEnd.toISOString(),
        };
      }
      default:
        return {};
    }
  };

  // Fetch shared EMRs from API
  const fetchSharedEmrs = async () => {
    setIsLoading(true);
    try {
      const dateRange = getDateRange(dateFilter);
      const params = {
        searchText: search || undefined,
        ...dateRange,
        offset: (currentPage - 1) * itemsPerPage,
        limit: itemsPerPage,
      };

      const response = await getSharedEmrs_api(params);
      setSharedEmrs(response.data.sharedEmrs || []);
      setTotalRecords(response.data.total || 0);
    } catch (error) {
      console.error("Error fetching shared EMRs:", error);
      toast({
        description: "Failed to fetch shared EMRs. Please try again.",
        variant: "destructive",
      });
      setSharedEmrs([]);
      setTotalRecords(0);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when component mounts or filters change
  useEffect(() => {
    fetchSharedEmrs();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dateFilter, currentPage]);

  // Debounce search to avoid too many API calls
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (currentPage === 1) {
        fetchSharedEmrs();
      } else {
        setCurrentPage(1); // This will trigger fetchSharedEmrs via the other useEffect
      }
    }, 500);

    return () => clearTimeout(timeoutId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [search]);

  // Helper functions for rendering
  const isLoggedInDoctor = (doctor) => {
    return user && doctor && doctor.id === user.id;
  };

  const getDoctorDisplayName = (doctor) => {
    if (!doctor) return "N/A";
    const name = `${doctor.firstName || ""} ${doctor.lastName || ""}`.trim();
    return isLoggedInDoctor(doctor) ? `${name} (me)` : name;
  };

  const handleDoctorClick = (doctor) => {
    if (!isLoggedInDoctor(doctor)) {
      navigate("/doctor/doctor-view-doctor", {
        state: { doctorId: doctor.id },
      });
    }
  };

  const handlePatientClick = (patient) => {
    navigate("/doctor/patient-profile", { state: { patientId: patient.id } });
  };

  const totalPages = Math.ceil(totalRecords / itemsPerPage);

  const handlePageChange = (page) => {
    // Prevent navigation beyond boundaries
    if (page < 1 || page > totalPages) {
      return;
    }
    setCurrentPage(page);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold">List of shared patient EMR</h2>
        <div className="flex gap-4 items-center">
          <Input
            placeholder="Search patient, doctor, or medical concern"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-64"
          />
          <Select
            value={dateFilter}
            onValueChange={(value) => {
              setDateFilter(value);
              setCurrentPage(1); // Reset to first page when filter changes
            }}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All Time">All Time</SelectItem>
              <SelectItem value="This Week">This Week</SelectItem>
              <SelectItem value="Last Week">Last Week</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <Table>
        <TableHeader className="bg-[#DFE0E2]">
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Patient Name</TableHead>
            <TableHead>Medical Concern</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Time</TableHead>
            <TableHead>Shared By</TableHead>
            <TableHead>Shared To</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading ? (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-8">
                Loading shared EMRs...
              </TableCell>
            </TableRow>
          ) : sharedEmrs.length === 0 ? (
            <TableRow>
              <TableCell colSpan={8} className="text-center py-8 text-gray-500">
                No EMR records found.
              </TableCell>
            </TableRow>
          ) : (
            sharedEmrs.map((record) => (
              <TableRow key={record.id}>
                <TableCell>{record.id || "N/A"}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={
                          record.patient?.profilePicture?.url || DefaultAvatar
                        }
                        alt={`${record.patient?.firstName || ""} ${record.patient?.lastName || ""}`}
                      />
                      <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
                        {`${record.patient?.firstName?.[0] || ""}${record.patient?.lastName?.[0] || ""}`}
                      </AvatarFallback>
                    </Avatar>
                    <span
                      className="cursor-pointer hover:text-blue-600 transition-colors"
                      onClick={() => handlePatientClick(record.patient)}
                    >
                      {record.patient
                        ? `${record.patient.firstName || ""} ${record.patient.lastName || ""}`.trim()
                        : "N/A"}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  {record.patient?.medicalConcerns || "N/A"}
                </TableCell>
                <TableCell>
                  {record.createdAt
                    ? new Date(record.createdAt).toLocaleDateString("en-GB")
                    : "N/A"}
                </TableCell>
                <TableCell>
                  {record.createdAt
                    ? new Date(record.createdAt).toLocaleTimeString("en-US", {
                        hour: "numeric",
                        minute: "2-digit",
                        hour12: true,
                        timeZone: "UTC",
                      })
                    : "N/A"}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={
                          record.sharedBy?.profilePicture?.url || DefaultAvatar
                        }
                        alt={`${record.sharedBy?.firstName || ""} ${record.sharedBy?.lastName || ""}`}
                      />
                      <AvatarFallback className="bg-green-100 text-green-600 text-xs">
                        {`${record.sharedBy?.firstName?.[0] || ""}${record.sharedBy?.lastName?.[0] || ""}`}
                      </AvatarFallback>
                    </Avatar>
                    <span
                      className={`${
                        isLoggedInDoctor(record.sharedBy)
                          ? "cursor-default"
                          : "cursor-pointer hover:text-blue-600 transition-colors"
                      }`}
                      onClick={() => handleDoctorClick(record.sharedBy)}
                    >
                      {getDoctorDisplayName(record.sharedBy)}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={
                          record.sharedTo?.profilePicture?.url || DefaultAvatar
                        }
                        alt={`${record.sharedTo?.firstName || ""} ${record.sharedTo?.lastName || ""}`}
                      />
                      <AvatarFallback className="bg-purple-100 text-purple-600 text-xs">
                        {`${record.sharedTo?.firstName?.[0] || ""}${record.sharedTo?.lastName?.[0] || ""}`}
                      </AvatarFallback>
                    </Avatar>
                    <span
                      className={`${
                        isLoggedInDoctor(record.sharedTo)
                          ? "cursor-default"
                          : "cursor-pointer hover:text-blue-600 transition-colors"
                      }`}
                      onClick={() => handleDoctorClick(record.sharedTo)}
                    >
                      {getDoctorDisplayName(record.sharedTo)}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="default"
                    onClick={() => handlePatientClick(record.patient)}
                  >
                    <Eye />
                  </Button>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>

      {!isLoading && totalRecords > 0 && (
        <div className="mt-4 w-full">
          <div className="text-sm text-gray-500 mb-4">
            Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
            {Math.min(currentPage * itemsPerPage, totalRecords)} of{" "}
            {totalRecords} EMR records
          </div>
          <div className="flex justify-end">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => {
                      if (currentPage > 1) {
                        handlePageChange(currentPage - 1);
                      }
                    }}
                    disabled={currentPage === 1}
                    className={
                      currentPage === 1
                        ? "opacity-50 cursor-not-allowed"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>

                {/* Show up to 5 page numbers */}
                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  let pageNumber;
                  if (totalPages <= 5) {
                    pageNumber = i + 1;
                  } else {
                    // Calculate which pages to show based on current page
                    if (currentPage <= 3) {
                      pageNumber = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNumber = totalPages - 4 + i;
                    } else {
                      pageNumber = currentPage - 2 + i;
                    }
                  }

                  return (
                    <PaginationItem key={pageNumber}>
                      <PaginationLink
                        isActive={currentPage === pageNumber}
                        onClick={() => handlePageChange(pageNumber)}
                        className="cursor-pointer"
                      >
                        {pageNumber}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => {
                      if (currentPage < totalPages) {
                        handlePageChange(currentPage + 1);
                      }
                    }}
                    disabled={currentPage === totalPages}
                    className={
                      currentPage === totalPages
                        ? "opacity-50 cursor-not-allowed"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
    </div>
  );
};

export default EMRSharingTable;
