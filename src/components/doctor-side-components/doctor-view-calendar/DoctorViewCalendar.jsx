import React, { useState, useEffect } from "react";
import FullCalendar from "@fullcalendar/react";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import interactionPlugin from "@fullcalendar/interaction";

const DoctorViewCalendar = () => {
  const [events, setEvents] = useState([]);
  useEffect(() => {
    const data = {
      data: [
        {
          date: "2025-04-04",
          isOverride: false,
          slots: [
            { start: "09:00:00", end: "09:30:00" },
            { start: "09:30:00", end: "09:45:00" },
            { start: "09:45:00", end: "09:50:00" },
            { start: "09:50:00", end: "10:00:00" },
          ],
        },
        {
          date: "2025-04-05",
          isOverride: false,
          slots: [
            { start: "09:00:00", end: "09:15:00" },
            { start: "09:15:00", end: "09:30:00" },
            { start: "09:30:00", end: "09:45:00" },
            { start: "09:45:00", end: "10:00:00" },
          ],
        },
        {
          date: "2025-04-06",
          isOverride: false,
          slots: [
            { start: "09:00:00", end: "09:15:00" },
            { start: "09:15:00", end: "09:30:00" },
            { start: "09:30:00", end: "09:45:00" },
            { start: "09:45:00", end: "10:00:00" },
          ],
        },
        {
          date: "2025-04-07",
          isOverride: false,
          slots: [
            { start: "09:00:00", end: "09:15:00" },
            { start: "09:15:00", end: "09:30:00" },
            { start: "09:30:00", end: "09:45:00" },
            { start: "09:45:00", end: "10:00:00" },
          ],
        },
        {
          date: "2025-04-08",
          isOverride: false,
          slots: [
            { start: "09:00:00", end: "09:15:00" },
            { start: "09:15:00", end: "09:30:00" },
            { start: "09:30:00", end: "09:45:00" },
            { start: "09:45:00", end: "10:00:00" },
          ],
        },
        {
          date: "2025-04-09",
          isOverride: false,
          slots: [
            { start: "09:00:00", end: "09:15:00" },
            { start: "09:15:00", end: "09:30:00" },
            { start: "09:30:00", end: "09:45:00" },
            { start: "09:45:00", end: "10:00:00" },
          ],
        },
        {
          date: "2025-04-10",
          isOverride: false,
          slots: [
            { start: "09:00:00", end: "09:15:00" },
            { start: "09:15:00", end: "09:30:00" },
            { start: "09:30:00", end: "09:45:00" },
            { start: "09:45:00", end: "10:00:00" },
          ],
        },
      ],
    };

    // Transform the data into FullCalendar events format
    const transformedEvents = data.data.flatMap((day) => {
      return day.slots.map((slot) => ({
        title: "",
        start: `${day.date}T${slot.start}`,
        end: `${day.date}T${slot.end}`,
        allDay: false,
        color: day.isOverride ? "#FF5733" : "#28a745", // Different color for overridden slots
        extendedProps: {
          isOverride: day.isOverride,
        },
      }));
    });

    setEvents(transformedEvents);
  }, []);

  const today = new Date();
  const [selectedYear, setSelectedYear] = useState(today.getFullYear());
  const [month, setMonth] = useState(today);

  const currentYear = today.getFullYear();
  const years = [];

  for (let i = currentYear; i <= currentYear + 10; i++) {
    years.push(i);
  }

  useEffect(() => {
    const newMonth = new Date(selectedYear, month.getMonth());
    if (newMonth < new Date(currentYear, today.getMonth())) {
      setMonth(today);
    } else {
      setMonth(newMonth);
    }
  }, [selectedYear]);

  const handleYearChange = (value) => {
    const year = parseInt(value, 10);
    setSelectedYear(year);
  };

  const handleMonthChange = (newMonth) => {
    const currentDate = new Date();
    const firstDayOfNewMonth = new Date(
      newMonth.getFullYear(),
      newMonth.getMonth(),
      1,
    );
    const firstDayOfCurrentMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      1,
    );

    if (firstDayOfNewMonth >= firstDayOfCurrentMonth) {
      setMonth(newMonth);
    } else {
      setMonth(currentDate);
    }
  };

  return (
    <div className="p-2 bg-white rounded-lg shadow-md">
      <FullCalendar
        plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
        initialView="timeGridWeek"
        headerToolbar={{
          left: "prev,next today",
          center: "title",
          right: "dayGridMonth,timeGridWeek,timeGridDay",
        }}
        events={events}
        eventDisplay="block"
        eventTimeFormat={{
          hour: "2-digit",
          minute: "2-digit",
          hour12: false,
        }}
        height="auto"
        slotDuration="00:15:00"
        slotMinTime="00:00:00"
        slotMaxTime="24:00:00"
        nowIndicator={true}
        editable={true}
        selectable={true}
        selectMirror={true}
        dayMaxEvents={true}
        weekends={true}
      />
    </div>
  );
};

export default DoctorViewCalendar;
