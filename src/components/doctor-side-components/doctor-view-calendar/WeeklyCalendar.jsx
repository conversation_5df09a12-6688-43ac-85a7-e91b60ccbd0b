import React, { useState, useMemo, useEffect } from "react";
import { format, startOfWeek, addDays, parse, addMonths } from "date-fns";
import { getSlotDetailsOfDoctor_api } from "../../../api/api_calls/schedule_apiCalls";
import { toast } from "../../../hooks/use-toast";
import { getUserTimezone } from "../../../helper/timezone-helper";

const WeeklyCalendar = ({
  doctorId,
  minimalMode,
  setSelectedDate,
  selectedDate,
  onBack,
}) => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [slotDetails, setSlotDetails] = useState({ data: [] });
  const [currentViewStartDate, setCurrentViewStartDate] = useState(new Date());
  const [requestedTimezone, setRequestedTimezone] = useState("");

  // Detect doctor's timezone
  useEffect(() => {
    const timezone = getUserTimezone();
    setRequestedTimezone(timezone);
  }, []);

  // Hook to detect screen size and determine days to show
  const [daysToShow, setDaysToShow] = useState(7);

  useEffect(() => {
    const updateDaysToShow = () => {
      const width = window.innerWidth;
      if (width < 640) {
        // sm breakpoint
        setDaysToShow(1);
      } else if (width < 768) {
        // md breakpoint
        setDaysToShow(2);
      } else if (width < 1024) {
        // lg breakpoint
        setDaysToShow(3);
      } else if (width < 1280) {
        // xl breakpoint
        setDaysToShow(4);
      } else {
        setDaysToShow(7);
      }
    };

    updateDaysToShow();
    window.addEventListener("resize", updateDaysToShow);
    return () => window.removeEventListener("resize", updateDaysToShow);
  }, []);

  // Initialize currentViewStartDate when daysToShow changes
  useEffect(() => {
    if (daysToShow === 7) {
      // For full week view, use Monday as start
      setCurrentViewStartDate(startOfWeek(currentDate, { weekStartsOn: 1 }));
    } else {
      // For other views, start from current date
      setCurrentViewStartDate(currentDate);
    }
  }, [daysToShow, currentDate]);

  const weekStart = useMemo(
    () => startOfWeek(currentDate, { weekStartsOn: 1 }),
    [currentDate],
  );
  const weekEnd = useMemo(() => addDays(weekStart, 6), [weekStart]);

  const slotMap = useMemo(() => {
    const map = {};
    if (slotDetails && slotDetails.data) {
      slotDetails.data.forEach((item) => {
        map[item.date] = item.slots;
      });
    }
    return map;
  }, [slotDetails]);

  // Generate dates to display based on screen size
  const displayDates = useMemo(() => {
    return Array.from({ length: daysToShow }, (_, i) =>
      addDays(currentViewStartDate, i),
    );
  }, [currentViewStartDate, daysToShow]);

  const handlePrevWeek = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (daysToShow === 7) {
      // Full week navigation
      const newDate = addDays(weekStart, -7);
      if (newDate < today) {
        setCurrentDate(today);
      } else {
        setCurrentDate(addDays(weekStart, -7));
      }
    } else {
      // Navigate by the number of days currently shown
      const newStartDate = addDays(currentViewStartDate, -daysToShow);
      if (newStartDate < today) {
        setCurrentViewStartDate(today);
        setCurrentDate(today);
      } else {
        setCurrentViewStartDate(newStartDate);
        setCurrentDate(newStartDate);
      }
    }
  };

  const handleNextWeek = () => {
    if (daysToShow === 7) {
      // Full week navigation
      setCurrentDate(addDays(weekStart, 7));
    } else {
      // Navigate by the number of days currently shown
      const newStartDate = addDays(currentViewStartDate, daysToShow);
      setCurrentViewStartDate(newStartDate);
      setCurrentDate(newStartDate);
    }
  };

  const handleMonthYearChange = (e) => {
    const [year, month] = e.target.value.split("-");
    const newDate = new Date(Number(year), Number(month) - 1, 1);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (newDate < today) {
      setCurrentDate(today);
    } else {
      setCurrentDate(newDate);
    }
  };

  const getSlotDetails = async (startDate, endDate) => {
    try {
      const res = await getSlotDetailsOfDoctor_api({
        doctorId: doctorId || null,
        startDate,
        endDate,
        requestedTimezone: requestedTimezone || null,
      });
      setSlotDetails(res);
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN getSlotDetails  => ", error);
    }
  };

  const getDateValue = (slotTime, dateRecieved) => {
    const date = new Date(dateRecieved);
    // Handle both old format (string) and new format (object with startTime)
    const timeString =
      typeof slotTime === "string" ? slotTime : slotTime.startTime;
    const [hours, minutes, seconds] = timeString.split(":").map(Number);
    date.setHours(hours);
    date.setMinutes(minutes);
    date.setSeconds(seconds);
    return date;
  };
  const handleSlotClick = (slotTime, dateRecieved) => {
    // Handle both old format (string) and new format (object with startTime)
    const timeString =
      typeof slotTime === "string" ? slotTime : slotTime.startTime;
    setSelectedDate(getDateValue(timeString, dateRecieved));
  };

  useEffect(() => {
    if (displayDates.length > 0 && requestedTimezone) {
      const startDate = displayDates[0];
      const endDate = displayDates[displayDates.length - 1];
      const formattedStart = format(startDate, "yyyy-MM-dd");
      const formattedEnd = format(endDate, "yyyy-MM-dd");
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (startDate < today) {
        const formattedToday = format(today, "yyyy-MM-dd");
        getSlotDetails(formattedToday, formattedEnd);
      } else {
        getSlotDetails(formattedStart, formattedEnd);
      }
      console.log("slotDetails:", slotDetails);
    }
  }, [currentViewStartDate, daysToShow, requestedTimezone]);

  return (
    <div className="p-2 sm:p-4">
      {/* Header Section - Responsive */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4 gap-3">
        {!minimalMode && (
          <div className="flex items-center justify-center sm:justify-start">
            <select
              value={format(currentDate, "yyyy-MM")}
              onChange={handleMonthYearChange}
              className="border rounded p-2 text-lg sm:text-xl lg:text-2xl font-bold w-full sm:w-auto"
            >
              {Array.from({ length: 6 }, (_, i) => {
                const optionDate = addMonths(new Date(), i);
                return (
                  <option
                    key={format(optionDate, "yyyy-MM")}
                    value={format(optionDate, "yyyy-MM")}
                  >
                    {format(optionDate, "MMMM yyyy")}
                  </option>
                );
              })}
            </select>
          </div>
        )}

        {/* Navigation Buttons */}
        <div
          className={`flex flex-row justify-center ${
            minimalMode
              ? "flex-1 justify-between"
              : "items-center gap-2 sm:gap-4"
          }`}
        >
          <button
            onClick={handlePrevWeek}
            className="text-gray-700 hover:text-black py-2 px-3 sm:px-4 border-2 rounded-lg text-lg sm:text-xl touch-manipulation"
          >
            &larr;
          </button>
          <button
            onClick={handleNextWeek}
            className="text-gray-700 hover:text-black py-2 px-3 sm:px-4 border-2 rounded-lg text-lg sm:text-xl touch-manipulation"
          >
            &rarr;
          </button>
        </div>
      </div>

      {/* Responsive Calendar Grid */}
      <div
        className={`grid gap-2 sm:gap-3 lg:gap-1 min-h-[400px] sm:min-h-[500px] lg:min-h-[600px] ${
          daysToShow === 1
            ? "grid-cols-1"
            : daysToShow === 2
              ? "grid-cols-2"
              : daysToShow === 3
                ? "grid-cols-3"
                : daysToShow === 4
                  ? "grid-cols-4"
                  : "grid-cols-7"
        }`}
      >
        {displayDates.map((date) => {
          const dateStr = format(date, "yyyy-MM-dd");
          const dayName = format(date, "EEE");
          const dayNum = format(date, "dd");
          const monthName = format(date, "MMM");
          const slots = slotMap[dateStr] || [];

          return (
            <div
              key={dateStr}
              className="rounded p-1 sm:p-2 flex flex-col border border-gray-200 bg-gray-50"
            >
              {/* Day Header */}
              <div
                className={`${
                  dateStr === format(new Date(), "yyyy-MM-dd")
                    ? "bg-green-100"
                    : "bg-white"
                } p-1 sm:p-2 rounded-t ${
                  minimalMode ? "flex justify-center flex-col items-center" : ""
                }`}
              >
                <div className="text-xs sm:text-sm text-gray-600 font-medium">
                  {dayName}
                </div>
                {minimalMode ? (
                  <div className="flex flex-row gap-1 sm:gap-2 text-sm sm:text-base">
                    <div className="font-semibold">{monthName}</div>
                    <div className="font-semibold">{dayNum}</div>
                  </div>
                ) : (
                  <div className="font-semibold text-sm sm:text-base">
                    {dayNum}
                  </div>
                )}
              </div>
              {/* SLOTS */}
              <div className="mt-1 sm:mt-2 w-full flex flex-1">
                {slots.length > 0 ? (
                  <div className="space-y-1 flex-1">
                    {slots
                      .slice()
                      .filter((slot) => {
                        // Handle both old format (string) and new format (object)
                        if (typeof slot === "string") {
                          return slot.match(/^\d{2}:\d{2}:\d{2}$/);
                        } else if (typeof slot === "object" && slot.startTime) {
                          return slot.startTime.match(/^\d{2}:\d{2}:\d{2}$/);
                        }
                        return false;
                      }) // Ensure valid HH:mm:ss format
                      .sort((a, b) => {
                        // Handle both old format (string) and new format (object)
                        const timeA = typeof a === "string" ? a : a.startTime;
                        const timeB = typeof b === "string" ? b : b.startTime;
                        const parsedA = parse(timeA, "HH:mm:ss", new Date());
                        const parsedB = parse(timeB, "HH:mm:ss", new Date());
                        return parsedA - parsedB;
                      })
                      .map((slot) => {
                        // Handle both old format (string) and new format (object)
                        const slotTime =
                          typeof slot === "string" ? slot : slot.startTime;
                        const parsedTime = parse(
                          slotTime,
                          "HH:mm:ss",
                          new Date(),
                        );
                        return (
                          <div
                            key={slotTime}
                            className={`bg-white border-[1px] border-[#000000] text-black rounded px-1 sm:px-2 py-1 sm:py-2 text-center text-xs sm:text-sm transition-colors duration-200 ${
                              minimalMode && "cursor-pointer hover:bg-gray-100"
                            } ${
                              minimalMode &&
                              new Date(selectedDate).getTime() ===
                                getDateValue(slot, date).getTime()
                                ? "bg-sky-300 hover:bg-sky-400"
                                : ""
                            }`}
                            onClick={() => {
                              if (minimalMode) {
                                handleSlotClick(slot, date);
                              }
                            }}
                          >
                            <span className="block sm:hidden">
                              {format(parsedTime, "h:mm")}
                            </span>
                            <span className="hidden sm:block">
                              {format(parsedTime, "h:mm aa")}
                            </span>
                          </div>
                        );
                      })}
                  </div>
                ) : (
                  <div className="relative flex-1 border border-gray-300 rounded-lg overflow-hidden min-h-[80px] sm:min-h-[120px]">
                    <div className="absolute inset-0 z-0 bg-[repeating-linear-gradient(45deg,_#e5e5e5_0,_#e5e5e5_5px,_#ffffff_5px,_#ffffff_15px)] pointer-events-none"></div>
                    <div className="relative flex items-center justify-center h-full z-10">
                      <span className="text-gray-700 text-sm sm:text-lg font-bold transform -rotate-90 whitespace-nowrap">
                        Not Available
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default WeeklyCalendar;
