import React, { useEffect, useState, useRef } from "react";
import FadeInSection from "../../animations/FadeInSection";
import { Input } from "../../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../ui/table";
import { Eye, Loader2 } from "lucide-react";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "../../ui/pagination";
import { useNavigate } from "react-router-dom";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import { Avatar, AvatarFallback, AvatarImage } from "../../ui/avatar";
import { Button } from "../../ui/button";
import { useSelector } from "react-redux";
import { getAppointmentsByDoctorId_apiCalls } from "../../../api/api_calls/appointment_apiCalls";
import { useToast } from "../../../hooks/use-toast";
import LoadingSpinner from "../../animations/LoadingSpinner";
import { formatDateShort, formatTime } from "../../../helpers/dateTimeHelpers";

const ITEMS_PER_PAGE = 20;

const AppointmentsTable = () => {
  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [dateFilter, setDateFilter] = useState("All Time");
  const [currentPage, setCurrentPage] = useState(1);
  const [statusFilter, setStatusFilter] = useState(null);
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [totalCount, setTotalCount] = useState(0);
  const [statusCounts, setStatusCounts] = useState({
    upcoming: 0,
    completed: 0,
    cancelled: 0,
  });

  // Hooks
  const navigate = useNavigate();
  const { toast } = useToast();
  const user = useSelector((state) => state.userReducer.user);

  // Get doctor ID - the user object IS the doctor entity
  const doctorId = user?.id;

  // Ref for search timeout
  const searchTimeoutRef = useRef(null);

  // Function to fetch appointments
  const fetchAppointments = async () => {
    if (!doctorId) return;

    setLoading(true);
    try {
      // Calculate date range for API call
      let startDate, endDate;

      const today = new Date();

      if (dateFilter === "This Week") {
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay() + 1); // Monday
        startDate = startOfWeek.toISOString().split("T")[0];

        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6); // Sunday
        endDate = endOfWeek.toISOString().split("T")[0];
      } else if (dateFilter === "Last Week") {
        const startOfLastWeek = new Date(today);
        startOfLastWeek.setDate(today.getDate() - today.getDay() - 6); // Last Monday
        startDate = startOfLastWeek.toISOString().split("T")[0];

        const endOfLastWeek = new Date(startOfLastWeek);
        endOfLastWeek.setDate(startOfLastWeek.getDate() + 6); // Last Sunday
        endDate = endOfLastWeek.toISOString().split("T")[0];
      } else if (dateFilter === "Next Week") {
        const startOfNextWeek = new Date(today);
        startOfNextWeek.setDate(today.getDate() - today.getDay() + 8); // Next Monday
        startDate = startOfNextWeek.toISOString().split("T")[0];

        const endOfNextWeek = new Date(startOfNextWeek);
        endOfNextWeek.setDate(startOfNextWeek.getDate() + 6); // Next Sunday
        endDate = endOfNextWeek.toISOString().split("T")[0];
      }

      // Map frontend status to backend enum
      let appointmentStatus = null;
      if (statusFilter === "Upcoming") appointmentStatus = "upcoming";
      else if (statusFilter === "Completed") appointmentStatus = "completed";
      else if (statusFilter === "Canceled") appointmentStatus = "cancelled";

      const response = await getAppointmentsByDoctorId_apiCalls({
        offset: (currentPage - 1) * ITEMS_PER_PAGE,
        limit: ITEMS_PER_PAGE,
        appointmentStatus,
        searchText: searchQuery.trim() || undefined,
        startDate,
        endDate,
      });
      console.log("response :", response);
      if (response?.data) {
        setAppointments(response.data.appointments || []);
        setTotalCount(response.data.total || 0);

        // Extract status counts from API response
        setStatusCounts({
          upcoming: response.data.totalUpcomingAppointments || 0,
          completed: response.data.totalCompletedAppointments || 0,
          cancelled: response.data.totalCancelledAppointments || 0,
        });
      }
    } catch (error) {
      console.error("Failed to fetch appointments:", error);
      toast({
        title: "Error",
        description: "Failed to fetch appointments. Please try again.",
        variant: "destructive",
      });
      setAppointments([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  // Effect to fetch appointments when dependencies change (excluding search)
  useEffect(() => {
    fetchAppointments();
  }, [doctorId, currentPage, statusFilter, dateFilter]);

  // Effect to reset page when filters change (excluding searchQuery to avoid immediate API calls)
  useEffect(() => {
    setCurrentPage(1);
  }, [dateFilter, statusFilter]);

  // Debounced search effect - wait 500ms after user stops typing, minimum 3 characters
  useEffect(() => {
    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    // Set new timeout
    searchTimeoutRef.current = setTimeout(() => {
      // Call API if search is empty (show all) or has at least 3 characters
      if (searchQuery.trim() === "" || searchQuery.trim().length >= 3) {
        setCurrentPage(1);
        // Use a small delay to ensure page state is updated before fetching
        setTimeout(() => {
          fetchAppointments();
        }, 10);
      }
    }, 500); // 500ms debounce

    // Cleanup function
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchQuery]); // Only depend on searchQuery

  // Handler functions
  const handleStatusFilter = (status) => {
    // Toggle functionality: if same status is clicked, deselect it
    if (statusFilter === status) {
      setStatusFilter(null);
    } else {
      setStatusFilter(status);
    }
    setCurrentPage(1);
  };

  const clearFilters = () => {
    setSearchQuery("");
    setDateFilter("All Time");
    setStatusFilter(null);
    setCurrentPage(1);
  };

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Calculate total pages
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);

  // Helper functions for data formatting
  const getAppointmentId = (appointment) => {
    return appointment?.id || appointment?.appointmentId || "N/A";
  };

  const getPatientName = (appointment) => {
    if (appointment?.patient) {
      return (
        `${appointment.patient.firstName || ""} ${appointment.patient.lastName || ""}`.trim() ||
        "Unknown Patient"
      );
    }
    return "Unknown Patient";
  };

  const getPatientImage = (appointment) => {
    return appointment?.patient?.profilePicture?.url || DefaultAvatar;
  };

  const getServiceName = (appointment) => {
    return (
      appointment?.consultation?.serviceName || appointment?.service || "N/A"
    );
  };

  // Using imported helper functions for date and time formatting

  const formatStatus = (status) => {
    if (!status) return "Pending";
    switch (status.toLowerCase()) {
      case "upcoming":
        return "Upcoming";
      case "completed":
        return "Completed";
      case "cancelled":
        return "Canceled";
      default:
        return status.charAt(0).toUpperCase() + status.slice(1).toLowerCase();
    }
  };

  const getStatusBadgeClass = (status) => {
    if (!status) return "bg-gray-100 text-gray-600";
    switch (status.toLowerCase()) {
      case "upcoming":
        return "bg-yellow-100 text-yellow-600";
      case "completed":
        return "bg-blue-100 text-blue-600";
      case "cancelled":
        return "bg-red-100 text-red-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <FadeInSection>
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center justify-center">
            <div className="flex flex-row items-center gap-8">
              <div className="flex flex-row items-center gap-2">
                <h2
                  className={`text-lg font-[500] ${statusFilter === "Upcoming" ? "text-yellow-600 underline" : "text-black"} cursor-pointer`}
                  onClick={() => handleStatusFilter("Upcoming")}
                >
                  Upcoming
                </h2>
                <div
                  className={`h-7 w-7 border-solid ${statusFilter === "Upcoming" ? "border-yellow-600" : "border-black"} border-[1px] rounded-full flex items-center justify-center`}
                >
                  <span
                    className={`text-xs font-[500] ${statusFilter === "Upcoming" ? "text-yellow-600" : "text-black"}`}
                  >
                    {statusCounts.upcoming}
                  </span>
                </div>
              </div>

              <div className="flex flex-row items-center gap-2">
                <h2
                  className={`text-lg font-[500] ${statusFilter === "Completed" ? "text-blue-600 underline" : "text-black"} cursor-pointer`}
                  onClick={() => handleStatusFilter("Completed")}
                >
                  Completed
                </h2>
                <div
                  className={`h-7 w-7 border-solid ${statusFilter === "Completed" ? "border-blue-600" : "border-black"} border-[1px] rounded-full flex items-center justify-center`}
                >
                  <span
                    className={`text-xs font-[500] ${statusFilter === "Completed" ? "text-blue-600" : "text-black"}`}
                  >
                    {statusCounts.completed}
                  </span>
                </div>
              </div>

              <div className="flex flex-row items-center gap-2">
                <h2
                  className={`text-lg font-[500] ${statusFilter === "Canceled" ? "text-red-600 underline" : "text-black"} cursor-pointer`}
                  onClick={() => handleStatusFilter("Canceled")}
                >
                  Canceled
                </h2>
                <div
                  className={`h-7 w-7 border-solid ${statusFilter === "Canceled" ? "border-red-600" : "border-black"} border-[1px] rounded-full flex items-center justify-center`}
                >
                  <span
                    className={`text-xs font-[500] ${statusFilter === "Canceled" ? "text-red-600" : "text-black"}`}
                  >
                    {statusCounts.cancelled}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div className="flex gap-4 items-center">
            <div className="relative">
              <Input
                placeholder="Search here"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-64"
              />
              {searchQuery.trim().length > 0 &&
                searchQuery.trim().length < 3 && (
                  <div className="absolute top-full left-0 mt-1 text-xs text-gray-500">
                    Type at least 3 characters to search
                  </div>
                )}
            </div>
            <Select
              value={dateFilter}
              onValueChange={(value) => {
                setDateFilter(value);
                setCurrentPage(1);
              }}
            >
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All Time">All Time</SelectItem>
                <SelectItem value="This Week">This Week</SelectItem>
                <SelectItem value="Last Week">Last Week</SelectItem>
                <SelectItem value="Next Week">Next Week</SelectItem>
              </SelectContent>
            </Select>
            {(searchQuery || dateFilter !== "All Time" || statusFilter) && (
              <Button variant="outline" onClick={clearFilters} size="sm">
                Clear Filters
              </Button>
            )}
          </div>
        </div>

        <div>
          <Table>
            <TableHeader className="bg-[#DFE0E2]">
              <TableRow>
                <TableHead>Appointment ID</TableHead>
                <TableHead>Patient Name</TableHead>
                <TableHead>Service</TableHead>
                <TableHead>Appointment Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Payment Status</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="flex justify-center items-center">
                      <Loader2 className="h-6 w-6 animate-spin mr-2" />
                      Loading appointments...
                    </div>
                  </TableCell>
                </TableRow>
              ) : appointments.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center py-8 text-gray-500"
                  >
                    No appointments found.
                  </TableCell>
                </TableRow>
              ) : (
                appointments.map((appointment, index) => (
                  <TableRow key={getAppointmentId(appointment) || index}>
                    <TableCell>{getAppointmentId(appointment)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2 flex-row">
                        <div className="h-9 w-9">
                          <Avatar className="w-full h-full object-cover">
                            <AvatarImage src={getPatientImage(appointment)} />
                            <AvatarFallback>
                              {getPatientName(appointment)
                                .split(" ")
                                .map((name) => name[0])
                                .join("")
                                .toUpperCase() || "PT"}
                            </AvatarFallback>
                          </Avatar>
                        </div>
                        {getPatientName(appointment)}
                      </div>
                    </TableCell>
                    <TableCell>{getServiceName(appointment)}</TableCell>
                    <TableCell>
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                          appointment.appointmentStatus,
                        )}`}
                      >
                        {formatStatus(appointment.appointmentStatus)}
                      </span>
                    </TableCell>
                    <TableCell>
                      {formatDateShort(appointment.startDateTime)}
                    </TableCell>
                    <TableCell>
                      {formatTime(appointment.startDateTime)}
                    </TableCell>
                    <TableCell>
                      <span
                        className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                          appointment.paymentStatus || "Pending",
                        )}`}
                      >
                        {appointment.paymentStatus || "Pending"}
                      </span>
                    </TableCell>
                    <TableCell>
                      <button
                        className="text-gray-500 hover:text-blue-600"
                        onClick={() =>
                          navigate("/doctor/appointments-details", {
                            state: {
                              appointmentId: getAppointmentId(appointment),
                            },
                          })
                        }
                      >
                        <Eye size={20} />
                      </button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>

          {/* Always show pagination area when there are appointments */}
          {!loading && appointments.length > 0 && (
            <div className="flex justify-between items-center mt-4 w-full">
              <div className="text-sm text-gray-600">
                Showing {appointments.length} of {totalCount} appointments
                {(searchQuery.trim() ||
                  statusFilter !== "Upcoming" ||
                  dateFilter !== "All Time") &&
                  " (filtered)"}
              </div>

              {/* Only show pagination controls if more than 1 page */}
              {totalPages > 1 && (
                <Pagination className="mt-4">
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                      />
                    </PaginationItem>
                    {Array.from({ length: totalPages }, (_, i) => (
                      <PaginationItem key={i}>
                        <PaginationLink
                          isActive={currentPage === i + 1}
                          onClick={() => handlePageChange(i + 1)}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))}
                    <PaginationItem>
                      <PaginationNext
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === totalPages}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              )}
            </div>
          )}
        </div>
      </FadeInSection>
    </div>
  );
};

export default AppointmentsTable;
