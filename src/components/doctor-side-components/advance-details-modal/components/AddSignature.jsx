import React from "react";
import { useState, useRef, useEffect } from "react";
import { X, Upload, Pen, FileUp } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from "../../../ui/tabs";
import { Button } from "../../../ui/button";
import { Card } from "../../../ui/card";
import DocMobileLogo from "../../../../assets/images/DocMobilLogo.png";

const DrawingCanvas = ({ selectedColor, onSignatureChange }) => {
  const canvasRef = useRef(null);
  const [isDrawing, setIsDrawing] = useState(false);
  const [ctx, setCtx] = useState(null);
  const [lastX, setLastX] = useState(0);
  const [lastY, setLastY] = useState(0);
  const [points, setPoints] = useState([]);
  const [isDrawingEmpty, setIsDrawingEmpty] = useState(true);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    const rect = canvas.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;

    canvas.style.width = `${rect.width}px`;
    canvas.style.height = `${rect.height}px`;

    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;

    const context = canvas.getContext("2d");
    if (context) {
      context.scale(dpr, dpr);
      context.lineJoin = "round";
      context.lineCap = "round";
      context.lineWidth = 3;
      context.strokeStyle = selectedColor === "blue" ? "#0066FF" : "#000000";
      setCtx(context);
    }
  }, [selectedColor]);

  useEffect(() => {
    const handleResize = () => {
      if (canvasRef.current) {
        const currentDrawing = isDrawingEmpty
          ? null
          : canvasRef.current.toDataURL();

        const canvas = canvasRef.current;
        const rect = canvas.getBoundingClientRect();
        const dpr = window.devicePixelRatio || 1;

        canvas.style.width = `${rect.width}px`;
        canvas.style.height = `${rect.height}px`;

        canvas.width = rect.width * dpr;
        canvas.height = rect.height * dpr;

        const context = canvas.getContext("2d");
        if (context) {
          context.scale(dpr, dpr);
          context.lineJoin = "round";
          context.lineCap = "round";
          context.lineWidth = 3;
          context.strokeStyle =
            selectedColor === "blue" ? "#0066FF" : "#000000";
          setCtx(context);

          if (currentDrawing) {
            const img = new Image();
            img.onload = () => {
              context.drawImage(
                img,
                0,
                0,
                canvas.width / dpr,
                canvas.height / dpr,
              );
            };
            img.src = currentDrawing;
          }
        }
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [isDrawingEmpty, selectedColor]);

  const startDrawing = (e) => {
    if (!ctx || !canvasRef.current) return;
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    setIsDrawing(true);
    setLastX(x);
    setLastY(y);
    setPoints([...points, { x, y }]);
    ctx.beginPath();
    ctx.arc(x, y, 1.5, 0, Math.PI * 2);
    ctx.fill();
    setIsDrawingEmpty(false);
    if (onSignatureChange) onSignatureChange(true);
  };

  const draw = (e) => {
    if (!isDrawing || !ctx || !canvasRef.current) return;
    const rect = canvasRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    ctx.beginPath();
    ctx.moveTo(lastX, lastY);
    ctx.lineTo(x, y);
    ctx.stroke();
    setLastX(x);
    setLastY(y);
    setPoints([...points, { x, y }]);
  };

  const endDrawing = () => {
    setIsDrawing(false);
  };

  const handleTouchStart = (e) => {
    e.preventDefault();
    if (!ctx || !canvasRef.current) return;
    const touch = e.touches[0];
    const rect = canvasRef.current.getBoundingClientRect();
    const x = touch.clientX - rect.left;
    const y = touch.clientY - rect.top;
    setIsDrawing(true);
    setLastX(x);
    setLastY(y);
    setPoints([...points, { x, y }]);
    ctx.beginPath();
    ctx.arc(x, y, 1.5, 0, Math.PI * 2);
    ctx.fill();
    setIsDrawingEmpty(false);
    if (onSignatureChange) onSignatureChange(true);
  };

  const handleTouchMove = (e) => {
    e.preventDefault();
    if (!isDrawing || !ctx || !canvasRef.current) return;
    const touch = e.touches[0];
    const rect = canvasRef.current.getBoundingClientRect();
    const x = touch.clientX - rect.left;
    const y = touch.clientY - rect.top;
    ctx.beginPath();
    ctx.moveTo(lastX, lastY);
    ctx.lineTo(x, y);
    ctx.stroke();
    setLastX(x);
    setLastY(y);
    setPoints([...points, { x, y }]);
  };

  const handleTouchEnd = (e) => {
    e.preventDefault();
    setIsDrawing(false);
  };

  const clearCanvas = () => {
    if (ctx && canvasRef.current) {
      const dpr = window.devicePixelRatio || 1;
      ctx.clearRect(
        0,
        0,
        canvasRef.current.width / dpr,
        canvasRef.current.height / dpr,
      );
      setPoints([]);
      setIsDrawingEmpty(true);
      if (onSignatureChange) onSignatureChange(false);
    }
  };

  return (
    <Card className="relative border rounded-lg mb-6 p-0 overflow-hidden">
      <div className="w-full h-48 relative">
        <canvas
          ref={canvasRef}
          className="w-full h-full touch-none"
          onMouseDown={startDrawing}
          onMouseMove={draw}
          onMouseUp={endDrawing}
          onMouseLeave={endDrawing}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
        />
      </div>
      <Button
        variant="destructive"
        size="icon"
        className="absolute top-2 right-2 h-6 w-6 rounded-full p-0"
        onClick={clearCanvas}
      >
        <X className="h-4 w-4" />
      </Button>
    </Card>
  );
};

const AddSignature = ({ onSave, onClose }) => {
  const [open, setOpen] = useState(true);
  const [activeTab, setActiveTab] = useState("draw");
  const [selectedColor, setSelectedColor] = useState("blue");
  const [uploadedImage, setUploadedImage] = useState(null);
  const [isDragging, setIsDragging] = useState(false);
  const [hasSignature, setHasSignature] = useState(false);
  const [logoImage, setLogoImage] = useState(null);
  const [logoLoaded, setLogoLoaded] = useState(false);
  const [canvasKey, setCanvasKey] = useState(0);
  const fileInputRef = useRef(null);
  const canvasRef = useRef(null);

  useEffect(() => {
    const img = new Image();
    img.onload = () => {
      setLogoImage(img);
      setLogoLoaded(true);
    };
    img.src = DocMobileLogo;
  }, []);

  const handleTabChange = (value) => {
    setActiveTab(value);
    if (value === "draw") {
      setCanvasKey((prevKey) => prevKey + 1);
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFile(file);
    }
  };

  const handleFile = (file) => {
    const validTypes = ["image/jpeg", "image/jpg", "image/png"];
    if (!file.type.startsWith("image/") || !validTypes.includes(file.type)) {
      alert("Please upload only JPEG or PNG images");
      return;
    }

    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        setUploadedImage(e.target.result);
        setHasSignature(true);
      }
    };
    reader.readAsDataURL(file);
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const clearUploadedImage = () => {
    setUploadedImage(null);
    setHasSignature(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const addWatermark = (ctx, canvas) => {
    if (!logoImage) return;
    const logoWidth = canvas.width * 0.7;
    const logoHeight = (logoImage.height / logoImage.width) * logoWidth;
    const x = (canvas.width - logoWidth) / 2;
    const y = (canvas.height - logoHeight) / 2;
    ctx.save();
    ctx.globalAlpha = 0.2;
    ctx.drawImage(logoImage, x, y, logoWidth, logoHeight);
    ctx.restore();
  };

  const createSignatureWithWatermark = () => {
    return new Promise((resolve, reject) => {
      if (!logoLoaded) {
        reject(
          new Error("Logo is still loading. Please try again in a moment."),
        );
        return;
      }

      try {
        if (activeTab === "draw" && canvasRef.current && hasSignature) {
          const originalCanvas = document.querySelector("canvas");
          if (!originalCanvas) {
            reject(new Error("Canvas not found"));
            return;
          }
          const tempCanvas = document.createElement("canvas");

          tempCanvas.width = originalCanvas.width;
          tempCanvas.height = originalCanvas.height;

          const tempCtx = tempCanvas.getContext("2d");
          addWatermark(tempCtx, tempCanvas);
          tempCtx.drawImage(originalCanvas, 0, 0);

          const dataUrl = tempCanvas.toDataURL("image/png", 1.0);
          resolve(dataUrl);
        } else if (activeTab === "upload" && uploadedImage) {
          const img = new Image();
          img.crossOrigin = "anonymous";
          img.onload = () => {
            const canvas = document.createElement("canvas");
            canvas.width = img.width;
            canvas.height = img.height;
            const ctx = canvas.getContext("2d");

            addWatermark(ctx, canvas);
            ctx.drawImage(img, 0, 0);

            const dataUrl = canvas.toDataURL("image/png", 1.0);
            resolve(dataUrl);
          };
          img.onerror = () => {
            reject(new Error("Failed to load the uploaded image"));
          };
          img.src = uploadedImage;
        } else {
          reject(new Error("No signature available"));
        }
      } catch (error) {
        reject(error);
      }
    });
  };

  const handleSaveSignature = async () => {
    try {
      const dataUrlWithWatermark = await createSignatureWithWatermark("png");

      const signatureData = {
        type: activeTab,
        dataUrl: dataUrlWithWatermark,
      };

      if (activeTab === "draw") {
        signatureData.color = selectedColor;
      }

      onSave(signatureData);
      setOpen(false);
      if (onClose) onClose();
    } catch (error) {
      alert(error.message || "Please create a signature before saving");
    }
  };

  const handleCancel = () => {
    setOpen(false);
    if (onClose) onClose();
  };

  const handleSignatureChange = (hasSignature) => {
    setHasSignature(hasSignature);
    canvasRef.current = document.querySelector("canvas");
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div
        className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[80vh] overflow-y-auto"
        open={open}
        // onOpenChange={setOpen}
      >
        <div className="sm:max-w-[800px]">
          <div className="flex justify-between items-center">
            <div className="text-2xl font-bold">Create Signature</div>
          </div>

          <Tabs
            defaultValue="draw"
            value={activeTab}
            onValueChange={handleTabChange}
            className="w-full mt-4"
          >
            <TabsList className="grid grid-cols-2">
              <TabsTrigger value="draw" className="flex items-center gap-2">
                <Pen className="w-4 h-4" />
                Draw
              </TabsTrigger>
              <TabsTrigger value="upload" className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                Upload
              </TabsTrigger>
            </TabsList>

            <TabsContent value="draw">
              <div className="flex justify-between items-center my-4">
                <div className="flex gap-2">
                  <button
                    className={`w-10 h-10 rounded-full ${selectedColor === "blue" ? "ring-2 ring-gray-400" : ""}`}
                    style={{ backgroundColor: "#0066FF" }}
                    onClick={() => setSelectedColor("blue")}
                    aria-label="Blue color"
                  />
                  <button
                    className={`w-10 h-10 rounded-full bg-black ${
                      selectedColor === "black" ? "ring-2 ring-gray-400" : ""
                    }`}
                    onClick={() => setSelectedColor("black")}
                    aria-label="Black color"
                  />
                </div>
              </div>

              <DrawingCanvas
                key={canvasKey}
                selectedColor={selectedColor}
                onSignatureChange={handleSignatureChange}
              />
            </TabsContent>

            <TabsContent value="upload">
              <Card className="relative border rounded-lg my-6 overflow-hidden">
                {!uploadedImage ? (
                  <div
                    className={`h-48 flex flex-col items-center justify-center p-4 ${
                      isDragging ? "bg-blue-50 border-blue-300" : ""
                    }`}
                    onDragOver={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setIsDragging(true);
                    }}
                    onDragEnter={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                    onDragLeave={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setIsDragging(false);
                    }}
                    onDrop={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      setIsDragging(false);
                      if (e.dataTransfer.files && e.dataTransfer.files[0]) {
                        handleFile(e.dataTransfer.files[0]);
                      }
                    }}
                  >
                    <FileUp className="w-10 h-10 text-gray-400 mb-2" />
                    <p className="text-gray-500 mb-2">
                      Drag and drop your signature image here
                    </p>
                    <Button variant="primary" onClick={triggerFileInput}>
                      Browse files
                    </Button>
                    <input
                      type="file"
                      ref={fileInputRef}
                      className="hidden"
                      accept=".jpeg,.jpg,.png"
                      onChange={handleFileChange}
                    />
                  </div>
                ) : (
                  <div className="relative h-48 flex items-center justify-center">
                    <img
                      src={uploadedImage || "/placeholder.svg"}
                      alt="Uploaded signature"
                      className="max-h-full max-w-full object-contain"
                    />
                    <Button
                      variant="destructive"
                      size="icon"
                      className="absolute top-2 right-2 h-6 w-6 rounded-full p-0"
                      onClick={clearUploadedImage}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                )}
              </Card>
            </TabsContent>
          </Tabs>

          <div className="w-full items-end justify-end flex gap-4">
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
            <Button
              onClick={handleSaveSignature}
              variant={"primary"}
              disabled={!hasSignature}
            >
              Save
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddSignature;
