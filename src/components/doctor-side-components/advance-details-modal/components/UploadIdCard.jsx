import React from "react";
import { useState, useMemo } from "react";
import { Button } from "../../../../components/ui/button";
import { Label } from "../../../ui/label";
import X from "../../../../assets/svgs/x.svg";
import { toast } from "../../../../hooks/use-toast";

const UploadIdCard = ({ initialFront, initialBack, onSave, onClose }) => {
  const [frontCard, setFrontCard] = useState(initialFront);
  const [backCard, setBackCard] = useState(initialBack);
  const [showFrontError, setShowFrontError] = useState(false);
  const [showBackError, setShowBackError] = useState(false);

  const isSaveDisabled = useMemo(() => {
    if (!frontCard && !backCard) return true;
    return frontCard === initialFront && backCard === initialBack;
  }, [frontCard, backCard, initialFront, initialBack]);

  const handleSave = () => {
    if (frontCard && backCard) {
      onSave(frontCard, backCard);
      toast({
        title: "Success",
        description: "ID cards have been successfully uploaded.",
      });
      setShowFrontError(false);
      setShowBackError(false);
    } else {
      if (!frontCard) setShowFrontError(true);
      if (!backCard) setShowBackError(true);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Please upload both front and back ID cards.",
      });
    }
  };

  const handleCancel = () => {
    onClose();
    toast({
      title: "Cancelled",
      description: "ID card upload has been cancelled.",
    });
  };

  const validateFileType = (file) => {
    if (!file) return true;
    const validTypes = ["image/jpeg", "image/jpg", "image/png"];
    if (!validTypes.includes(file.type)) {
      toast({
        variant: "destructive",
        title: "Invalid File Type",
        description: "Please upload only JPEG or PNG images.",
      });
      return false;
    }
    return true;
  };

  const handleFileChange = (side, file) => {
    if (file && validateFileType(file)) {
      if (side === "front") {
        setFrontCard(file);
        setShowFrontError(false);
        toast({
          title: "Front ID Uploaded",
          description: "Front side of ID card has been uploaded successfully.",
        });
      } else {
        setBackCard(file);
        setShowBackError(false);
        toast({
          title: "Back ID Uploaded",
          description: "Back side of ID card has been uploaded successfully.",
        });
      }
    }
  };

  const renderPreview = (file) => {
    return file ? (
      <img
        src={file.url ? file.url : URL.createObjectURL(file)}
        alt="Preview"
        className="h-full w-full object-cover rounded-lg"
      />
    ) : (
      <div className="flex flex-col items-center justify-center pt-5 pb-6">
        <svg
          className="w-8 h-8 mb-4 text-primary"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 20 16"
          aria-hidden="true"
        >
          <path
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
          />
        </svg>
        <p className="mb-2 text-sm text-gray-500">
          <span className="font-semibold text-primary">Drag and drop</span> your
          file here, or{" "}
          <span className="font-semibold text-primary">click</span>
        </p>
        <p className="text-xs text-gray-500">JPEG or PNG (MAX. 800x400px)</p>
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[80vh] overflow-y-auto transform transition-transform duration-300 translate-x-0 opacity-100">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-[#1E1E1E] font-bold text-[20px]">Add ID Card</h1>
          <img
            src={X || "/placeholder.svg"}
            alt="Close"
            className="cursor-pointer"
            onClick={handleCancel}
          />
        </div>
        <div className="border border-solid border-border p-4 rounded-lg">
          <form>
            {/* Front Side */}
            <div className="mb-6">
              <Label>Attach your ID Card Front Side</Label>
              <div className="w-full">
                <label
                  htmlFor="front-card"
                  className="flex flex-col items-center justify-center w-full h-40 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
                  onDragOver={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onDragEnter={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onDragLeave={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onDrop={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const file = e.dataTransfer.files[0];
                    if (file) {
                      handleFileChange("front", file);
                    }
                  }}
                >
                  {renderPreview(frontCard)}
                  <input
                    type="file"
                    id="front-card"
                    className="hidden"
                    accept=".jpeg,.jpg,.png"
                    onChange={(e) =>
                      handleFileChange("front", e.target.files[0] || null)
                    }
                  />
                </label>
              </div>
              {showFrontError && (
                <p className="text-xs text-red-500 mt-1">
                  * Required: Please upload the front side of your ID card.
                </p>
              )}
            </div>

            {/* Back Side */}
            <div className="mb-6">
              <Label>Attach your ID Card Back Side</Label>
              <div className="w-full">
                <label
                  htmlFor="back-card"
                  className="flex flex-col items-center justify-center w-full h-40 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
                  onDragOver={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onDragEnter={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onDragLeave={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                  }}
                  onDrop={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    const file = e.dataTransfer.files[0];
                    if (file) {
                      handleFileChange("back", file);
                    }
                  }}
                >
                  {renderPreview(backCard)}
                  <input
                    type="file"
                    id="back-card"
                    className="hidden"
                    accept=".jpeg,.jpg,.png"
                    onChange={(e) =>
                      handleFileChange("back", e.target.files[0] || null)
                    }
                  />
                </label>
              </div>
              {showBackError && (
                <p className="text-xs text-red-500 mt-1">
                  * Required: Please upload the back side of your ID card.
                </p>
              )}
            </div>

            {/* Buttons */}
            <div className="mt-8 flex justify-end gap-4">
              <Button variant="secondary" type="button" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                variant="primary"
                type="button"
                onClick={handleSave}
                disabled={isSaveDisabled}
              >
                Save & Next
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default UploadIdCard;
