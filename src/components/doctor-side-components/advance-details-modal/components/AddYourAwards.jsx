import React from "react";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "../../../../components/ui/form";
import { Textarea } from "../../../ui/textarea";
import { ReactComponent as AwardedBy } from "../../../../assets/svgs/InstituteIcon.svg";
import { ReactComponent as AwardsIcon } from "../../../../assets/svgs/AwardsIcon.svg";
import DatePicker from "../../../date-picker/DatePicker";
import { toast } from "../../../../hooks/use-toast";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

const formSchema = z.object({
  awardName: z
    .string()
    .min(1, "Award name is required")
    .min(3, "Award name atleast 3 characters")
    .max(30, "Award name should be at most 30 characters"),
  awardedBy: z
    .string()
    .min(1, "Award by is required")
    .min(3, "Award by atleast 3 characters")
    .max(30, "Award by should be at most 30 characters"),
  issueDate: z.string().min(1, "Issue date is required"),
  awardDetails: z
    .string()
    .min(1, "Award details are required")
    .min(20, "Award details should be at least 20 characters")
    .max(200, "Award details should be at most 200 characters"),
});

const AddYourAwards = ({ initialData, onSave, onClose }) => {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      awardName: "",
      awardedBy: "",
      issueDate: "",
      awardDetails: "",
    },
    mode: "onChange",
  });

  const {
    handleSubmit,
    reset,
    control,
    formState: { errors },
    watch,
  } = form;

  useEffect(() => {
    if (initialData) {
      reset(initialData);
    }
  }, [initialData, reset]);

  const formValues = watch();

  const hasChanges = initialData
    ? Object.keys(formValues).some(
        (key) =>
          formValues[key] !== initialData[key] &&
          !(formValues[key] === "" && initialData[key] === null),
      )
    : true;

  const isFieldEmpty = (value) =>
    value === undefined || value === null || value === "";

  const requiredFieldsEmpty = [
    formValues.awardName,
    formValues.awardedBy,
    formValues.issueDate,
    formValues.awardDetails,
  ].some(isFieldEmpty);

  const isButtonDisabled = requiredFieldsEmpty || (initialData && !hasChanges);

  const onSubmit = (data) => {
    if (data.issueDate && isNaN(new Date(data.issueDate).getTime())) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Invalid date format. Please select a valid date.",
      });
      return;
    }

    onSave(data);
    toast({
      title: "Success",
      description: `Award ${initialData ? "updated" : "saved"} successfully!`,
    });
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[80vh] overflow-y-auto">
        <Form {...form}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="border border-solid border-border p-4 rounded-lg">
              <div className="flex items-center justify-center flex-col w-full gap-4">
                {/* Award Title */}
                <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={control}
                    name="awardName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Award Title</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <AwardsIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                            <Input
                              {...field}
                              type="text"
                              placeholder="Enter award title"
                              className="pl-10"
                            />
                          </div>
                        </FormControl>
                        <FormMessage>{errors.awardName?.message}</FormMessage>
                      </FormItem>
                    )}
                  />

                  {/* Awarded By */}
                  <FormField
                    control={control}
                    name="awardedBy"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Awarded By</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <AwardedBy className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                            <Input
                              {...field}
                              type="text"
                              placeholder="Enter organization name"
                              className="pl-10"
                            />
                          </div>
                        </FormControl>
                        <FormMessage>{errors.awardedBy?.message}</FormMessage>
                      </FormItem>
                    )}
                  />
                </div>

                {/* Issue Date */}
                <div className="w-full grid grid-cols-1 md:grid-cols-1">
                  <FormField
                    control={control}
                    name="issueDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Issue Date</FormLabel>
                        <DatePicker field={field} />
                        <FormMessage>{errors.issueDate?.message}</FormMessage>
                      </FormItem>
                    )}
                  />
                </div>

                {/* Award Details */}
                <div className="w-full grid grid-cols-1 md:grid-cols-1">
                  <FormField
                    control={control}
                    name="awardDetails"
                    render={({ field }) => (
                      <FormItem className="col-span-2">
                        <FormLabel>Award Details</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Enter award detail..."
                            className="h-36"
                          />
                        </FormControl>
                        <FormMessage>
                          {errors.awardDetails?.message}
                        </FormMessage>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="md:col-span-2 flex justify-end gap-4 w-full">
                  <Button variant="secondary" type="button" onClick={onClose}>
                    Cancel
                  </Button>
                  <Button
                    variant="primary"
                    type="submit"
                    disabled={isButtonDisabled}
                  >
                    {initialData ? "Update" : "Save"} Award
                  </Button>
                </div>
              </div>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default AddYourAwards;
