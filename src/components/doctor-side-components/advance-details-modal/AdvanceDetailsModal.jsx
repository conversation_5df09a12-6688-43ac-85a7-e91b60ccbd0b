import React from "react";
import { useState, useMemo, useEffect } from "react";
import UploadIdCard from "./components/UploadIdCard";
import AddYourQualification from "./components/AddYourQualification";
import AddPractiseExperience from "./components/AddPractiseExpierience";
import AddMembershipDetails from "./components/AddMembershipDetails";
import AddYourAwards from "./components/AddYourAwards";
import AddSignature from "./components/AddSignature";
import { Button } from "../../ui/button";
import PlusCircle from "../../../assets/svgs/plus-circle.svg";
import EditBtn from "../../../assets/svgs/EditBtn.svg";
import DeleteBtn from "../../../assets/svgs/DeleteBtn.svg";
import { format } from "date-fns";
import { Plus } from "lucide-react";
import { updateDoctor_api } from "../../../api/api_calls/doctor_apiCalls";
import { toast } from "../../../hooks/use-toast";
import { useDispatch, useSelector } from "react-redux";
import ActivityLoaderComponent from "../../animations/LoadingSpinner";
import { me_api } from "../../../api/api_calls/auth_apiCalls";
import { setUser } from "../../../redux/slices/userSlice";

const AdvanceDetailsModal = ({ onNext }) => {
  const dispatch = useDispatch();
  const user = useSelector((state) => state.userReducer.user);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showAddYourQualification, setShowAddYourQualification] =
    useState(false);

  const [idFront, setIdFront] = useState(user.idFront || null);
  const [idBack, setIdBack] = useState(user.idBack || null);

  const [signature, setSignature] = useState(() => {
    if (user?.signature && user.signature.url) {
      return {
        type: "upload",
        dataUrl: user.signature.url,
        apiSignature: user.signature,
      };
    }
    return user?.signature || null;
  });

  const [showAddYourSignatur, setShowAddYourSignatur] = useState(false);

  const [qualifications, setQualifications] = useState(
    user.qualifications || [],
  );
  const [selectedQualification, setSelectedQualification] = useState(null);
  const [selectedQualificationIndex, setSelectedQualificationIndex] =
    useState(null);

  const [experiences, setExperiences] = useState(
    user.practiceExperiences || [],
  );
  const [selectedExperience, setSelectedExperience] = useState(null);
  const [selectedExperienceIndex, setSelectedExperienceIndex] = useState(null);

  const [awards, setAwards] = useState(user.awards || []);
  const [selectedAward, setSelectedAward] = useState(null);
  const [selectedAwardIndex, setSelectedAwardIndex] = useState(null);

  const [memberships, setMemberships] = useState(
    user.membershipDetails ? [{ details: user.membershipDetails }] : [],
  );
  const [selectedMembership, setSelectedMembership] = useState(null);
  const [selectedMembershipIndex, setSelectedMembershipIndex] = useState(null);

  const [showAwardModal, setShowAwardModal] = useState(false);
  const [showExperienceModal, setShowExperienceModal] = useState(false);
  const [showMembershipModal, setShowMembershipModal] = useState(false);

  const frontImageUrl = useMemo(
    () => idFront && (idFront.url ? idFront.url : URL.createObjectURL(idFront)),
    [idFront],
  );

  const backImageUrl = useMemo(
    () => idBack && (idBack.url ? idBack.url : URL.createObjectURL(idBack)),
    [idBack],
  );

  useEffect(() => {
    return () => {
      if (frontImageUrl && !frontImageUrl.startsWith("http"))
        URL.revokeObjectURL(frontImageUrl);
      if (backImageUrl && !backImageUrl.startsWith("http"))
        URL.revokeObjectURL(backImageUrl);
    };
  }, [frontImageUrl, backImageUrl]);

  const handleUploadSave = (newFront, newBack) => {
    setIdFront(newFront);
    setIdBack(newBack);
    setShowUploadModal(false);
  };

  const handleDelete = () => {
    setIdFront(null);
    setIdBack(null);
  };

  const handleQualificationSave = (data, degreeFile) => {
    const newQualification = {
      ...data,
      degreeFile: degreeFile,
    };

    if (
      selectedQualification &&
      (selectedQualificationIndex || selectedQualificationIndex === 0)
    ) {
      setQualifications((prev) => {
        const updatedQualifications = [...prev];
        updatedQualifications[selectedQualificationIndex] = newQualification;
        return updatedQualifications;
      });
    } else {
      setQualifications((prev) => [...prev, newQualification]);
    }
    setShowAddYourQualification(false);
    setSelectedQualification(null);
    setSelectedQualificationIndex(null);
  };

  const handleQualificationDelete = (index) => {
    setQualifications((prev) => prev.filter((q, idx) => idx !== index));
  };

  const handleQualificationEdit = (qualification, index) => {
    setSelectedQualification(qualification);
    setSelectedQualificationIndex(index);
    setShowAddYourQualification(true);
  };

  const handleSaveExperience = (data) => {
    const newExperience = { ...data };
    if (
      selectedExperience &&
      (selectedExperienceIndex || selectedExperienceIndex === 0)
    ) {
      setExperiences((prev) => {
        const updatedExperiences = [...prev];
        updatedExperiences[selectedExperienceIndex] = newExperience;
        return updatedExperiences;
      });
    } else {
      setExperiences((prev) => [...prev, newExperience]);
    }

    setShowExperienceModal(false);
    setSelectedExperience(null);
    setSelectedExperienceIndex(null);
  };

  const handleDeleteExperience = (index) => {
    setExperiences((prev) => prev.filter((e, idx) => idx !== index));
  };

  const handleEditExperience = (experience, index) => {
    setSelectedExperience(experience);
    setSelectedExperienceIndex(index);
    setShowExperienceModal(true);
  };

  const handleSaveAward = (data) => {
    const newAward = { ...data };
    if (selectedAward && (selectedAwardIndex || selectedAwardIndex === 0)) {
      setAwards((prev) => {
        const updatedAwards = [...prev];
        updatedAwards[selectedAwardIndex] = newAward;
        return updatedAwards;
      });
    } else {
      setAwards((prev) => [...prev, newAward]);
    }
    setShowAwardModal(false);
    setSelectedAward(null);
    setSelectedAwardIndex(null);
  };

  const handleDeleteAward = (index) => {
    setAwards((prev) => prev.filter((a, idx) => idx !== index));
  };

  const handleEditAward = (award, index) => {
    setSelectedAward(award);
    setSelectedAwardIndex(index);
    setShowAwardModal(true);
  };

  const handleSaveMembership = (data) => {
    const newMembership = { ...data };

    if (
      selectedMembership &&
      (selectedMembershipIndex || selectedMembershipIndex === 0)
    ) {
      setMemberships((prev) => {
        const updatedMemberships = [...prev];
        updatedMemberships[selectedMembershipIndex] = newMembership;
        return updatedMemberships;
      });
    } else {
      setMemberships((prev) => [...prev, newMembership]);
    }
    setShowMembershipModal(false);
    setSelectedMembership(null);
    setSelectedMembershipIndex(null);
  };

  const handleDeleteMembership = (index) => {
    setMemberships((prev) => prev.filter((m, idx) => idx !== index));
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      const qualificationDegreeFiles = [];
      const qualifications_final = [];

      const practiceExperienceFiles = [];
      const practiceExperiences_final = [];

      qualifications.map((qualification) => {
        qualifications_final.push({
          degreeName: qualification.degreeName,
          institutionName: qualification.institutionName,
          startDate: qualification.startDate,
          endDate: qualification.endDate,
          id: qualification.id,
        });
        qualificationDegreeFiles.push(qualification.degreeFile);
      });
      experiences.map((experience) => {
        practiceExperiences_final.push({
          hospitalName: experience.hospitalName,
          designation: experience.designation,
          description: experience.description,
          startDate: experience.startDate,
          endDate: experience.endDate,
          id: experience.id,
        });
        practiceExperienceFiles.push(experience.experienceFile);
      });

      let signatureFile = null;
      if (signature) {
        if (signature.dataUrl) {
          if (signature.apiSignature && signature.dataUrl.startsWith("http")) {
            signatureFile = null;
          } else {
            try {
              const fetchRes = await fetch(signature.dataUrl);
              const signatureBlob = await fetchRes.blob();
              signatureFile = new File([signatureBlob], "signature.png", {
                type: "image/png",
              });
            } catch (error) {
              console.error("Error converting signature to File:", error);
            }
          }
        }
      }

      console.log("PAYLOAD => ", {
        doctorData: {
          qualifications: qualifications_final,
          practiceExperiences: practiceExperiences_final,
          awards,
          membershipDetails: memberships[0]?.details || null,
        },
        idFront: idFront,
        idBack: idBack,
        qualificationDegreeFiles,
        practiceExperienceFiles,
        signature: signatureFile,
      });

      await updateDoctor_api({
        doctorData: {
          qualifications: qualifications_final,
          practiceExperiences: practiceExperiences_final,
          awards,
          membershipDetails: memberships[0]?.details || null,
        },
        idFront: idFront,
        idBack: idBack,
        qualificationDegreeFiles,
        practiceExperienceFiles,
        signature: signatureFile,
      });

      const res = await me_api();
      dispatch(setUser(res.data));
      if (onNext) onNext();
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN UPDATE  => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateForm = () => {
    if (!idFront || !idBack) return false;
    if (!signature) return false;
    if (qualifications.length === 0) return false;
    if (experiences.length === 0) return false;
    // if (awards.length === 0) return false;
    // if (memberships.length === 0) return false;
    return true;
  };

  return (
    <div className="bg-white p-8 rounded-lg w-full max-w-4xl relative">
      {isSubmitting && (
        <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-50 rounded-lg">
          <ActivityLoaderComponent />
        </div>
      )}

      {/* ID Card Upload Section */}
      <div>
        <div className="flex items-center justify-between">
          <h1 className="font-bold text-[20px] text-[#1E1E1E]">ID Card</h1>
          <div className="flex items-center flex-row gap-4">
            {idFront || idBack ? (
              <>
                <img
                  src={EditBtn || "/placeholder.svg"}
                  alt="Edit"
                  className="cursor-pointer"
                  onClick={() => setShowUploadModal(true)}
                />
                <img
                  src={DeleteBtn || "/placeholder.svg"}
                  alt="Delete"
                  className="cursor-pointer"
                  onClick={handleDelete}
                />
              </>
            ) : (
              <img
                src={PlusCircle || "/placeholder.svg"}
                alt="Add"
                className="cursor-pointer"
                onClick={() => setShowUploadModal(true)}
              />
            )}
          </div>
        </div>

        {/* Only Show Images When Uploaded */}
        {(idFront || idBack) && (
          <div className="flex items-center flex-row gap-4 mt-2">
            {idFront && (
              <div>
                <h1 className="font-semibold text-[16px] text-[#1E1E1E]">
                  Front Side
                </h1>
                <img
                  src={frontImageUrl || "/placeholder.svg"}
                  alt="Front ID"
                  className="h-[69px] w-[94px] rounded-lg"
                />
              </div>
            )}
            {idBack && (
              <div>
                <h1 className="font-semibold text-[16px] text-[#1E1E1E]">
                  Back Side
                </h1>
                <img
                  src={backImageUrl || "/placeholder.svg"}
                  alt="Back ID"
                  className="h-[69px] w-[94px] rounded-lg"
                />
              </div>
            )}
          </div>
        )}
      </div>

      {/* Sign Upload Section */}
      <div className="mt-6">
        <div className="flex items-center justify-between">
          <h1 className="font-bold text-[20px] text-[#1E1E1E]">
            Add Signature
          </h1>
          <div className="flex items-center flex-row gap-4">
            {signature ? (
              <>
                <img
                  src={EditBtn || "/placeholder.svg"}
                  alt="Edit"
                  className="cursor-pointer"
                  onClick={() => setShowAddYourSignatur(true)}
                />
                <img
                  src={DeleteBtn || "/placeholder.svg"}
                  alt="Delete"
                  className="cursor-pointer"
                  onClick={() => setSignature(null)}
                />
              </>
            ) : (
              <img
                src={PlusCircle || "/placeholder.svg"}
                alt="Add"
                className="cursor-pointer"
                onClick={() => setShowAddYourSignatur(true)}
              />
            )}
          </div>
        </div>

        {/* Show signature when available */}
        {signature && (
          <div className="mt-2">
            {signature.type === "draw" || signature.type === "upload" ? (
              <div>
                <img
                  src={signature.dataUrl || "/placeholder.svg"}
                  alt="Signature"
                  className="h-[69px] max-w-[200px] object-contain"
                />
              </div>
            ) : signature.type === "type" ? (
              <div
                className={`${signature.font} text-2xl`}
                style={{
                  color: signature.color === "blue" ? "#0066FF" : "#000000",
                }}
              >
                {signature.text}
              </div>
            ) : null}
          </div>
        )}
      </div>

      {/* Add Qualification Section */}
      <div className="mt-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="font-bold text-[20px] text-[#1E1E1E]">
            Qualifications
          </h1>

          {qualifications.length === 0 ? (
            <img
              src={PlusCircle || "/placeholder.svg"}
              alt="Add"
              className="cursor-pointer"
              onClick={() => {
                setSelectedQualification(null);
                setSelectedQualificationIndex(null);
                setShowAddYourQualification(true);
              }}
            />
          ) : (
            <Button
              variant={"secondary"}
              onClick={() => {
                setSelectedQualification(null);
                setSelectedQualificationIndex(null);
                setShowAddYourQualification(true);
              }}
            >
              <Plus strokeWidth={3} />
              Add Qualification
            </Button>
          )}
        </div>

        {qualifications.map((qualification, index) => (
          <div
            key={qualification.id || index}
            className="mb-4 rounded-lg relative"
          >
            <div className="flex justify-between items-center">
              <h3 className="font-semibold text-[16px] text-[#1E1E1E]">
                {qualification.institutionName}
              </h3>
              <div className="flex gap-2">
                <img
                  src={EditBtn || "/placeholder.svg"}
                  alt="Edit"
                  className="cursor-pointer"
                  onClick={() => handleQualificationEdit(qualification, index)}
                />
                <img
                  src={DeleteBtn || "/placeholder.svg"}
                  alt="Delete"
                  className="cursor-pointer"
                  onClick={() => handleQualificationDelete(index)}
                />
              </div>
            </div>
            <p className="text-[16px] text-[#1E1E1EB2] font-semibold">
              {qualification.degreeName}
            </p>
            <p className="text-[16px] text-[#1E1E1EB2] font-medium">
              {format(new Date(qualification.startDate), "MMM yyyy")} -
              {format(new Date(qualification.endDate), "MMM yyyy")}
            </p>
            {qualification.degreeFile && (
              <div className="">
                <p className="text-[16px] text-[#1E1E1EB2] font-semibold">
                  Certificate:
                </p>
                <img
                  src={
                    qualification.degreeFile.url
                      ? qualification.degreeFile.url
                      : URL.createObjectURL(qualification.degreeFile)
                  }
                  alt="Certificate preview"
                  className="h-20 w-20 object-cover rounded"
                />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Practice Experience Section */}
      <div className="mt-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="font-bold text-[20px] text-[#1E1E1E]">
            Practice Experience
          </h1>

          {experiences.length === 0 ? (
            <img
              src={PlusCircle || "/placeholder.svg"}
              alt="Add"
              className="cursor-pointer"
              onClick={() => {
                setSelectedExperience(null);
                setShowExperienceModal(true);
              }}
            />
          ) : (
            <Button
              variant={"secondary"}
              onClick={() => {
                setSelectedExperience(null);
                setShowExperienceModal(true);
              }}
            >
              <Plus strokeWidth={3} />
              Add Experience
            </Button>
          )}
        </div>

        {experiences.map((exp, index) => (
          <div key={exp.id || index} className="mb-4 rounded-lg relative">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold text-[16px] text-[#1E1E1E]">
                {exp.hospitalName}
              </h3>
              <div className="flex gap-2">
                <img
                  src={EditBtn || "/placeholder.svg"}
                  alt="Edit"
                  className="cursor-pointer"
                  onClick={() => handleEditExperience(exp, index)}
                />
                <img
                  src={DeleteBtn || "/placeholder.svg"}
                  alt="Delete"
                  className="cursor-pointer"
                  onClick={() => handleDeleteExperience(index)}
                />
              </div>
            </div>
            <p className="text-[16px] text-[#1E1E1EB2] font-semibold">
              {exp.designation}
            </p>
            <p className="text-[16px] text-[#1E1E1EB2] font-semibold">
              {format(new Date(exp.startDate), "MMM yyyy")} -
              {format(new Date(exp.endDate), "MMM yyyy")}
            </p>

            <p className="text-[16px] text-[#1E1E1E] font-[400]">
              {exp?.description}
            </p>

            {exp.experienceFile && (
              <div className="">
                <p className="text-[16px] text-[#1E1E1EB2] font-semibold">
                  Certificate:
                </p>
                <img
                  src={
                    exp.experienceFile.url
                      ? exp.experienceFile.url
                      : URL.createObjectURL(exp.experienceFile)
                  }
                  alt="Certificate preview"
                  className="h-20 w-20 object-cover rounded"
                />
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Add Awards Section */}
      <div className="mt-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="font-bold text-[20px] text-[#1E1E1E]">
            Awards{" "}
            <span className="font-normal text-[16px] text-gray-400">
              (optional)
            </span>
          </h1>

          {awards.length === 0 ? (
            <img
              src={PlusCircle || "/placeholder.svg"}
              alt="Add"
              className="cursor-pointer"
              onClick={() => {
                setSelectedAward(null);
                setSelectedAwardIndex(null);
                setShowAwardModal(true);
              }}
            />
          ) : (
            <Button
              variant={"secondary"}
              onClick={() => {
                setSelectedAward(null);
                setSelectedAwardIndex(null);
                setShowAwardModal(true);
              }}
            >
              <Plus strokeWidth={3} />
              Add Awards
            </Button>
          )}
        </div>

        {awards.map((award, index) => (
          <div key={award.id || index} className="mb-4 rounded-lg relative">
            <div className="flex justify-between items-center">
              <h3 className="font-semibold text-[16px] text-[#1E1E1E]">
                {award.awardName}
              </h3>
              <div className="flex gap-2">
                <img
                  src={EditBtn || "/placeholder.svg"}
                  alt="Edit"
                  className="cursor-pointer"
                  onClick={() => handleEditAward(award, index)}
                />
                <img
                  src={DeleteBtn || "/placeholder.svg"}
                  alt="Delete"
                  className="cursor-pointer"
                  onClick={() => handleDeleteAward(index)}
                />
              </div>
            </div>
            <p className="text-[16px] text-[#1E1E1EB2] font-semibold">
              {award.awardedBy}
            </p>
            <p className="text-[16px] text-[#1E1E1EB2] font-semibold">
              {format(new Date(award.issueDate), "MMM yyyy")}
            </p>
            {award.awardDetails && (
              <p className="text-[16px] text-[#1E1E1EB2] font-semibold">
                {award.awardDetails}
              </p>
            )}
          </div>
        ))}
      </div>

      {/* Memberships Section */}
      <div className="mt-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="font-bold text-[20px] text-[#1E1E1E]">
            Memberships{" "}
            <span className="font-normal text-[16px] text-gray-400">
              (optional)
            </span>
          </h1>
          {memberships.length === 0 ? (
            <img
              src={PlusCircle || "/placeholder.svg"}
              alt="Add"
              className="cursor-pointer"
              onClick={() => {
                setSelectedMembership(null);
                setSelectedMembershipIndex(null);
                setShowMembershipModal(true);
              }}
            />
          ) : (
            <div></div>
          )}
        </div>

        {memberships.map((membership, index) => (
          <div
            key={membership.id || index}
            className="mb-4 rounded-lg relative"
          >
            <div className="flex justify-between items-center">
              <p className="text-[16px] text-[#1E1E1EB2] font-normal w-9/12">
                {membership.details}
              </p>
              <div className="flex gap-2 w-3/12 items-end justify-end">
                <img
                  src={EditBtn || "/placeholder.svg"}
                  alt="Edit"
                  className="cursor-pointer"
                  onClick={() => {
                    setSelectedMembership(membership);
                    setSelectedMembershipIndex(index);
                    setShowMembershipModal(true);
                  }}
                />
                <img
                  src={DeleteBtn || "/placeholder.svg"}
                  alt="Delete"
                  className="cursor-pointer"
                  onClick={() => handleDeleteMembership(index)}
                />
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="w-full flex items-end justify-end">
        <Button
          onClick={() => {
            handleSubmit();
          }}
          variant={"primary"}
          disabled={isSubmitting || !validateForm()}
        >
          {isSubmitting ? "Submitting..." : "Next"}
        </Button>
      </div>

      {showUploadModal && (
        <UploadIdCard
          initialFront={idFront}
          initialBack={idBack}
          onSave={handleUploadSave}
          onClose={() => setShowUploadModal(false)}
        />
      )}

      {showAddYourSignatur && (
        <AddSignature
          initialData={signature}
          onSave={(signatureData) => {
            setSignature(signatureData);
            setShowAddYourSignatur(false);
          }}
          onClose={() => setShowAddYourSignatur(false)}
        />
      )}

      {showAddYourQualification && (
        <AddYourQualification
          qualification={selectedQualification}
          onSave={handleQualificationSave}
          onClose={() => {
            setShowAddYourQualification(false);
            setSelectedQualification(null);
            setSelectedQualificationIndex(null);
          }}
        />
      )}

      {showExperienceModal && (
        <AddPractiseExperience
          initialData={selectedExperience}
          onSave={handleSaveExperience}
          onClose={() => {
            setShowExperienceModal(false);
            setSelectedExperience(null);
          }}
        />
      )}

      {showAwardModal && (
        <AddYourAwards
          initialData={selectedAward}
          onSave={handleSaveAward}
          onClose={() => {
            setShowAwardModal(false);
            setSelectedAward(null);
            setSelectedAwardIndex(null);
          }}
        />
      )}

      {showMembershipModal && (
        <AddMembershipDetails
          initialData={selectedMembership}
          onSave={handleSaveMembership}
          onClose={() => {
            setShowMembershipModal(false);
            setSelectedMembership(null);
            setSelectedMembershipIndex(null);
          }}
        />
      )}
    </div>
  );
};

export default AdvanceDetailsModal;
