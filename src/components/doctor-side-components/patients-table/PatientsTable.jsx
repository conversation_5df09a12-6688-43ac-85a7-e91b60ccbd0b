import React from "react";
import { useState, useMemo, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationLink,
} from "../../ui/pagination";
import { Avatar, AvatarFallback, AvatarImage } from "../../ui/avatar";
import { useNavigate } from "react-router-dom";
import { ReactComponent as Eye } from "../../../assets/svgs/eye.svg";
import { Input } from "../../ui/input";
import { Button } from "../../ui/button";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import { fetchConcernedPatients_api } from "../../../api/api_calls/patient_apiCalls";
import { toast } from "../../../hooks/use-toast";
import LoadingSpinner from "../../animations/LoadingSpinner";

const ITEMS_PER_PAGE = 20;

const PatientsTable = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [search, setSearch] = useState("");
  const [debouncedSearch, setDebouncedSearch] = useState("");
  const [patients, setPatients] = useState([]);
  const [totalPatients, setTotalPatients] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(search);
    }, 500);

    return () => clearTimeout(timer);
  }, [search]);

  // Fetch concerned patients from API
  const fetchPatients = async () => {
    try {
      setIsLoading(true);
      const offset = (currentPage - 1) * ITEMS_PER_PAGE;
      const res = await fetchConcernedPatients_api({
        searchText: debouncedSearch || null,
        offset,
        limit: ITEMS_PER_PAGE,
      });
      setPatients(res?.data?.patients || []);
      setTotalPatients(res?.data?.total || 0);
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN fetchConcernedPatients => ", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch patients on component mount and when dependencies change
  useEffect(() => {
    fetchPatients();
  }, [currentPage, debouncedSearch]);

  // Reset to first page when search changes
  useEffect(() => {
    if (currentPage !== 1) {
      setCurrentPage(1);
    }
  }, [debouncedSearch]);

  const totalPages = Math.ceil(totalPatients / ITEMS_PER_PAGE);

  const handlePageChange = (page) => {
    // Prevent unnecessary API calls for invalid page numbers
    if (page < 1 || page > totalPages || page === currentPage) {
      return;
    }
    setCurrentPage(page);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center justify-center">
          <h2 className="text-lg font-semibold">List of Patients</h2>
        </div>
        <div className="flex gap-4 items-center">
          <Input
            placeholder="Search patient name, email, or contact"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-64"
          />
        </div>
      </div>

      {isLoading && (
        <div className="flex justify-center items-center py-8">
          <LoadingSpinner />
        </div>
      )}

      {!isLoading && (
        <Table>
          <TableHeader className="bg-[#DFE0E2]">
            <TableRow>
              <TableHead>Patient ID</TableHead>
              <TableHead>Patient Name</TableHead>
              <TableHead>Email Address</TableHead>
              <TableHead>Contact Number</TableHead>
              <TableHead>Medical Concern</TableHead>
              <TableHead>Date Registered</TableHead>
              <TableHead>Patient EMR</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {patients.map((patient, index) => (
              <TableRow key={patient.id || index}>
                <TableCell>{patient.id || "N/A"}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2 flex-row">
                    <div className="h-9	w-9">
                      <Avatar className="w-full h-full object-cover ">
                        <AvatarImage
                          src={patient?.profilePicture?.url || DefaultAvatar}
                        />
                        <AvatarFallback>
                          {`${patient.firstName?.[0] || ""}${patient.lastName?.[0] || ""}`}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                    {`${patient.firstName || "N/A"} ${patient.lastName || ""}`}{" "}
                  </div>
                </TableCell>
                <TableCell>{patient.email || "N/A"}</TableCell>
                <TableCell>{patient.contactNumber || "N/A"}</TableCell>
                <TableCell>{patient.medicalConcerns || "N/A"}</TableCell>
                <TableCell>
                  {patient.createdAt
                    ? new Date(patient.createdAt).toLocaleDateString()
                    : "N/A"}
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() =>
                      navigate("/doctor/patient-profile", {
                        state: { patientId: patient.id },
                      })
                    }
                  >
                    <Eye />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}

      {!isLoading && patients.length === 0 && (
        <div className="py-8 text-center text-gray-500">
          No patients found matching your search.
        </div>
      )}

      {!isLoading && patients.length > 0 && (
        <div className="flex justify-between items-center mt-4 w-full">
          <Pagination className="mt-4">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                />
              </PaginationItem>

              {totalPages <= 5 ? (
                // If 5 or fewer pages, show all page numbers
                Array.from({ length: totalPages }, (_, i) => (
                  <PaginationItem key={i}>
                    <PaginationLink
                      isActive={currentPage === i + 1}
                      onClick={() => handlePageChange(i + 1)}
                    >
                      {i + 1}
                    </PaginationLink>
                  </PaginationItem>
                ))
              ) : (
                // If more than 5 pages, show limited page numbers with ellipsis
                <>
                  {/* First page always shown */}
                  <PaginationItem>
                    <PaginationLink
                      isActive={currentPage === 1}
                      onClick={() => handlePageChange(1)}
                    >
                      1
                    </PaginationLink>
                  </PaginationItem>

                  {/* Show ellipsis if current page is > 3 */}
                  {currentPage > 3 && (
                    <PaginationItem>
                      <span className="flex h-9 w-9 items-center justify-center text-sm">
                        ...
                      </span>
                    </PaginationItem>
                  )}

                  {/* Pages around current page */}
                  {Array.from({ length: totalPages }, (_, i) => i + 1)
                    .filter((page) => {
                      if (page === 1 || page === totalPages) return false; // Skip first and last pages (handled separately)
                      return Math.abs(page - currentPage) < 2; // Show pages within 1 of current page
                    })
                    .map((page) => (
                      <PaginationItem key={page}>
                        <PaginationLink
                          isActive={currentPage === page}
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </PaginationLink>
                      </PaginationItem>
                    ))}

                  {/* Show ellipsis if current page is < totalPages - 2 */}
                  {currentPage < totalPages - 2 && (
                    <PaginationItem>
                      <span className="flex h-9 w-9 items-center justify-center text-sm">
                        ...
                      </span>
                    </PaginationItem>
                  )}

                  {/* Last page always shown */}
                  <PaginationItem>
                    <PaginationLink
                      isActive={currentPage === totalPages}
                      onClick={() => handlePageChange(totalPages)}
                    >
                      {totalPages}
                    </PaginationLink>
                  </PaginationItem>
                </>
              )}

              <PaginationItem>
                <PaginationNext
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
};

export default PatientsTable;
