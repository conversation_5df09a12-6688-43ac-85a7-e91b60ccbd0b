import React from "react";
import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationLink,
} from "../../ui/pagination";
import { Avatar, AvatarFallback, AvatarImage } from "../../ui/avatar";
import { Input } from "../../ui/input";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import payments from "./PatientPaymentData";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import FadeInSection from "../../animations/FadeInSection";
import { Search } from "lucide-react";

const ITEMS_PER_PAGE = 20;

const PaymentsTable = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedWeek, setSelectedWeek] = useState("All");
  const [currentPage, setCurrentPage] = useState(1);

  const isDateInRange = (dateStr, range) => {
    if (range === "All") return true;

    const today = new Date(2025, 4, 21);
    const dateParts = dateStr.split("/");
    const date = new Date(
      Number.parseInt(dateParts[2]),
      Number.parseInt(dateParts[1]) - 1,
      Number.parseInt(dateParts[0]),
    );

    today.setHours(0, 0, 0, 0);

    const startOfThisWeek = new Date(today);
    const day = today.getDay();
    const diff = today.getDate() - day + (day === 0 ? -6 : 1);
    startOfThisWeek.setDate(diff);
    startOfThisWeek.setHours(0, 0, 0, 0);

    const endOfThisWeek = new Date(startOfThisWeek);
    endOfThisWeek.setDate(startOfThisWeek.getDate() + 6);
    endOfThisWeek.setHours(23, 59, 59, 999);

    const startOfLastWeek = new Date(startOfThisWeek);
    startOfLastWeek.setDate(startOfThisWeek.getDate() - 7);

    const endOfLastWeek = new Date(startOfLastWeek);
    endOfLastWeek.setDate(startOfLastWeek.getDate() + 6);
    endOfLastWeek.setHours(23, 59, 59, 999);

    const startOfNextWeek = new Date(endOfThisWeek);
    startOfNextWeek.setDate(endOfThisWeek.getDate() + 1);

    const endOfNextWeek = new Date(startOfNextWeek);
    endOfNextWeek.setDate(startOfNextWeek.getDate() + 6);
    endOfNextWeek.setHours(23, 59, 59, 999);

    switch (range) {
      case "This Week":
        return date >= startOfThisWeek && date <= endOfThisWeek;
      case "Last Week":
        return date >= startOfLastWeek && date <= endOfLastWeek;
      case "Next Week":
        return date >= startOfNextWeek && date <= endOfNextWeek;
      default:
        return true;
    }
  };

  const filteredPayments = payments.filter((payment) => {
    const matchesSearch =
      payment.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.doctorName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      payment.service.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesDateFilter = isDateInRange(payment.date, selectedWeek);
    return matchesSearch && matchesDateFilter;
  });

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "Complete":
        return "bg-blue-100 text-blue-600";
      case "Cancelled":
        return "bg-red-100 text-red-600";
      case "Upcoming":
        return "bg-yellow-100 text-yellow-600";
      case "Accepted":
        return "bg-green-100 text-green-600";
      case "Pending":
        return "bg-orange-100 text-orange-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  const totalPages = Math.ceil(filteredPayments.length / ITEMS_PER_PAGE);

  const currentData = filteredPayments.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE,
  );

  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedWeek]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="w-full flex p-4 md:p-8 lg:p-8 flex-col gap-4">
      <FadeInSection>
        <h1 className="text-2xl font-semibold mb-6">List of Payments</h1>

        <div className="flex justify-between items-center mb-6">
          <div className="relative w-full max-w-md">
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Search by Payment ID, Doctor Name, or Service"
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Select value={selectedWeek} onValueChange={setSelectedWeek}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="This Week">This Week</SelectItem>
              <SelectItem value="Last Week">Last Week</SelectItem>
              <SelectItem value="Next Week">Next Week</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="">
          <Table>
            <TableHeader className="bg-[#DFE0E2]">
              <TableRow>
                <TableHead>Payment ID</TableHead>
                <TableHead>Doctor Name</TableHead>
                {/* <TableHead>Location</TableHead> */}
                <TableHead>Service</TableHead>
                <TableHead>Appointment Status</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Payment Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.map((payment, index) => (
                <TableRow key={index}>
                  <TableCell>{payment.id}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2 flex-row">
                      <div className="h-9	w-9">
                        <Avatar className="w-full h-full object-cover ">
                          <AvatarImage
                            src={payment?.profileImage || DefaultAvatar}
                          />
                          <AvatarFallback>{`${payment.firstName} ${payment.lastName}`}</AvatarFallback>
                        </Avatar>
                      </div>
                      {payment.doctorName}
                    </div>
                  </TableCell>
                  <TableCell>{payment.service}</TableCell>
                  {/* <TableCell>{payment.location}</TableCell> */}
                  <TableCell>
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                        payment.appointmentStatus,
                      )}`}
                    >
                      {payment.appointmentStatus}
                    </span>
                  </TableCell>
                  <TableCell>{payment.amount}</TableCell>
                  <TableCell>{payment.date}</TableCell>
                  <TableCell>{payment.time}</TableCell>
                  <TableCell>
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                        payment.paymentStatus,
                      )}`}
                    >
                      {payment.paymentStatus}
                    </span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredPayments.length === 0 && (
            <div className="py-8 text-center text-gray-500">
              No payments found matching your filters.
            </div>
          )}

          {filteredPayments.length > 0 && (
            <div className="flex justify-between items-center mt-4 w-full">
              <Pagination className="mt-4">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    />
                  </PaginationItem>

                  {totalPages <= 5 ? (
                    Array.from({ length: totalPages }, (_, i) => (
                      <PaginationItem key={i}>
                        <PaginationLink
                          isActive={currentPage === i + 1}
                          onClick={() => handlePageChange(i + 1)}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))
                  ) : (
                    <>
                      <PaginationItem>
                        <PaginationLink
                          isActive={currentPage === 1}
                          onClick={() => handlePageChange(1)}
                        >
                          1
                        </PaginationLink>
                      </PaginationItem>

                      {currentPage > 3 && (
                        <PaginationItem>
                          <span className="flex h-9 w-9 items-center justify-center text-sm">
                            ...
                          </span>
                        </PaginationItem>
                      )}

                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter((page) => {
                          if (page === 1 || page === totalPages) return false;
                          return Math.abs(page - currentPage) < 2;
                        })
                        .map((page) => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              isActive={currentPage === page}
                              onClick={() => handlePageChange(page)}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        ))}

                      {currentPage < totalPages - 2 && (
                        <PaginationItem>
                          <span className="flex h-9 w-9 items-center justify-center text-sm">
                            ...
                          </span>
                        </PaginationItem>
                      )}

                      <PaginationItem>
                        <PaginationLink
                          isActive={currentPage === totalPages}
                          onClick={() => handlePageChange(totalPages)}
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      </FadeInSection>
    </div>
  );
};

export default PaymentsTable;
