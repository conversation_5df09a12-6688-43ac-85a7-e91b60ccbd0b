import React from "react";
import { useState, useRef, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "../../ui/dialog";
import { Loader2, FileText, Hash } from "lucide-react";
import { Label } from "../../ui/label";
import { Input } from "../../ui/input";
import { RadioGroup, RadioGroupItem } from "../../ui/radio-group";
import { Button } from "../../ui/button";
import { Textarea } from "../../ui/textarea";
import { FaAngleLeft } from "react-icons/fa";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../ui/form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "../../../hooks/use-toast";
import { useSelector, useDispatch } from "react-redux";
import { ReactComponent as UserIcon } from "../../../assets/svgs/UserIcon.svg";
import { ReactComponent as LocationIcon } from "../../../assets/svgs/LocationWithCircleBlue.svg";
import { updatePatient_api } from "../../../api/api_calls/patient_apiCalls";
import { me_api } from "../../../api/api_calls/auth_apiCalls";
import { setUser } from "../../../redux/slices/userSlice";

const insurancePlanTypes = [
  "HMO (Health Maintenance Organization)",
  "PPO (Preferred Provider Organization)",
  "EPO (Exclusive Provider Organization)",
  "POS (Point of Service)",
  "HDHP (High Deductible Health Plan)",
  "FSA (Flexible Spending Account)",
  "HSA (Health Savings Account)",
  "Medicare",
  "Medicaid",
  "CHIP (Children's Health Insurance Program)",
];

const insuranceCompanies = [
  "Aetna",
  "Blue Cross Blue Shield",
  "Cigna",
  "Humana",
  "UnitedHealthcare",
  "Kaiser Permanente",
  "Anthem",
  "Centene",
  "Molina Healthcare",
  "CareFirst",
  "Health Net",
  "WellCare",
  "Ambetter",
  "Oscar Health",
  "Bright Health",
];

// 1. Define form schema with Zod
const formSchema = z.object({
  planType: z.string(),
  companyName: z.string(),
  companyLocation: z.string(),
  policyOwner: z.string(),
  policyNumber: z.string(),
  patientRegisteredName: z.string(),
  groupNumber: z.string(),
  planDetails: z.string(),
  consentForMedInfoRelease: z.boolean(),
  consentForFinances: z.boolean(),
  coPay: z.coerce.number().min(0, "Experience cannot be negative"), // coPay could be string if input is type text
});

const PatientInsuranceDialog = ({
  isOpen,
  onClose,
  onDone,
  onBack,
  onNext,
  callingFrom,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [planTypeSuggestions, setPlanTypeSuggestions] = useState([]);
  const [companyNameSuggestions, setCompanyNameSuggestions] = useState([]);
  const [showPlanTypeSuggestions, setShowPlanTypeSuggestions] = useState(false);
  const [showCompanyNameSuggestions, setShowCompanyNameSuggestions] =
    useState(false);
  const planTypeRef = useRef(null);
  const companyNameRef = useRef(null);

  const user = useSelector((state) => state.userReducer.user);
  const dispatch = useDispatch();

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      planType: "",
      companyName: "",
      companyLocation: "",
      policyOwner: "",
      policyNumber: "",
      patientRegisteredName: "",
      groupNumber: "",
      planDetails: "",
      consentForMedInfoRelease: false,
      consentForFinances: false,
      coPay: "",
    },
  });

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (planTypeRef.current && !planTypeRef.current.contains(event.target)) {
        setShowPlanTypeSuggestions(false);
      }
      if (
        companyNameRef.current &&
        !companyNameRef.current.contains(event.target)
      ) {
        setShowCompanyNameSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handlePlanTypeChange = (e, onChange) => {
    const value = e.target.value;
    onChange(value);

    if (value.trim() === "") {
      setPlanTypeSuggestions([]);
      setShowPlanTypeSuggestions(false);
      return;
    }

    const filtered = insurancePlanTypes.filter((type) =>
      type.toLowerCase().includes(value.toLowerCase()),
    );
    setPlanTypeSuggestions(filtered);
    setShowPlanTypeSuggestions(filtered.length > 0);
  };

  const handleCompanyNameChange = (e, onChange) => {
    const value = e.target.value;
    onChange(value);

    if (value.trim() === "") {
      setCompanyNameSuggestions([]);
      setShowCompanyNameSuggestions(false);
      return;
    }

    const filtered = insuranceCompanies.filter((company) =>
      company.toLowerCase().includes(value.toLowerCase()),
    );
    setCompanyNameSuggestions(filtered);
    setShowCompanyNameSuggestions(filtered.length > 0);
  };

  const handleonSkip = async () => {
    const res = await me_api();
    dispatch(setUser(res.data));
  };

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      await updatePatient_api({
        patientData: {
          insuranceDetails: {
            patientRegisteredName: data?.patientRegisteredName,
            companyLocation: data?.companyLocation,
            companyName: data?.companyName,
            planType: data?.planType,
            policyOwner: data?.policyOwner,
            policyNumber: data?.policyNumber,
            groupNumber: data?.groupNumber,
            coPay: data?.coPay,
            planDetails: data?.planDetails,
            consentForFinances: data?.consentForFinances,
            consentForMedInfoRelease: data?.consentForMedInfoRelease,
          },
        },
      });

      const res = await me_api();
      dispatch(setUser(res.data));
      toast({
        description: "Insurance information updated successfully",
      });
      if (onNext) {
        onNext();
      } else if (onDone) {
        onDone();
      }
    } catch (error) {
      toast({
        description: error.message || "Failed to update insurance information",
        variant: "destructive",
      });
      console.error("ERROR IN INSURANCE UPDATE => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    if (user?.insuranceDetails) {
      form.reset({
        planType: user.insuranceDetails.planType || "",
        companyName: user.insuranceDetails.companyName || "",
        companyLocation: user.insuranceDetails.companyLocation || "",
        policyOwner: user.insuranceDetails.policyOwner || "",
        policyNumber: user.insuranceDetails.policyNumber || "",
        patientRegisteredName:
          user.insuranceDetails.patientRegisteredName || "",
        groupNumber: user.insuranceDetails.groupNumber || "",
        planDetails: user.insuranceDetails.planDetails || "",
        consentForMedInfoRelease:
          user.insuranceDetails.consentForMedInfoRelease,
        consentForFinances: user.insuranceDetails.consentForFinances,
        coPay: user.insuranceDetails.coPay || "",
      });
    }
  }, [user]);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={onClose}
      modal={false} // Disables the default modal behavior
      onInteractOutside={(e) => {
        // Prevent closing when clicking outside
        e.preventDefault();
      }}
    >
      <DialogContent
        className="sm:max-w-5xl max-h-[90vh] overflow-auto p-0"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="sr-only">Patient Insurance Dialog</DialogTitle>
        <DialogDescription className="sr-only">
          Patient Insurance Dialog
        </DialogDescription>
        <div className="p-6">
          {user?.isProfileComplete ? null : (
            <div className="w-full items-center justify-between flex-row flex py-4">
              <Button variant={"outline"} size={"icon"} onClick={onBack}>
                <FaAngleLeft />
              </Button>
              <div onClick={handleonSkip} className="p-2">
                <h3 className="text-[#1E1E1EB2] underline cursor-pointer text-[20px] font-[600]">
                  Skip
                </h3>
              </div>
            </div>
          )}
          <h2 className="text-2xl font-bold text-gray-800 mb-8">
            Please Provide Your Insurance Details
          </h2>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                {/* Patient Name */}
                <FormField
                  control={form.control}
                  name="patientRegisteredName"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Patient Name
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                          <Input
                            {...field}
                            placeholder="Enter patient name"
                            className={`pl-10 transition-all ${
                              fieldState.error
                                ? "border-destructive focus:ring-destructive/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Location */}
                <FormField
                  control={form.control}
                  name="companyLocation"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Location
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <LocationIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                          <Input
                            {...field}
                            placeholder="Enter your location"
                            className={`pl-10 transition-all ${
                              fieldState.error
                                ? "border-destructive focus:ring-destructive/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Insurance Company Name */}
                <FormField
                  control={form.control}
                  name="companyName"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Insurance Company Name
                      </FormLabel>
                      <FormControl>
                        <div className="relative" ref={companyNameRef}>
                          {/* <FaBuilding className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" /> */}
                          <Input
                            {...field}
                            placeholder="Enter insurance company name"
                            className={` transition-all ${
                              fieldState.error
                                ? "border-destructive focus:ring-destructive/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                            onChange={(e) =>
                              handleCompanyNameChange(e, field.onChange)
                            }
                            onFocus={() => {
                              if (
                                field.value &&
                                insuranceCompanies.some((company) =>
                                  company
                                    .toLowerCase()
                                    .includes(field.value.toLowerCase()),
                                )
                              ) {
                                const filtered = insuranceCompanies.filter(
                                  (company) =>
                                    company
                                      .toLowerCase()
                                      .includes(field.value.toLowerCase()),
                                );
                                setCompanyNameSuggestions(filtered);
                                setShowCompanyNameSuggestions(true);
                              }
                            }}
                          />
                          {showCompanyNameSuggestions &&
                            companyNameSuggestions.length > 0 && (
                              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                                {companyNameSuggestions.map(
                                  (suggestion, index) => (
                                    <div
                                      key={index}
                                      className="px-4 py-2 cursor-pointer hover:bg-gray-100"
                                      onClick={() => {
                                        field.onChange(suggestion);
                                        setShowCompanyNameSuggestions(false);
                                      }}
                                    >
                                      {suggestion}
                                    </div>
                                  ),
                                )}
                              </div>
                            )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Insurance Plan Type */}
                <FormField
                  control={form.control}
                  name="planType"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Insurance Plan Type
                      </FormLabel>
                      <FormControl>
                        <div className="relative" ref={planTypeRef}>
                          {/* <FaUserCircle className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" /> */}
                          <Input
                            {...field}
                            placeholder="Enter insurance plan type"
                            className={`transition-all ${
                              fieldState.error
                                ? "border-destructive focus:ring-destructive/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                            onChange={(e) =>
                              handlePlanTypeChange(e, field.onChange)
                            }
                            onFocus={() => {
                              if (
                                field.value &&
                                insurancePlanTypes.some((type) =>
                                  type
                                    .toLowerCase()
                                    .includes(field.value.toLowerCase()),
                                )
                              ) {
                                const filtered = insurancePlanTypes.filter(
                                  (type) =>
                                    type
                                      .toLowerCase()
                                      .includes(field.value.toLowerCase()),
                                );
                                setPlanTypeSuggestions(filtered);
                                setShowPlanTypeSuggestions(true);
                              }
                            }}
                          />
                          {showPlanTypeSuggestions &&
                            planTypeSuggestions.length > 0 && (
                              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
                                {planTypeSuggestions.map(
                                  (suggestion, index) => (
                                    <div
                                      key={index}
                                      className="px-4 py-2 cursor-pointer hover:bg-gray-100"
                                      onClick={() => {
                                        field.onChange(suggestion);
                                        setShowPlanTypeSuggestions(false);
                                      }}
                                    >
                                      {suggestion}
                                    </div>
                                  ),
                                )}
                              </div>
                            )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Policy Owner */}
                <FormField
                  control={form.control}
                  name="policyOwner"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Policy Owner
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                          <Input
                            {...field}
                            placeholder="Enter policy owner name"
                            className={`pl-10 transition-all ${
                              fieldState.error
                                ? "border-destructive focus:ring-destructive/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Policy Number */}
                <FormField
                  control={form.control}
                  name="policyNumber"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Policy Number
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                          <Input
                            {...field}
                            placeholder="Enter policy number"
                            className={`pl-10 transition-all ${
                              fieldState.error
                                ? "border-destructive focus:ring-destructive/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Group Number */}
                <FormField
                  control={form.control}
                  name="groupNumber"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Group Number
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                          <Input
                            {...field}
                            placeholder="Enter group number"
                            className={`pl-10 transition-all ${
                              fieldState.error
                                ? "border-destructive focus:ring-destructive/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Co-pay */}
                <FormField
                  control={form.control}
                  name="coPay"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Co-pay
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          {/* <FaMoneyBillWave className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" /> */}
                          <Input
                            type="number"
                            {...field}
                            placeholder="Co-pay"
                            onChange={(e) => {
                              const value = Number.parseInt(e.target.value, 10);
                              if (!isNaN(value) && value >= 0 && value <= 100) {
                                field.onChange(value);
                              }
                            }}
                            min="0"
                            max="100"
                            className={`transition-all ${
                              fieldState.error
                                ? "border-destructive focus:ring-destructive/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-6 mb-6">
                {/* Plan */}
                <FormField
                  control={form.control}
                  name="planDetails"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="text-gray-700 font-medium">
                        Plan
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <FileText className="absolute left-3 top-3 text-primary" />
                          <Textarea
                            {...field}
                            placeholder="Enter plan details"
                            className={`pl-10 transition-all ${
                              fieldState.error
                                ? "border-destructive focus:ring-destructive/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-6 mb-8">
                <div>
                  <h3 className="text-lg font-medium text-gray-800 mb-1">
                    Authorization and Consent:
                  </h3>
                </div>

                {/* authorize the release of any medical information */}
                <FormField
                  control={form.control}
                  name="consentForMedInfoRelease"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="text-gray-700">
                        Do you authorize the release of any medical information
                        necessary to process insurance claims?
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="grid grid-cols-1 md:grid-cols-2 gap-4"
                        >
                          <div className="flex items-center space-x-2 border border-gray-300 rounded-lg p-4">
                            <RadioGroupItem value={true} id="consent-yes-1" />
                            <Label
                              htmlFor="consent-yes-1"
                              className="font-medium"
                            >
                              Yes
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2 border border-gray-300 rounded-lg p-4">
                            <RadioGroupItem value={false} id="consent-no-1" />
                            <Label
                              htmlFor="consent-no-1"
                              className="font-medium"
                            >
                              No
                            </Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* authorize payment of insurance benefits directly */}
                <FormField
                  control={form.control}
                  name="consentForFinances"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel className="text-gray-700">
                        Do you authorize payment of insurance benefits directly
                        to the healthcare provider?
                      </FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="grid grid-cols-1 md:grid-cols-2 gap-4"
                        >
                          <div className="flex items-center space-x-2 border border-gray-300 rounded-lg p-4">
                            <RadioGroupItem value={true} id="consent-yes-2" />
                            <Label
                              htmlFor="consent-yes-2"
                              className="font-medium"
                            >
                              Yes
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2 border border-gray-300 rounded-lg p-4">
                            <RadioGroupItem value={false} id="consent-no-2" />
                            <Label
                              htmlFor="consent-no-2"
                              className="font-medium"
                            >
                              No
                            </Label>
                          </div>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end gap-3">
                <Button
                  variant="outline"
                  onClick={onClose}
                  className="rounded-lg px-6"
                >
                  Cancel
                </Button>

                {callingFrom === "patientInformation" ? (
                  <Button
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-6"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processing...
                      </>
                    ) : onNext ? (
                      "Done"
                    ) : (
                      "Done"
                    )}
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700 text-white rounded-lg px-6"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Processing...
                      </>
                    ) : onNext ? (
                      "Update"
                    ) : (
                      "Update"
                    )}
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PatientInsuranceDialog;
