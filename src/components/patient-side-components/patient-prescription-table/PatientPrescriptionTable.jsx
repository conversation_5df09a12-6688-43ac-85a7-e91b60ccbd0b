import { useState, useEffect } from "react";
import { Eye, Download, Search, Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../ui/table";
import { Input } from "../../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationLink,
} from "../../ui/pagination";
import { Avatar, AvatarFallback, AvatarImage } from "../../ui/avatar";
import { getPrescriptionFormsByPatientId_apiCalls } from "../../../api/api_calls/appointmentforms/prescriptionForm_apiCalls";
import FadeInSection from "../../animations/FadeInSection";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";

const PatientPrescriptionTable = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTimeframe, setSelectedTimeframe] = useState("All");
  const [currentPage, setCurrentPage] = useState(1);
  const [prescriptions, setPrescriptions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  // Debounce search query to avoid too many API calls
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(searchQuery);

  const navigate = useNavigate();
  const user = useSelector((state) => state.userReducer.user);

  const ITEMS_PER_PAGE = 20;

  // Since filtering is now handled by the API, we use the prescriptions directly
  const filteredPrescriptions = prescriptions;
  const totalPages = Math.ceil(totalCount / ITEMS_PER_PAGE);
  const currentData = prescriptions; // API already returns paginated data

  // Handle page changes
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle prescription view navigation
  const handleViewPrescription = (prescription) => {
    navigate(`/patient/prescription-details`, {
      state: {
        appointmentId: prescription.appointmentId,
        prescription: prescription,
      },
    });
  };

  // Fetch prescriptions from API
  const fetchPrescriptions = async () => {
    if (!user?.id) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      console.log("Fetching prescriptions for patient:", user.id);

      // Calculate date range for API call based on selected timeframe
      let startDate = null;
      let endDate = null;

      if (selectedTimeframe !== "All") {
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        switch (selectedTimeframe) {
          case "This Week": {
            const startOfWeek = new Date(today);
            const day = today.getDay();
            const diff = today.getDate() - day + (day === 0 ? -6 : 1);
            startOfWeek.setDate(diff);
            startDate = startOfWeek.toISOString().split("T")[0];

            const endOfWeek = new Date(startOfWeek);
            endOfWeek.setDate(startOfWeek.getDate() + 6);
            endDate = endOfWeek.toISOString().split("T")[0];
            break;
          }
          case "Last Week": {
            const startOfThisWeek = new Date(today);
            const day = today.getDay();
            const diff = today.getDate() - day + (day === 0 ? -6 : 1);
            startOfThisWeek.setDate(diff);

            const startOfLastWeek = new Date(startOfThisWeek);
            startOfLastWeek.setDate(startOfThisWeek.getDate() - 7);
            startDate = startOfLastWeek.toISOString().split("T")[0];

            const endOfLastWeek = new Date(startOfLastWeek);
            endOfLastWeek.setDate(startOfLastWeek.getDate() + 6);
            endDate = endOfLastWeek.toISOString().split("T")[0];
            break;
          }
          case "This Month": {
            const startOfMonth = new Date(
              today.getFullYear(),
              today.getMonth(),
              1,
            );
            startDate = startOfMonth.toISOString().split("T")[0];

            const endOfMonth = new Date(
              today.getFullYear(),
              today.getMonth() + 1,
              0,
            );
            endDate = endOfMonth.toISOString().split("T")[0];
            break;
          }
          case "Last Month": {
            const startOfLastMonth = new Date(
              today.getFullYear(),
              today.getMonth() - 1,
              1,
            );
            startDate = startOfLastMonth.toISOString().split("T")[0];

            const endOfLastMonth = new Date(
              today.getFullYear(),
              today.getMonth(),
              0,
            );
            endDate = endOfLastMonth.toISOString().split("T")[0];
            break;
          }
        }
      }

      const response = await getPrescriptionFormsByPatientId_apiCalls({
        patientId: user.id,
        offset: (currentPage - 1) * ITEMS_PER_PAGE,
        limit: ITEMS_PER_PAGE,
        searchText: debouncedSearchQuery || null,
        startDate,
        endDate,
      });

      console.log("Prescriptions API response:", response);

      if (response?.data) {
        // Transform API response to match table structure
        const transformedPrescriptions =
          response.data.appointments?.map((appointment) => ({
            id: appointment.id,
            appointmentId: appointment.id,
            doctorName:
              `${appointment.doctor?.firstName || ""} ${appointment.doctor?.lastName || ""}`.trim(),
            email: appointment.doctor?.email || "N/A",
            contactNumber: appointment.doctor?.contactNumber || "N/A",
            service: appointment.consultation?.serviceName || "N/A",
            date: appointment.startDateTime
              ? new Date(appointment.startDateTime).toLocaleDateString("en-GB")
              : "N/A",
            time: appointment.startDateTime
              ? new Date(appointment.startDateTime).toLocaleTimeString(
                  "en-US",
                  {
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: true,
                    timeZone: "UTC",
                  },
                )
              : "N/A",
            location: "N/A", // This might need to be added to the API response
            profileImage: appointment.doctor?.profilePicture?.url || null,
            firstName: appointment.doctor?.firstName || "",
            lastName: appointment.doctor?.lastName || "",
          })) || [];

        setPrescriptions(transformedPrescriptions);
        setTotalCount(response.data.total || 0);
      } else {
        setPrescriptions([]);
        setTotalCount(0);
      }
    } catch (error) {
      console.error("Failed to fetch prescriptions:", error);
      setPrescriptions([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Fetch prescriptions when component mounts or dependencies change
  useEffect(() => {
    fetchPrescriptions();
  }, [user?.id, currentPage, selectedTimeframe, debouncedSearchQuery]);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedTimeframe]);

  // Loading state
  if (loading) {
    return (
      <div className="w-full flex p-4 md:p-8 lg:p-8 flex-col gap-4">
        <FadeInSection>
          <h1 className="text-2xl font-semibold mb-6">Prescriptions</h1>
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading prescriptions...</span>
          </div>
        </FadeInSection>
      </div>
    );
  }

  return (
    <div className="w-full flex p-4 md:p-8 lg:p-8 flex-col gap-4">
      <FadeInSection>
        <h1 className="text-2xl font-semibold mb-6">Prescriptions</h1>

        <div className="flex justify-between items-center mb-6">
          <div className="relative w-full max-w-md">
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Search by ID, Doctor, Email, Contact, Service, or Location"
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Select
            value={selectedTimeframe}
            onValueChange={setSelectedTimeframe}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="This Week">This Week</SelectItem>
              <SelectItem value="Last Week">Last Week</SelectItem>
              <SelectItem value="This Month">This Month</SelectItem>
              <SelectItem value="Last Month">Last Month</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="">
          <Table>
            <TableHeader className="bg-[#DFE0E2]">
              <TableRow>
                <TableHead>Prescription ID</TableHead>
                <TableHead>Doctor Name</TableHead>
                <TableHead>Email Address</TableHead>
                <TableHead>Contact Number</TableHead>
                <TableHead>Service</TableHead>
                {/* <TableHead>Location</TableHead> */}
                <TableHead>Date</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.map((prescription, index) => (
                <TableRow key={index}>
                  <TableCell>{prescription.id}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2 flex-row">
                      <div className="h-9	w-9">
                        <Avatar className="w-full h-full object-cover ">
                          <AvatarImage
                            src={prescription?.profileImage || DefaultAvatar}
                          />
                          <AvatarFallback>{`${prescription.firstName} ${prescription.lastName}`}</AvatarFallback>
                        </Avatar>
                      </div>
                      {prescription.doctorName}
                    </div>
                  </TableCell>
                  <TableCell>{prescription.email}</TableCell>
                  <TableCell>{prescription.contactNumber}</TableCell>
                  <TableCell>{prescription.service}</TableCell>
                  {/* <TableCell>{prescription.location}</TableCell> */}
                  <TableCell>{prescription.date}</TableCell>
                  <TableCell>{prescription.time}</TableCell>
                  <TableCell>
                    <div className="flex space-x-3">
                      <button className="text-gray-500 hover:text-blue-600">
                        <Download size={20} />
                      </button>
                      <button
                        className="text-gray-500 hover:text-blue-600"
                        onClick={() => handleViewPrescription(prescription)}
                      >
                        <Eye size={20} />
                      </button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredPrescriptions.length === 0 && !loading && (
            <div className="py-8 text-center text-gray-500">
              {searchQuery || selectedTimeframe !== "All"
                ? "No prescriptions found matching your filters."
                : "No prescriptions found. Prescriptions will appear here after your appointments with doctors."}
            </div>
          )}

          {filteredPrescriptions.length > 0 && (
            <div className="flex justify-between items-center mt-4 w-full">
              <Pagination className="mt-4">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    />
                  </PaginationItem>

                  {totalPages <= 5 ? (
                    // If 5 or fewer pages, show all page numbers
                    Array.from({ length: totalPages }, (_, i) => (
                      <PaginationItem key={i}>
                        <PaginationLink
                          isActive={currentPage === i + 1}
                          onClick={() => handlePageChange(i + 1)}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))
                  ) : (
                    // If more than 5 pages, show limited page numbers with ellipsis
                    <>
                      {/* First page always shown */}
                      <PaginationItem>
                        <PaginationLink
                          isActive={currentPage === 1}
                          onClick={() => handlePageChange(1)}
                        >
                          1
                        </PaginationLink>
                      </PaginationItem>

                      {/* Show ellipsis if current page is > 3 */}
                      {currentPage > 3 && (
                        <PaginationItem>
                          <span className="flex h-9 w-9 items-center justify-center text-sm">
                            ...
                          </span>
                        </PaginationItem>
                      )}

                      {/* Pages around current page */}
                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter((page) => {
                          if (page === 1 || page === totalPages) return false; // Skip first and last pages (handled separately)
                          return Math.abs(page - currentPage) < 2; // Show pages within 1 of current page
                        })
                        .map((page) => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              isActive={currentPage === page}
                              onClick={() => handlePageChange(page)}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        ))}

                      {/* Show ellipsis if current page is < totalPages - 2 */}
                      {currentPage < totalPages - 2 && (
                        <PaginationItem>
                          <span className="flex h-9 w-9 items-center justify-center text-sm">
                            ...
                          </span>
                        </PaginationItem>
                      )}

                      {/* Last page always shown */}
                      <PaginationItem>
                        <PaginationLink
                          isActive={currentPage === totalPages}
                          onClick={() => handlePageChange(totalPages)}
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      </FadeInSection>
    </div>
  );
};

export default PatientPrescriptionTable;
