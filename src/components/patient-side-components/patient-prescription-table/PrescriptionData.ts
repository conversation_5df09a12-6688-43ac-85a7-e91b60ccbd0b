/* eslint-disable */
// Prescription data generator with realistic information

// Doctor names
const doctorFirstNames: string[] = [
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];

const doctorLastNames: string[] = [
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "Miller",
  "Davis",
  "Rodriguez",
  "Martinez",
  "Hernandez",
  "Lopez",
  "Gonzalez",
  "Wilson",
  "Anderson",
];

// Email domains
const emailDomains: string[] = [
  "gmail.com",
  "yahoo.com",
  "outlook.com",
  "hotmail.com",
  "icloud.com",
  "hospital.org",
  "medcenter.com",
  "healthcare.net",
  "doctors.med",
  "clinic.org",
];

// Medical services
const medicalServices: string[] = [
  "Gastrointestinal Disorder",
  "Hypertension Treatment",
  "Diabetes Management",
  "Migraine Therapy",
  "Back Pain Treatment",
  "Anxiety Counseling",
  "Asthma Management",
  "Allergy Testing",
  "Arthritis Treatment",
  "Dermatology Consultation",
  "Sleep Disorder Therapy",
  "Depression Counseling",
  "Thyroid Examination",
  "Urinary Tract Treatment",
  "General Checkup",
];

// Times
const appointmentTimes: string[] = [
  "09:00 AM",
  "10:30 AM",
  "11:30 AM",
  "01:00 PM",
  "02:30 PM",
  "03:45 PM",
  "05:00 PM",
];

// Locations
const locations: string[] = [
  "Main Hospital - North Wing",
  "Downtown Medical Center",
  "Westside Clinic",
  "Eastside Health Center",
  "Central Medical Plaza",
  "Riverside Hospital",
  "Parkview Medical Center",
  "Hillside Clinic",
  "Metropolitan Hospital",
  "Community Health Center",
  "University Medical Center",
  "Lakeside Hospital",
  "Valley Medical Group",
  "Sunset Medical Plaza",
  "Harbor View Hospital",
];

// Helper function to get random item from array
const getRandomItem = <T>(arr: T[]): T => {
  return arr[Math.floor(Math.random() * arr.length)];
};

// Function to generate random date within a range
const getRandomDate = (start: Date, end: Date): Date => {
  return new Date(
    start.getTime() + Math.random() * (end.getTime() - start.getTime()),
  );
};

// Format date as DD/MM/YYYY
const formatDate = (date: Date): string => {
  return `${date.getDate().toString().padStart(2, "0")}/${(date.getMonth() + 1).toString().padStart(2, "0")}/${date.getFullYear()}`;
};

// Generate random prescription ID
const generatePrescriptionId = (index: number): string => {
  return `PRE${100000 + index}`;
};

// Generate random email based on name
const generateEmail = (firstName: string, lastName: string): string => {
  const randomNum = Math.floor(Math.random() * 100);
  return `${firstName.toLowerCase()}.${lastName.toLowerCase()}${randomNum}@${getRandomItem(emailDomains)}`;
};

// Generate random phone number
const generatePhoneNumber = (): string => {
  const countryCode = "+92";
  const areaCode = Math.floor(Math.random() * 900) + 100;
  const firstPart = Math.floor(Math.random() * 9000) + 1000;
  const secondPart = Math.floor(Math.random() * 9000) + 1000;
  return `${countryCode} ${areaCode} ${firstPart}${secondPart}`;
};

// Set the current date to 21/5/2025
const today = new Date(2025, 4, 21); // Month is 0-indexed, so 4 = May

// Get the current week's Monday and Sunday
const getWeekRange = (date: Date) => {
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
  const monday = new Date(date);
  monday.setDate(diff);
  monday.setHours(0, 0, 0, 0);

  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);
  sunday.setHours(23, 59, 59, 999);

  return { start: monday, end: sunday };
};

const thisWeekRange = getWeekRange(today);

// Get last week's range
const lastWeekStart = new Date(thisWeekRange.start);
lastWeekStart.setDate(lastWeekStart.getDate() - 7);
const lastWeekEnd = new Date(thisWeekRange.start);
lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
lastWeekEnd.setHours(23, 59, 59, 999);

// Get this month's range
const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1);
const thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
thisMonthEnd.setHours(23, 59, 59, 999);

// Get last month's range
const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1);
const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0);
lastMonthEnd.setHours(23, 59, 59, 999);

// Get date ranges for random date generation
const threeMonthsAgo = new Date(today);
threeMonthsAgo.setMonth(today.getMonth() - 3);
const oneMonthFromNow = new Date(today);
oneMonthFromNow.setMonth(today.getMonth() + 1);

// Prescription interface
interface Prescription {
  id: string;
  doctorName: string;
  email: string;
  contactNumber: string;
  service: string;
  date: string;
  time: string;
  location: string;
  rawDate?: Date;
}

// Create a single prescription record
const createPrescription = (
  index: number,
  prescriptionDate: Date,
): Prescription => {
  const firstName = getRandomItem(doctorFirstNames);
  const lastName = getRandomItem(doctorLastNames);
  const doctorName = `${firstName} ${lastName}`;

  return {
    id: generatePrescriptionId(index),
    doctorName: doctorName,
    email: generateEmail(firstName, lastName),
    contactNumber: generatePhoneNumber(),
    service: getRandomItem(medicalServices),
    date: formatDate(prescriptionDate),
    time: getRandomItem(appointmentTimes),
    location: getRandomItem(locations),
    rawDate: prescriptionDate,
  };
};

// Create prescriptions with a realistic distribution
const createPrescriptions = (): Prescription[] => {
  const prescriptions: Prescription[] = [];

  // Create prescriptions for last week
  for (let index = 0; index < 50; index++) {
    const prescriptionDate = getRandomDate(lastWeekStart, lastWeekEnd);
    prescriptions.push(createPrescription(index, prescriptionDate));
  }

  // Create prescriptions for this week
  for (let index = 50; index < 150; index++) {
    const prescriptionDate = getRandomDate(
      thisWeekRange.start,
      thisWeekRange.end,
    );
    prescriptions.push(createPrescription(index, prescriptionDate));
  }

  // Create prescriptions for this month (excluding this week and last week)
  for (let index = 150; index < 200; index++) {
    let prescriptionDate;
    do {
      prescriptionDate = getRandomDate(thisMonthStart, thisMonthEnd);
    } while (
      prescriptionDate >= lastWeekStart &&
      prescriptionDate <= thisWeekRange.end
    );
    prescriptions.push(createPrescription(index, prescriptionDate));
  }

  // Create prescriptions for last month
  for (let index = 200; index < 250; index++) {
    const prescriptionDate = getRandomDate(lastMonthStart, lastMonthEnd);
    prescriptions.push(createPrescription(index, prescriptionDate));
  }

  // Create some additional prescriptions in the broader date range
  for (let index = 250; index < 300; index++) {
    const prescriptionDate = getRandomDate(threeMonthsAgo, oneMonthFromNow);
    prescriptions.push(createPrescription(index, prescriptionDate));
  }

  // Remove the rawDate property before returning (not needed for display)
  return prescriptions.map(({ rawDate, ...rest }) => rest);
};

const PrescriptionData = createPrescriptions();

export default PrescriptionData;
