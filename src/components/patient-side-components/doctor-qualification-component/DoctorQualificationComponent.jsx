import React from "react";
import { parseISO, format } from "date-fns";

const DoctorQualificationComponent = ({ qualification }) => {
  return (
    <div className="flex border border-solid border-[#E7E8E9] rounded-xl w-full max-w-6xl hover:bg-[#0052FD08] flex-col p-2 sm:p-2">
      <div className="flex flex-row">
        {/* <div className="w-full sm:w-2/12 flex items-start justify-start sm:justify-center">
          <div className="flex items-center justify-center w-full h-full rounded-2xl overflow-hidden border border-solid border-[#E7E8E9]">
            <img
              alt="Doctor"
              src={qualification?.degreeFile?.url}
              className=" w-[122px] h-[118px] object-contain"
            />
          </div>
        </div> */}

        <div className="w-full sm:w-10/12 p-2 sm:p-3 md:p-4">
          <h1 className="font-bold text-sm sm:text-base md:text-lg lg:text-[20px] text-center sm:text-left">
            {qualification?.institutionName}
          </h1>

          <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1EB2] mt-1 text-center sm:text-left">
            {qualification?.degreeName}
          </h4>
          <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1EB2] mt-1 text-center sm:text-left">
            {`${format(parseISO(qualification?.startDate), "MMM yyyy")}-${format(parseISO(qualification?.endDate), "MMM yyyy")}`}
          </h4>
        </div>
      </div>
    </div>
  );
};

export default DoctorQualificationComponent;
