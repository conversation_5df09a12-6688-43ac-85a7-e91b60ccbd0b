import React from "react";
import { cn } from "../../../lib/utils";
import { Card, CardContent } from "../../ui/card";
import { ReactComponent as LocationGreyIcon } from "../../../assets/svgs/LocationWithCircleGrey.svg";
import { ReactComponent as <PERSON><PERSON><PERSON><PERSON> } from "../../../assets/svgs/CalenderGrey.svg";
import { ReactComponent as <PERSON><PERSON><PERSON> } from "../../../assets/svgs/ClockGrey.svg";
import { Badge } from "../../ui/badge";

const DoctorProfileCard = ({ doctor }) => {
  return (
    <div
      className={cn(
        "flex items-center justify-center w-full flex-col gap-4 sm:gap-6 py-6",
      )}
    >
      <Card className="w-full ">
        <CardContent className="p-3 sm:p-4 md:p-6">
          <div className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row items-start gap-4">
              {/* Doctor Image - Responsive sizing */}
              <div className="w-full sm:w-auto flex items-center justify-center sm:justify-start mb-2 sm:mb-0">
                <div className="w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden border-2 border-primary">
                  <img
                    alt={`Dr. ${doctor.name}`}
                    src={doctor.imageUrl || "/placeholder.svg"}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>

              {/* Doctor Information - Responsive layout */}
              <div className="w-full flex flex-col md:flex-row items-start justify-between gap-4">
                {/* Basic Info */}
                <div className="flex flex-col gap-2">
                  <h1 className="font-bold text-xl text-foreground">
                    {doctor.name}
                  </h1>
                  <h2 className="font-semibold text-muted-foreground text-base">
                    {doctor.qualification}
                  </h2>
                  <div className="flex flex-row items-center gap-2">
                    <LocationGreyIcon className="w-4 h-4 text-muted-foreground" />
                    <span className="font-semibold text-foreground text-base">
                      {doctor.location}
                    </span>
                  </div>
                  <h3 className="font-semibold text-foreground text-base mt-1">
                    Service
                  </h3>
                  <span className="font-semibold text-muted-foreground text-base">
                    {doctor.specialty}
                  </span>
                </div>

                {/* Date & Time */}
                <div className="flex flex-col gap-2">
                  <h3 className="font-bold text-foreground text-xl">
                    Date & Time
                  </h3>
                  <div className="flex flex-row items-center gap-2">
                    <CalenderGrey className="w-4 h-4 text-muted-foreground" />
                    <span className="font-semibold text-foreground text-base">
                      {doctor.appointmentDate}
                    </span>
                  </div>
                  <div className="flex flex-row items-center gap-2">
                    <ClockGrey className="w-4 h-4 text-muted-foreground" />
                    <span className="font-semibold text-foreground text-base">
                      {doctor.appointmentTime}
                    </span>
                  </div>
                </div>

                {/* Fee Information */}
                <div className="flex flex-col gap-2">
                  <h3 className="font-bold text-foreground text-xl">Fee</h3>
                  <div className="flex flex-row items-center gap-2">
                    <span className="font-semibold text-foreground text-base">
                      $ {doctor.fee}
                    </span>
                    {doctor.isPaid && (
                      <Badge
                        variant="outline"
                        className="bg-primary/10 text-primary border-none"
                      >
                        Paid
                      </Badge>
                    )}
                  </div>
                  {doctor.hasInsurance && (
                    <span className="font-semibold text-foreground text-base">
                      Insurance coverage applied
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DoctorProfileCard;
