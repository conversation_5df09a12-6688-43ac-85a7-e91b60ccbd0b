/* eslint-disable */

// Appointment data generator with realistic information

// Doctor names
const doctorFirstNames: string[] = [
  "<PERSON>",
  "<PERSON><PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
];

const doctorLastNames: string[] = [
  "<PERSON><PERSON>",
  "<PERSON><PERSON><PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "<PERSON>",
  "Miller",
  "Davis",
  "Rodriguez",
  "Martinez",
  "Hernandez",
  "Lopez",
  "Gonzalez",
  "Wilson",
  "Anderson",
];

// Medical services
const medicalServices: string[] = [
  "Gastrointestinal Disorder",
  "Hypertension Treatment",
  "Diabetes Management",
  "Migraine Therapy",
  "Back Pain Treatment",
  "Anxiety Counseling",
  "Asthma Management",
  "Allergy Testing",
  "Arthritis Treatment",
  "Dermatology Consultation",
  "Sleep Disorder Therapy",
  "Depression Counseling",
  "Thyroid Examination",
  "Urinary Tract Treatment",
  "General Checkup",
];

// Appointment statuses
const appointmentStatuses: string[] = [
  "Completed",
  "Canceled",
  "Upcoming",
  "Accepted",
];

// Payment statuses
const paymentStatuses: string[] = [
  "Completed",
  "Canceled",
  "Pending",
  "Accepted",
];

// Times
const appointmentTimes: string[] = [
  "09:00 AM",
  "10:30 AM",
  "11:30 AM",
  "01:00 PM",
  "02:30 PM",
  "03:45 PM",
  "05:00 PM",
];

// Locations
const locations: string[] = [
  "Main Hospital - North Wing",
  "Downtown Medical Center",
  "Westside Clinic",
  "Eastside Health Center",
  "Central Medical Plaza",
  "Riverside Hospital",
  "Parkview Medical Center",
  "Hillside Clinic",
  "Metropolitan Hospital",
  "Community Health Center",
  "University Medical Center",
  "Lakeside Hospital",
  "Valley Medical Group",
  "Sunset Medical Plaza",
  "Harbor View Hospital",
];

// Helper function to get random item from array
const getRandomItem = <T>(arr: T[]): T => {
  return arr[Math.floor(Math.random() * arr.length)];
};

// Function to generate random date within a range
const getRandomDate = (start: Date, end: Date): Date => {
  return new Date(
    start.getTime() + Math.random() * (end.getTime() - start.getTime()),
  );
};

// Format date as DD/MM/YYYY
const formatDate = (date: Date): string => {
  return `${date.getDate().toString().padStart(2, "0")}/${(date.getMonth() + 1).toString().padStart(2, "0")}/${date.getFullYear()}`;
};

// Generate random appointment ID
const generateAppointmentId = (index: number): string => {
  return `APT${100000 + index}`;
};

// Set the current date to 21/5/2025
const today = new Date(2025, 4, 21); // Month is 0-indexed, so 4 = May

// Function to determine appointment status based on date
const getStatusBasedOnDate = (appointmentDate: Date, today: Date): string => {
  // Calculate the difference in days
  const diffTime = appointmentDate.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays < 0) {
    // Past appointments are mostly Completed
    return Math.random() > 0.1 ? "Completed" : "Canceled";
  } else if (diffDays === 0) {
    // Today's appointments are a mix of Completed and Accepted
    const rand = Math.random();
    if (rand < 0.4) return "Completed";
    if (rand < 0.9) return "Accepted";
    return "Canceled";
  } else if (diffDays <= 2) {
    // Near future appointments (1-2 days) are mostly Accepted
    return Math.random() > 0.2 ? "Accepted" : "Upcoming";
  } else {
    // Future appointments are mostly Upcoming
    const rand = Math.random();
    if (rand < 0.7) return "Upcoming";
    if (rand < 0.9) return "Accepted";
    return "Canceled";
  }
};

// Get the current week's Monday and Sunday
const getWeekRange = (date: Date) => {
  const day = date.getDay();
  const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
  const monday = new Date(date);
  monday.setDate(diff);
  monday.setHours(0, 0, 0, 0);

  const sunday = new Date(monday);
  sunday.setDate(monday.getDate() + 6);
  sunday.setHours(23, 59, 59, 999);

  return { start: monday, end: sunday };
};

const thisWeekRange = getWeekRange(today);

// Get last week's range
const lastWeekStart = new Date(thisWeekRange.start);
lastWeekStart.setDate(lastWeekStart.getDate() - 7);
const lastWeekEnd = new Date(thisWeekRange.start);
lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
lastWeekEnd.setHours(23, 59, 59, 999);

// Get next week's range
const nextWeekStart = new Date(thisWeekRange.end);
nextWeekStart.setDate(nextWeekStart.getDate() + 1);
const nextWeekEnd = new Date(nextWeekStart);
nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
nextWeekEnd.setHours(23, 59, 59, 999);

// Get date ranges for random date generation
const oneMonthAgo = new Date(today);
oneMonthAgo.setMonth(today.getMonth() - 1);
const oneMonthFromNow = new Date(today);
oneMonthFromNow.setMonth(today.getMonth() + 1);

// Appointment interface
interface Appointment {
  id: string;
  doctorName: string;
  service: string;
  status: string;
  date: string;
  time: string;
  paymentStatus: string;
  location: string;
  rawDate?: Date;
}

// Create a single appointment
const createAppointment = (
  index: number,
  appointmentDate: Date,
): Appointment => {
  const doctorName = `${getRandomItem(doctorFirstNames)} ${getRandomItem(doctorLastNames)}`;
  let status = getStatusBasedOnDate(appointmentDate, today);

  // Payment status is usually related to appointment status
  let paymentStatus: string;

  switch (status) {
    case "Completed":
      // Completed appointments usually have completed payments, but some might be pending
      paymentStatus = Math.random() > 0.1 ? "Completed" : "Pending";
      break;
    case "Accepted":
      // Accepted appointments usually have accepted payments, but some might be pending
      paymentStatus = Math.random() > 0.3 ? "Accepted" : "Pending";
      break;
    case "Upcoming":
      // Upcoming appointments usually have pending payments, but some might be accepted (pre-paid)
      paymentStatus = Math.random() > 0.7 ? "Accepted" : "Pending";
      break;
    case "Canceled":
      // Canceled appointments usually have canceled payments
      paymentStatus = "Canceled";
      break;
    default:
      paymentStatus = "Pending";
  }

  return {
    id: generateAppointmentId(index),
    doctorName: doctorName,
    service: getRandomItem(medicalServices),
    status: status,
    date: formatDate(appointmentDate),
    time: getRandomItem(appointmentTimes),
    paymentStatus: paymentStatus,
    location: getRandomItem(locations),
    rawDate: appointmentDate,
  };
};

// Create appointments with a realistic distribution
const createAppointments = (): Appointment[] => {
  const appointments: Appointment[] = [];

  // Create appointments for last week (mostly completed)
  for (let index = 0; index < 50; index++) {
    const appointmentDate = getRandomDate(lastWeekStart, lastWeekEnd);
    appointments.push(createAppointment(index, appointmentDate));
  }

  // Create appointments for this week (mix of completed and upcoming)
  for (let index = 50; index < 150; index++) {
    const appointmentDate = getRandomDate(
      thisWeekRange.start,
      thisWeekRange.end,
    );
    appointments.push(createAppointment(index, appointmentDate));
  }

  // Create appointments for next week (all upcoming)
  for (let index = 150; index < 200; index++) {
    const appointmentDate = getRandomDate(nextWeekStart, nextWeekEnd);
    appointments.push(createAppointment(index, appointmentDate));
  }

  // Create some additional appointments in the broader date range
  for (let index = 200; index < 300; index++) {
    const appointmentDate = getRandomDate(oneMonthAgo, oneMonthFromNow);
    appointments.push(createAppointment(index, appointmentDate));
  }

  // Remove the rawDate property before returning (not needed for display)
  return appointments.map(({ rawDate, ...rest }) => rest);
};

const AppointmentData = createAppointments();

export default AppointmentData;
