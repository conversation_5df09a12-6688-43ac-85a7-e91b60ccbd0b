import { useEffect, useMemo, useState } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "../../ui/dialog";
import { Button } from "../../ui/button";
import { Input } from "../../ui/input";
import { Textarea } from "../../ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import { Calendar } from "../../ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "../../ui/popover";
import { format, parseISO } from "date-fns";
import { Globe, Loader2 } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../ui/form";
import { useForm } from "react-hook-form";
import { cn } from "../../../lib/utils";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSelector, useDispatch } from "react-redux";
import { toast } from "../../../hooks/use-toast";
import Countries from "../../../constants/countries";
import ActivityLoaderComponent from "../../animations/LoadingSpinner";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import { ReactComponent as CalenderIcon } from "../../../assets/svgs/CalendarIcon.svg";
import { ReactComponent as GenderIcon } from "../../../assets/svgs/GenderIcon.svg";
import { ReactComponent as AddressIcon } from "../../../assets/svgs/AddressIcon.svg";
import { ReactComponent as FileIcon } from "../../../assets/svgs/FileIcon.svg";
import Genders from "../../../constants/genders";
import { updatePatient_api } from "../../../api/api_calls/patient_apiCalls";
import { me_api } from "../../../api/api_calls/auth_apiCalls";
import { setUser } from "../../../redux/slices/userSlice";
import PhoneInputWithCountryCode from "../../phone-input-with-country-code/PhoneInputWithCountryCode";
import { FaUserCircle } from "react-icons/fa";
import { HiMail } from "react-icons/hi";

const formSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address").min(1, "Email is required"),
  contactNumber: z.string().min(1, "Phone is required"),
  dob: z.string().min(1, "Date of birth is required"),
  gender: z
    .string()
    .min(1, "Gender is required")
    .refine((val) => val !== "", {
      message: "Gender is required",
    }),
  address: z.string().min(10, "Address must be at least 10 characters"),
  country: z.string().min(1, "Country is required"),
  medicalConcerns: z.string().min(1, "Medical Concern is required"),
});

const PatientPersonalInformationDialog = ({
  isOpen,
  onClose,
  onNext,
  callingFrom,
}) => {
  const user = useSelector((state) => state.userReducer.user);
  const dispatch = useDispatch();
  const [image, setImage] = useState(null);
  const [fieldsNotEmpty, setFieldsNotEmpty] = useState(false);
  const [imageError, setImageError] = useState("");
  const [phoneValid, setPhoneValid] = useState(true);
  const [phoneErrorMessage, setPhoneErrorMessage] = useState("");
  const [yearDropdownOpen, setYearDropdownOpen] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [phoneValue, setPhoneValue] = useState(
    user.contactNumber?.toString() || "",
  );

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: user.firstName ?? "",
      lastName: user.lastName ?? "",
      email: user.email ?? "",
      contactNumber: user.contactNumber?.toString() ?? "",
      dob: user.dob ?? "",
      gender: user.gender ?? "",
      address: user.address ?? "",
      country: user.country ?? "",
      medicalConcerns: user.medicalConcerns ?? "",
    },
    mode: "onSubmit",
  });

  useEffect(() => {
    const subscription = form.watch((value) => {
      const requiredFieldsNotEmpty =
        !!value.firstName &&
        !!value.lastName &&
        !!value.email &&
        !!value.contactNumber &&
        !!value.dob &&
        !!value.gender &&
        !!value.address &&
        !!value.country &&
        !!value.medicalConcerns;
      setFieldsNotEmpty(requiredFieldsNotEmpty);
    });
    return () => subscription.unsubscribe();
  }, [form, form.watch]);

  const initialDate = useMemo(() => {
    const dateValue = form.getValues("dob");
    return dateValue ? parseISO(dateValue) : null;
  }, [form]);

  const [selectedYear, setSelectedYear] = useState(
    initialDate ? initialDate.getFullYear() : new Date().getFullYear(),
  );

  const [month, setMonth] = useState(
    initialDate ? initialDate.getMonth() : new Date().getMonth(),
  );

  const years = useMemo(
    () => Array.from({ length: 124 }, (_, i) => 1900 + i),
    [],
  );

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      const validTypes = ["image/jpeg", "image/jpg", "image/png"];
      if (!validTypes.includes(file.type)) {
        setImageError("Please upload only JPEG or PNG images");
        return;
      }

      setImage(file);
      setImageError("");
      console.log("Image selected:", file.name);
    }
  };

  const getCountryLabel = (value) => {
    const country = Countries.find((c) => c.value === value);
    return country ? country.label : value;
  };

  const getUserCountry = () => {
    const phoneNumber = user.contactNumber?.toString() || "";
    if (phoneNumber.startsWith("+")) {
      const countryCodes = {
        1: "us",
        44: "gb",
        91: "in",
        61: "au",
        49: "de",
        33: "fr",
        86: "cn",
        81: "jp",
      };
      for (const [code, iso] of Object.entries(countryCodes)) {
        if (phoneNumber.startsWith(`+${code}`)) {
          return iso;
        }
      }
    }
    return user.country?.toLowerCase() || "us";
  };

  useEffect(() => {
    const handlePopoverOpen = () => {
      setYearDropdownOpen(true);
    };
    document.addEventListener("popover-open", handlePopoverOpen);
    return () => {
      document.removeEventListener("popover-open", handlePopoverOpen);
    };
  }, []);

  // Add dispatch for Redux

  const onSubmit = async (data) => {
    // Check if image is required and not provided FIRST
    if (!image && !user?.profilePicture?.url) {
      setImageError("Profile image is required");
      console.log("Image error set:", "Profile image is required"); // Debug log
      // Scroll to the top to show the error message
      setTimeout(() => {
        const dialogContent = document.querySelector('[role="dialog"]');
        if (dialogContent) {
          dialogContent.scrollTop = 0;
        }
      }, 100);
      return;
    }

    // Clear any existing image errors
    setImageError("");

    setIsSubmitting(true);
    try {
      // Make sure phone number is properly formatted
      const formattedPhoneNumber = data.contactNumber.startsWith("+")
        ? data.contactNumber
        : `+${data.contactNumber}`;

      await updatePatient_api({
        profilePicture: image,
        patientData: {
          firstName: data?.firstName,
          lastName: data?.lastName,
          email: data?.email,
          contactNumber: formattedPhoneNumber,
          gender: data?.gender,
          country: data?.country,
          dob: data?.dob,
          address: data?.address,
          medicalConcerns: data?.medicalConcerns,
        },
      });

      if (user?.isProfileComplete) {
        const res = await me_api();
        dispatch(setUser(res.data));
      }

      toast({
        description: "Profile information updated successfully",
      });
      onNext();
    } catch (error) {
      toast({
        description: error.message || "Failed to update profile information",
        variant: "destructive",
      });
      console.error("ERROR IN PROFILE UPDATE => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onOpenChange={onClose}
      modal={false} // Disables the default modal behavior
      onInteractOutside={(e) => {
        // Prevent closing when clicking outside
        e.preventDefault();
      }}
    >
      {isSubmitting && (
        <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-50 rounded-lg">
          <ActivityLoaderComponent />
        </div>
      )}
      <DialogContent
        className="sm:max-w-5xl max-h-[90vh] overflow-auto p-0"
        onInteractOutside={(e) => {
          e.preventDefault();
        }}
      >
        <DialogTitle className="sr-only">
          Patient Information Dialog
        </DialogTitle>
        <DialogDescription className="sr-only">
          Patient Information Dialog
        </DialogDescription>
        <div className="p-6">
          <div className="flex flex-col items-center justify-center mb-8">
            <div className="w-full items-center justify-center flex flex-col gap-3 mb-8">
              <label
                htmlFor="dropzone-file"
                className="relative flex flex-col items-center justify-center w-[100px] h-[100px] border-2 border-primary border-solid rounded-full cursor-pointer bg-white hover:bg-gray-100 overflow-hidden transition-all duration-200 hover:shadow-md group"
                onDragOver={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onDragEnter={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onDragLeave={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onDrop={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  const file = e.dataTransfer.files[0];
                  if (file) {
                    handleImageChange({ target: { files: [file] } });
                  }
                }}
              >
                {image ? (
                  <img
                    src={URL.createObjectURL(image) || "/placeholder.svg"}
                    alt="Uploaded"
                    className="absolute top-0 left-0 w-full h-full object-cover"
                  />
                ) : (
                  <img
                    src={user?.profilePicture?.url || DefaultAvatar}
                    alt="Profile"
                    className="absolute top-0 left-0 w-full h-full object-cover"
                  />
                )}
                {/* Camera SVG shown only on hover */}
                <div className="absolute inset-0 flex flex-col items-center justify-center z-10 bg-white bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-200">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="40"
                    height="40"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="#0052FD"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                  >
                    <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z" />
                    <circle cx="12" cy="13" r="3" />
                  </svg>
                </div>
                <input
                  type="file"
                  id="dropzone-file"
                  className="hidden"
                  onChange={handleImageChange}
                  accept=".jpeg,.jpg,.png"
                />
              </label>
            </div>

            <h2 className="text-2xl font-bold text-gray dipole-800">
              Upload Your Profile Picture
            </h2>

            {imageError && (
              <h2 className="text-red-500 font-normal"> {imageError}</h2>
            )}
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                {callingFrom === "patientInformation" ? null : (
                  <>
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel className="font-semibold text-gray-800">
                            First Name
                          </FormLabel>
                          <FormControl>
                            <div className="relative w-full">
                              <FaUserCircle
                                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                                size={18}
                              />
                              <Input
                                {...field}
                                placeholder="Enter your first name"
                                className={`pl-10 transition-all ${
                                  fieldState.error
                                    ? "border-destructive focus:ring-destructive/50"
                                    : "focus:ring-primary/50 focus:border-primary"
                                } focus:ring-2`}
                              />
                            </div>
                          </FormControl>
                          {fieldState.error && (
                            <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                              {fieldState.error.message}
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />

                    {/* Last Name */}
                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel className="font-semibold text-gray-800">
                            Last Name
                          </FormLabel>
                          <FormControl>
                            <div className="relative w-full">
                              <FaUserCircle
                                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                                size={18}
                              />
                              <Input
                                {...field}
                                placeholder="Enter your last name"
                                className={`pl-10 transition-all ${
                                  fieldState.error
                                    ? "border-destructive focus:ring-destructive/50"
                                    : "focus:ring-primary/50 focus:border-primary"
                                } focus:ring-2`}
                              />
                            </div>
                          </FormControl>
                          {fieldState.error && (
                            <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                              {fieldState.error.message}
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />

                    {/* Email */}
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel className="font-semibold text-gray-800">
                            Email
                          </FormLabel>
                          <FormControl>
                            <div className="relative w-full">
                              <HiMail
                                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                                size={18}
                              />
                              <Input
                                {...field}
                                placeholder="Enter your email"
                                className={`pl-10 transition-all ${
                                  fieldState.error
                                    ? "border-destructive focus:ring-destructive/50"
                                    : "focus:ring-primary/50 focus:border-primary"
                                } focus:ring-2`}
                              />
                            </div>
                          </FormControl>
                          {fieldState.error && (
                            <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                              {fieldState.error.message}
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />

                    {/* Contact Number */}
                    <FormField
                      control={form.control}
                      name="contactNumber"
                      render={({ field, fieldState }) => (
                        <FormItem>
                          <FormLabel className="font-semibold text-gray-800">
                            Contact Number
                          </FormLabel>
                          <FormControl>
                            <PhoneInputWithCountryCode
                              field={field}
                              fieldState={fieldState}
                              getUserCountry={getUserCountry}
                              setPhoneValid={setPhoneValid}
                              setPhoneErrorMessage={setPhoneErrorMessage}
                              phoneValid={phoneValid}
                              value={phoneValue}
                              onChange={(value) => {
                                setPhoneValue(value);
                                field.onChange(value);
                              }}
                            />
                          </FormControl>
                          {!phoneValid && (
                            <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                              {phoneErrorMessage ||
                                "Please enter a valid phone number"}
                            </FormMessage>
                          )}
                        </FormItem>
                      )}
                    />
                  </>
                )}
                {/* First Name */}

                {/* Gender */}
                <FormField
                  control={form.control}
                  name="gender"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="font-semibold text-gray-800">
                        Gender
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <GenderIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                          >
                            <SelectTrigger
                              className={cn(
                                "pl-10 h-10",
                                !field.value
                                  ? "text-[#1E1E1E80]"
                                  : "text-[#1E1E1E]",
                                fieldState.error && "border-red-500",
                              )}
                            >
                              <SelectValue placeholder="Select Gender" />
                            </SelectTrigger>
                            <SelectContent>
                              {Genders.map((option) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Country */}
                <FormField
                  control={form.control}
                  name="country"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="font-semibold text-gray-800">
                        Country
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary z-10" />
                          <Select
                            onValueChange={field.onChange}
                            value={field.value}
                            hasError={!!fieldState.error}
                          >
                            <SelectTrigger
                              className={cn(
                                "pl-10 h-10 text-[14px] font-bold",
                                !field.value
                                  ? "text-[#1E1E1E80]"
                                  : "text-[#1E1E1E]",
                                fieldState.error && "border-red-500",
                              )}
                              hasError={!!fieldState.error}
                            >
                              <SelectValue placeholder="Select Country">
                                {field.value
                                  ? getCountryLabel(field.value)
                                  : "Select Country"}
                              </SelectValue>
                            </SelectTrigger>
                            <SelectContent>
                              {Countries.map((country) => (
                                <SelectItem
                                  key={country.value}
                                  value={country.value}
                                >
                                  {country.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Date of Birth */}
                <FormField
                  control={form.control}
                  name="dob"
                  render={({ field, fieldState }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className="font-semibold text-gray-800">
                        Date of Birth
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <CalenderIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant={"outline"}
                                  className={cn(
                                    "w-full text-left font-normal h-10 pl-10",
                                    !field.value &&
                                      "text-[#1E1E1E80] text-[14px] font-thin",
                                    fieldState.error && "border-red-500",
                                  )}
                                >
                                  <span className="w-full font-bold">
                                    {field.value
                                      ? format(parseISO(field.value), "PPP")
                                      : "Pick a date"}
                                  </span>
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <div className="flex items-center justify-between p-2 border-b">
                                <Select
                                  value={selectedYear.toString()}
                                  onValueChange={(value) => {
                                    const year = Number.parseInt(value, 10);
                                    setSelectedYear(year);
                                    setMonth(0);
                                  }}
                                  open={yearDropdownOpen}
                                  onOpenChange={setYearDropdownOpen}
                                >
                                  <SelectTrigger className="w-[120px] h-8">
                                    <SelectValue>
                                      {selectedYear.toString()}
                                    </SelectValue>
                                  </SelectTrigger>
                                  <SelectContent>
                                    {years.map((year) => (
                                      <SelectItem
                                        key={year}
                                        value={year.toString()}
                                      >
                                        {year}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                              <Calendar
                                mode="single"
                                selected={
                                  field.value ? parseISO(field.value) : null
                                }
                                onSelect={(date) => {
                                  field.onChange(
                                    date ? format(date, "yyyy-MM-dd") : "",
                                  );
                                  if (date) {
                                    setSelectedYear(date.getFullYear());
                                    setMonth(date.getMonth());
                                  }
                                }}
                                month={new Date(selectedYear, month)}
                                onMonthChange={(date) => {
                                  setSelectedYear(date.getFullYear());
                                  setMonth(date.getMonth());
                                }}
                                disabled={(date) =>
                                  date > new Date() ||
                                  date < new Date("1900-01-01")
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Address */}
                <FormField
                  control={form.control}
                  name="address"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="font-semibold text-gray-800">
                        Address
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <AddressIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                          <Input
                            {...field}
                            type="text"
                            placeholder="Address"
                            className="pl-10 h-10"
                            hasError={!!fieldState.error}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-1 gap-6">
                {/* Medical Concerns */}
                <FormField
                  control={form.control}
                  name="medicalConcerns"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="font-semibold text-gray-800">
                        Medical Concerns
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <FileIcon className="absolute left-3 top-3 text-primary" />
                          <Textarea
                            {...field}
                            placeholder="Enter any medical concerns"
                            className="pl-10 min-h-[100px]"
                            hasError={!!fieldState.error}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex justify-end gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  className="rounded-lg px-6"
                >
                  Cancel
                </Button>

                {callingFrom === "patientInformation" ? (
                  <Button
                    variant="primary"
                    className=""
                    type="submit"
                    disabled={
                      isSubmitting || !fieldsNotEmpty || !phoneValid || !image
                    }
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Updating...
                      </>
                    ) : (
                      "Next"
                    )}
                  </Button>
                ) : (
                  <Button
                    variant="primary"
                    className=""
                    type="submit"
                    disabled={isSubmitting || !fieldsNotEmpty || !phoneValid}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Updating...
                      </>
                    ) : (
                      "Update"
                    )}
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default PatientPersonalInformationDialog;
