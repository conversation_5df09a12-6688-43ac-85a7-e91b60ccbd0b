import React from "react";
import {
  parseISO,
  format,
  differenceInMonths,
  differenceInDays,
} from "date-fns";

const PracticeExperienceComponent = ({ practiceExperience }) => {
  const calculateDuration = (startDate, endDate) => {
    if (!startDate || !endDate) return null;
    const start = parseISO(startDate);
    const end = parseISO(endDate);
    const totalMonths = differenceInMonths(end, start);
    if (totalMonths === 0) {
      const days = differenceInDays(end, start);
      return `${days} ${days === 1 ? "day" : "days"}`;
    }
    const years = Math.floor(totalMonths / 12);
    const months = totalMonths % 12;
    const yearStr =
      years > 0 ? `${years} ${years === 1 ? "year" : "years"}` : "";
    const monthStr =
      months > 0 ? `${months} ${months === 1 ? "month" : "months"}` : "";
    return [yearStr, monthStr].filter(Boolean).join(" ");
  };

  return (
    <div className="flex border border-solid border-[#E7E8E9] rounded-xl w-full max-w-6xl hover:bg-[#0052FD08] flex-col p-2 sm:p-2">
      <div className="flex flex-row">
        {/* <div className="w-full sm:w-2/12 flex items-start justify-start sm:justify-center">
          <div className="flex items-center justify-center w-full h-full rounded-2xl overflow-hidden border border-solid border-[#E7E8E9]">
            <img
              alt="Doctor"
              src={practiceExperience?.experienceFile.url}
              className=" w-[122px] h-[118px] object-contain"
            />
          </div>
        </div> */}

        <div className="w-full sm:w-10/12 p-2 sm:p-3 md:p-4">
          <h1 className="font-bold text-sm sm:text-base md:text-lg lg:text-[20px] text-center sm:text-left">
            {practiceExperience?.hospitalName}
          </h1>

          <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1EB2] mt-1 text-center sm:text-left">
            {practiceExperience?.designation}
          </h4>

          <div className="flex items-center flex-row justify-start gap-8">
            <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1EB2] mt-1 text-center sm:text-left">
              {`${format(parseISO(practiceExperience?.startDate), "MMM yyyy")}-${format(parseISO(practiceExperience?.endDate), "MMM yyyy")}`}
            </h4>

            <div className="p-2 bg-[#0052FD14] px-4 rounded-full">
              <h4 className="text-[#0052FD] text-[14px] font-bold">
                {calculateDuration(
                  practiceExperience?.startDate,
                  practiceExperience?.endDate,
                )}
              </h4>
            </div>
          </div>

          <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1E] text-center sm:text-left mt-4">
            {practiceExperience?.description}
          </h4>
        </div>
      </div>
    </div>
  );
};

export default PracticeExperienceComponent;
