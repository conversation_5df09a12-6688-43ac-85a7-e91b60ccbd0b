import { useState, useEffect } from "react";
import { Download, Loader2 } from "lucide-react";
import { useLocation } from "react-router-dom";
import FadeInSection from "../../animations/FadeInSection";
import { Button } from "../../ui/button";
import { ReactComponent as SignaturePlaceholder } from "../../../assets/svgs/SignPlaceholder.svg";
import { getPrescriptionFormByAppointmentId_apiCalls } from "../../../api/api_calls/appointmentforms/prescriptionForm_apiCalls";

const PrescriptionDetails = () => {
  const location = useLocation();
  const [prescriptionData, setPrescriptionData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Get appointmentId from navigation state
  const appointmentId = location.state?.appointmentId;
  const prescriptionInfo = location.state?.prescription;

  // Fetch prescription data from API
  useEffect(() => {
    const fetchPrescriptionData = async () => {
      if (!appointmentId) {
        setError("No appointment ID provided");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log(
          "Fetching prescription data for appointmentId:",
          appointmentId,
        );

        const response = await getPrescriptionFormByAppointmentId_apiCalls({
          appointmentId,
        });

        console.log("Prescription API response:", response);

        if (
          response?.data &&
          Array.isArray(response.data) &&
          response.data.length > 0
        ) {
          // response.data is directly an array of prescriptions
          const prescriptions = response.data;

          // Get doctor info from the first prescription's appointment
          const firstPrescription = prescriptions[0];
          const doctor = firstPrescription.appointment?.doctor;
          const appointment = firstPrescription.appointment;

          // Transform the data to match component structure
          const transformedData = {
            id: appointmentId,
            doctorName: doctor
              ? `${doctor.firstName || ""} ${doctor.lastName || ""}`.trim()
              : prescriptionInfo?.doctorName || "N/A",
            medications: prescriptions.map((prescription) => ({
              drugName: prescription.drugName || "N/A",
              dosage: prescription.dosage || "N/A",
              instruction: prescription.instructions || "N/A",
              duration: prescription.duration || "N/A",
              frequency: prescription.frequency || "N/A",
            })),
            signature: doctor?.signature?.url || null,
            doctorSignatureName: doctor
              ? `${doctor.firstName || ""} ${doctor.lastName || ""}`.trim()
              : prescriptionInfo?.doctorName || "N/A",
            date: appointment?.startDateTime
              ? new Date(appointment.startDateTime).toLocaleDateString("en-GB")
              : prescriptionInfo?.date ||
                new Date().toLocaleDateString("en-GB"),
          };

          setPrescriptionData(transformedData);
        } else {
          setError("No prescription data found for this appointment");
        }
      } catch (error) {
        console.error("Failed to fetch prescription data:", error);
        setError("Failed to load prescription data");
      } finally {
        setLoading(false);
      }
    };

    fetchPrescriptionData();
  }, [appointmentId, prescriptionInfo]);

  // Loading state
  if (loading) {
    return (
      <div className="px-6 py-4 md:px-20">
        <FadeInSection>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold">Prescription Detail</h1>
            <Button variant="primary" disabled>
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
          </div>
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading prescription details...</span>
          </div>
        </FadeInSection>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="px-6 py-4 md:px-20">
        <FadeInSection>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold">Prescription Detail</h1>
            <Button variant="primary" disabled>
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
          </div>
          <div className="py-8 text-center text-red-500">
            <p>{error}</p>
          </div>
        </FadeInSection>
      </div>
    );
  }

  // No prescription data
  if (
    !prescriptionData ||
    !prescriptionData.medications ||
    prescriptionData.medications.length === 0
  ) {
    return (
      <div className="px-6 py-4 md:px-20">
        <FadeInSection>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold">Prescription Detail</h1>
            <Button variant="primary" disabled>
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
          </div>
          <div className="py-8 text-center text-gray-500">
            <p>No prescription data found for this appointment.</p>
          </div>
        </FadeInSection>
      </div>
    );
  }

  return (
    <div className="px-6 py-4 md:px-20">
      <FadeInSection>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold">Prescription Detail</h1>
          <Button variant="primary">
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
        </div>

        <div className="w-full rounded-lg border border-gray-200 overflow-hidden">
          {prescriptionData?.medications.map((medication, index) => (
            <div
              key={index}
              className={`grid md:grid-cols-2 gap-4 p-4 ${
                index !== prescriptionData?.medications.length - 1
                  ? "border-b border-gray-200"
                  : ""
              }`}
            >
              <div>
                <p className="text-gray-700 text-base font-medium">
                  Drug Name:{" "}
                  <span className="text-gray-900 text-base font-semibold">
                    {medication.drugName}
                  </span>
                </p>

                <p className="text-gray-700 text-base font-medium">
                  Dosage:{" "}
                  <span className="text-gray-900 text-base font-semibold">
                    {medication.dosage}
                  </span>
                </p>

                <p className="text-gray-700 text-base font-medium">
                  Instruction:{" "}
                  <span className="text-gray-900 text-base font-semibold">
                    {medication.instruction}
                  </span>
                </p>
              </div>

              <div>
                <p className="text-gray-700 text-base font-medium">
                  Duration:{" "}
                  <span className="text-gray-900 text-base font-semibold">
                    {medication.duration}
                  </span>
                </p>

                <p className="text-gray-700 text-base font-medium">
                  Frequency:{" "}
                  <span className="text-gray-900 text-base font-semibold">
                    {medication.frequency}
                  </span>
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <p className="text-gray-900 text-base font-semibold">Signature</p>
            <div className="h-16">
              {prescriptionData.signature ? (
                <img
                  src={prescriptionData.signature}
                  alt="Doctor Signature"
                  className="h-full object-contain"
                />
              ) : (
                <div className="flex flex-col items-start">
                  <SignaturePlaceholder />
                  <p className="text-sm text-gray-600 mt-1">
                    {prescriptionData.doctorSignatureName}
                  </p>
                </div>
              )}
            </div>
          </div>
          <div>
            <p className="text-gray-900 text-base font-semibold">Date</p>
            <p className="text-gray-900 text-base font-semibold">
              {prescriptionData.date}
            </p>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default PrescriptionDetails;
