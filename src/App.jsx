import "./App.css";
import { useEffect, useState } from "react";
import Navigation from "./navigation/navigation";
import { Toaster } from "./components/ui/toaster";
import { me_api } from "./api/api_calls/auth_apiCalls";
import { useDispatch, useSelector } from "react-redux";
import { setUser } from "./redux/slices/userSlice";
import { logOutUser } from "./common/logoutUser";
import { AnimatePresence } from "framer-motion";
import "./i18n";
import { Helmet } from "react-helmet";
import { useLocation } from "react-router-dom";

function App() {
  const dispatch = useDispatch();
  const user = useSelector((state) => state.userReducer.user);
  const token = localStorage.getItem("accessToken");
  const [checkingAuth, setCheckingAuth] = useState(!!token);
  const location = useLocation();

  const authCheck = async () => {
    if (token && !user) {
      try {
        const res = await me_api();
        dispatch(setUser(res.data));
      } catch (error) {
        console.error("Error during authCheck:", error);
        logOutUser();

        // If user is on a protected route, redirect them to login
        const currentPath = location.pathname;
        if (
          currentPath.startsWith("/doctor/") &&
          currentPath !== "/doctor/login" &&
          currentPath !== "/doctor/signup" &&
          !currentPath.includes("/forgot-password")
        ) {
          window.location.href = "/doctor/login";
        } else if (
          currentPath.startsWith("/patient/") &&
          currentPath !== "/patient/login" &&
          currentPath !== "/patient/signup" &&
          !currentPath.includes("/forgot-password")
        ) {
          window.location.href = "/patient/login";
        }
      } finally {
        setCheckingAuth(false);
      }
    } else {
      setCheckingAuth(false);

      // If no token but user is on a protected route, redirect them to login
      if (!token) {
        const currentPath = location.pathname;
        if (
          currentPath.startsWith("/doctor/") &&
          currentPath !== "/doctor/login" &&
          currentPath !== "/doctor/signup" &&
          !currentPath.includes("/forgot-password")
        ) {
          window.location.href = "/doctor/login";
        } else if (
          currentPath.startsWith("/patient/") &&
          currentPath !== "/patient/login" &&
          currentPath !== "/patient/signup" &&
          !currentPath.includes("/forgot-password")
        ) {
          window.location.href = "/patient/login";
        }
      }
    }
  };

  useEffect(() => {
    authCheck();
  }, [location.pathname]);

  return (
    <div>
      <Helmet>
        <title>DocMobil | AI-Powered Hybrid Healthcare Platform</title>
        <meta
          name="description"
          content="Experience seamless healthcare combining the best of in-person and virtual care with DocMobil's AI-powered platform."
        />
      </Helmet>

      <AnimatePresence mode="wait">
        {!checkingAuth && (
          <>
            <Navigation />
            <Toaster />
          </>
        )}
      </AnimatePresence>
    </div>
  );
}

export default App;
