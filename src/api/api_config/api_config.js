import axios from "axios";
import { logOutUser } from "../../common/logoutUser";
import { BASE_URL_STAG, BASE_URL_DEV, BASE_URL_PROD } from "./api_routes";

const env = (process.env.REACT_APP_ENV || "stag").toLowerCase();
const BASE_URL =
  env === "dev" ? BASE_URL_DEV : env === "prod" ? BASE_URL_PROD : BASE_URL_STAG;

const createJsonRequestInstance = () => {
  let instance = axios.create({});
  instance.interceptors.request.use(async (_config) => {
    var jwtToken = await localStorage.getItem("accessToken");
    _config.headers.Authorization = jwtToken ? `Bearer ${jwtToken}` : "";
    // _config.headers.Origin = window.location.origin;
    _config.headers["Content-Type"] = "application/json";
    _config.baseURL = BASE_URL;
    return _config;
  });
  instance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      if (error?.response?.data?.message === "Invalid token.") {
        logOutUser();
      }

      //   console.log("ERROR IN JSON REQUEST => ", error);
      throw new Error(
        error?.response?.data?.message || "An error occurred in json request",
      );
    },
  );
  return instance;
};

const createFormDataRequestInstance = () => {
  console.log('BASE_URL :', BASE_URL)
  let instance = axios.create({});
  instance.interceptors.request.use(async (_config) => {
    var jwtToken = await localStorage.getItem("accessToken");
    _config.headers.Authorization = jwtToken ? `Bearer ${jwtToken}` : "";
    // _config.headers.Origin = window.location.origin;
    _config.baseURL = BASE_URL;
    return _config;
  });
  instance.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      if (error?.response?.data?.message === "Invalid token.") {
        logOutUser();
      }
      throw new Error(
        error?.response?.data?.message || "An error occurred in json request",
      );
    },
  );
  return instance;
};

export const request = createJsonRequestInstance();
export const formData_Request = createFormDataRequestInstance();
