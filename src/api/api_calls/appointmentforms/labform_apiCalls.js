import { API_URLS } from "../../api_config/api_routes";
import { request } from "../../api_config/api_config";

export const createLabForm_api = async (labFormData) => {
  try {
    const res = await request.post(
      API_URLS.create_labForm_api_url,
      labFormData,
    );
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const updateLabForm_api = async (labFormData) => {
  try {
    const res = await request.put(
      API_URLS.update_labForm_api_url,
      labFormData,
    );
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getLabFormById_api = async (id) => {
  try {
    const res = await request.get(
      `${API_URLS.get_labForm_by_id_api_url}/${id}`,
    );
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getLabFormByAppointmentId_api = async (appointmentId) => {
  try {
    const res = await request.get(
      `${API_URLS.get_labForm_by_appointmentId_api_url}/${appointmentId}`,
    );
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getLabFormsByPatientId_api = async (patientId) => {
  try {
    const res = await request.get(
      `${API_URLS.get_labForm_by_patientId_api_url}/${patientId}`,
    );
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};
