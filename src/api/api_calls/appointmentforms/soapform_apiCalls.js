import { API_URLS } from "../../api_config/api_routes";
import { request } from "../../api_config/api_config";

export const createSoapForm_api = async (soapFormData) => {
  try {
    const res = await request.post(
      API_URLS.create_soapForm_api_url,
      soapFormData,
    );
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const updateSoapForm_api = async (soapFormData) => {
  try {
    const res = await request.put(
      API_URLS.update_soapForm_api_url,
      soapFormData,
    );
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getSoapFormById_api = async (id) => {
  try {
    const res = await request.get(
      `${API_URLS.get_soapForm_by_id_api_url}/${id}`,
    );
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getSoapFormByAppointmentId_api = async (appointmentId) => {
  try {
    const res = await request.get(
      `${API_URLS.get_soapForm_by_appointmentId_api_url}/${appointmentId}`,
    );
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};
