import { API_URLS } from "../../api_config/api_routes";
import { request } from "../../api_config/api_config";

// ================================
// PRE-APPOINTMENT FORM APIs
// ================================

export const createPreAppointmentForm_apiCalls = async (formData) => {
  try {
    const res = await request.post(API_URLS.create_preAppointmentForm_api_url, formData);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const updatePreAppointmentForm_apiCalls = async (formData) => {
  try {
    const res = await request.put(API_URLS.update_preAppointmentForm_api_url, formData);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getPreAppointmentFormById_apiCalls = async ({ id }) => {
  try {
    const res = await request.get(`${API_URLS.get_preAppointmentForm_by_id_api_url}/${id}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getPreAppointmentFormByAppointmentId_apiCalls = async ({ appointmentId }) => {
  try {
    const res = await request.get(`${API_URLS.get_preAppointmentForm_by_appointmentId_api_url}/${appointmentId}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};
