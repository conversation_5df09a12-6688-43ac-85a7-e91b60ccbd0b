import { API_URLS } from "../../api_config/api_routes";
import { request } from "../../api_config/api_config";

export const createCompliance_apiCalls = async (complianceData) => {
  try {
    const res = await request.post(API_URLS.create_compliance_api_url, complianceData);

    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const updateCompliance_apiCalls = async (complianceData) => {
  try {
    const res = await request.put(API_URLS.update_compliance_api_url, complianceData);

    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const deleteCompliance_apiCalls = async (complianceId) => {
  try {
    const url = `${API_URLS.delete_compliance_api_url}/${complianceId}`;
    const res = await request.delete(url);

    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getComplianceById_apiCalls = async (complianceId) => {
  try {
    const url = `${API_URLS.get_compliance_by_id_api_url}/${complianceId}`;
    const res = await request.get(url);

    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getCompliancesByAppointmentId_apiCalls = async (appointmentId) => {
  try {
    const url = `${API_URLS.get_compliances_by_appointmentId_api_url}/${appointmentId}`;
    const res = await request.get(url);

    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getDoctorCompliancesByAppointmentId_apiCalls = async (appointmentId) => {
  try {
    const url = `${API_URLS.get_doctor_compliances_by_appointmentId_api_url}/${appointmentId}`;
    const res = await request.get(url);

    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getPatientCompliancesByAppointmentId_apiCalls = async (appointmentId) => {
  try {
    const url = `${API_URLS.get_patient_compliances_by_appointmentId_api_url}/${appointmentId}`;
    const res = await request.get(url);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};


