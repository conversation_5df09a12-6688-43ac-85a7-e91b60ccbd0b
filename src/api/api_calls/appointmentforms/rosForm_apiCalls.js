import { API_URLS } from "../../api_config/api_routes";
import { request } from "../../api_config/api_config";

// ================================
// ROS FORM APIs (Review of Systems)
// ================================

export const createRosForm_apiCalls = async (formData) => {
  try {
    const res = await request.post(API_URLS.create_rosForm_api_url, formData);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const updateRosForm_apiCalls = async (formData) => {
  try {
    const res = await request.put(API_URLS.update_rosForm_api_url, formData);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getRosFormById_apiCalls = async ({ id }) => {
  try {
    const res = await request.get(`${API_URLS.get_rosForm_by_id_api_url}/${id}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getRosFormByAppointmentId_apiCalls = async ({ appointmentId }) => {
  try {
    const res = await request.get(`${API_URLS.get_rosForm_by_appointmentId_api_url}/${appointmentId}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};
