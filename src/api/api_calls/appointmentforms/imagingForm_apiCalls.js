import { API_URLS } from "../../api_config/api_routes";
import { request } from "../../api_config/api_config";

// ================================
// IMAGING FORM APIs
// ================================

export const createImagingForm_apiCalls = async (formData) => {
  try {
    console.log("🚀 Creating imaging form with data:", formData);
    console.log("📍 API URL:", API_URLS.create_imagingForm_api_url);

    const res = await request.post(API_URLS.create_imagingForm_api_url, formData);
    console.log("✅ Create imaging form response:", res);

    if (res) {
      return res.data;
    }
  } catch (error) {
    console.error("❌ Create imaging form error:", error);
    console.error("❌ Error response:", error.response?.data);
    console.error("❌ Error status:", error.response?.status);
    throw new Error(error.message);
  }
};

export const updateImagingForm_apiCalls = async (formData) => {
  try {
    console.log("🔄 Updating imaging form with data:", formData);
    console.log("📍 API URL:", API_URLS.update_imagingForm_api_url);

    const res = await request.put(API_URLS.update_imagingForm_api_url, formData);
    console.log("✅ Update imaging form response:", res);

    if (res) {
      return res.data;
    }
  } catch (error) {
    console.error("❌ Update imaging form error:", error);
    console.error("❌ Error response:", error.response?.data);
    console.error("❌ Error status:", error.response?.status);
    throw new Error(error.message);
  }
};

export const getImagingFormById_apiCalls = async ({ id }) => {
  try {
    const res = await request.get(`${API_URLS.get_imagingForm_by_id_api_url}/${id}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getImagingFormByAppointmentId_apiCalls = async ({ appointmentId }) => {
  try {
    const res = await request.get(`${API_URLS.get_imagingForm_by_appointmentId_api_url}/${appointmentId}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getImagingFormByPatientId_apiCalls = async ({ patientId }) => {
  try {
    const res = await request.get(`${API_URLS.get_imagingForm_by_patientId_api_url}/${patientId}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};
