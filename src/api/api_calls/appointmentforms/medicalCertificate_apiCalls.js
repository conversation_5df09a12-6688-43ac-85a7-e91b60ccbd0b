import { API_URLS } from "../../api_config/api_routes";
import { request } from "../../api_config/api_config";

// ================================
// MEDICAL CERTIFICATE APIs
// ================================

export const createMedicalCertificate_apiCalls = async (formData) => {
  try {
    console.log("🚀 Creating medical certificate with data:", formData);
    console.log("📍 API URL:", API_URLS.create_medicalCertificate_api_url);

    const res = await request.post(API_URLS.create_medicalCertificate_api_url, formData);
    console.log("✅ Create medical certificate response:", res);

    if (res) {
      return res.data;
    }
  } catch (error) {
    console.error("❌ Create medical certificate error:", error);
    console.error("❌ Error response:", error.response?.data);
    console.error("❌ Error status:", error.response?.status);
    throw new Error(error.message);
  }
};

export const updateMedicalCertificate_apiCalls = async (formData) => {
  try {
    console.log("🔄 Updating medical certificate with data:", formData);
    console.log("📍 API URL:", API_URLS.update_medicalCertificate_api_url);

    const res = await request.put(API_URLS.update_medicalCertificate_api_url, formData);
    console.log("✅ Update medical certificate response:", res);

    if (res) {
      return res.data;
    }
  } catch (error) {
    console.error("❌ Update medical certificate error:", error);
    console.error("❌ Error response:", error.response?.data);
    console.error("❌ Error status:", error.response?.status);
    throw new Error(error.message);
  }
};

export const getMedicalCertificateById_apiCalls = async ({ id }) => {
  try {
    const res = await request.get(`${API_URLS.get_medicalCertificate_by_id_api_url}/${id}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getMedicalCertificateByAppointmentId_apiCalls = async ({ appointmentId }) => {
  try {
    const res = await request.get(`${API_URLS.get_medicalCertificate_by_appointmentId_api_url}/${appointmentId}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const getMedicalCertificateByPatientId_apiCalls = async ({ patientId }) => {
  try {
    const res = await request.get(`${API_URLS.get_medicalCertificate_by_patientId_api_url}/${patientId}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};
