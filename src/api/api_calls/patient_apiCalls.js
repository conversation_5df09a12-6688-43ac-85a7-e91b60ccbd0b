import { API_URLS } from "../api_config/api_routes";
import { request, formData_Request } from "../api_config/api_config";

export const createPatient_api = async ({
  email,
  password,
  firstName,
  lastName,
  contactNumber,
}) => {
  try {
    const res = await request.post(API_URLS.create_patient_api_url, {
      email,
      password,
      firstName,
      lastName,
      contactNumber,
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const requestForgotPatientPassword_api = async ({ email }) => {
  try {
    const res = await request.post(API_URLS.forgot_patientPassword_api_url, {
      email,
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const verifyForgotPatientPasswordOtp_api = async ({ email, otp }) => {
  try {
    const res = await request.post(
      API_URLS.verify_forgot_patient_passowrd_otp_api_url,
      {
        email,
        otp,
      },
    );
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const resetPatientPassword_api = async ({
  email,
  resetPasswordToken,
  newPassword,
}) => {
  try {
    const res = await request.post(API_URLS.reset_patientPassword_api_url, {
      email,
      resetPasswordToken,
      newPassword,
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const updatePatient_api = async ({
  patientData = {},
  profilePicture = null,
} = {}) => {
  try {
    const formData = new FormData();

    formData.append("patientData", JSON.stringify(patientData));

    // Append files only if they exist
    if (profilePicture) {
      formData.append("profilePicture", profilePicture);
    }

    // console.log("PAYLOAD => ", formData.entries);
    for (let [key, value] of formData.entries()) {
      console.log(`${key}: ${JSON.stringify(value, null, 2)}`);
    }
    // Make the PUT request with multipart/form-data
    const res = await formData_Request.put(
      API_URLS.update_patient_api_url,
      formData,
    );

    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const fetchConcernedPatients_api = async ({
  searchText = null,
  offset = null,
  limit = null,
} = {}) => {
  try {
    const res = await request.get(API_URLS.fetch_concerned_patients_api_url, {
      params: { searchText, offset, limit },
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const fetchPatientById_api = async ({ patientId = null } = {}) => {
  try {
    const url = `${API_URLS.fetch_patient_by_id_api_url}${patientId ? `/${patientId}` : ""}`;
    const res = await request.get(url);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};
