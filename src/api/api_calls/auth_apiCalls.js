import { API_URLS } from "../api_config/api_routes";
import { request } from "../api_config/api_config";

export const loginDoctor_api = async ({ email, password }) => {
  try {
    const res = await request.post(API_URLS.login_doctor_api_url, {
      email,
      password,
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const loginPatient_api = async ({ email, password }) => {
  try {
    const res = await request.post(API_URLS.login_patient_api_url, {
      email,
      password,
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const me_api = async () => {
  try {
    const res = await request.get(API_URLS.me_api_url);
    if (res) {
      return res.data;
    }
  } catch (error) {
    console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};
