import { API_URLS } from "../api_config/api_routes";
import { request, formData_Request } from "../api_config/api_config";

export const createDoctor_api = async ({
  email,
  password,
  firstName,
  lastName,
}) => {
  try {
    const res = await request.post(API_URLS.create_doctor_api_url, {
      email,
      password,
      firstName,
      lastName,
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const requestForgotDoctorPassword_api = async ({ email }) => {
  try {
    const res = await request.post(API_URLS.forgot_doctorPassword_api_url, {
      email,
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const verifyForgotDoctorPasswordOtp_api = async ({ email, otp }) => {
  try {
    const res = await request.post(
      API_URLS.verify_forgot_doctor_passowrd_otp_api_url,
      {
        email,
        otp,
      },
    );
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const resetDoctorPassword_api = async ({
  email,
  resetPasswordToken,
  newPassword,
}) => {
  try {
    const res = await request.post(API_URLS.reset_doctorPassword_api_url, {
      email,
      resetPasswordToken,
      newPassword,
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const updateDoctor_api = async ({
  doctorData = {},
  profilePicture = null,
  idFront = null,
  idBack = null,
  signature = null,
  qualificationDegreeFiles = [],
  practiceExperienceFiles = [],
} = {}) => {
  try {
    const formData = new FormData();

    // Safely append the doctorData
    formData.append("doctorData", JSON.stringify(doctorData));

    // Append files only if they exist
    if (profilePicture) {
      formData.append("profilePicture", profilePicture);
    }
    if (idFront) {
      formData.append("idFront", idFront);
    }
    if (idBack) {
      formData.append("idBack", idBack);
    }
    if (signature) {
      formData.append("signature", signature);
    }

    // Qualification files (array)
    if (qualificationDegreeFiles.length > 0) {
      qualificationDegreeFiles.forEach((file, index) => {
        formData.append(`qualification_${index}_degreeFile`, file);
      });
    }

    // Practice experience files (array)
    if (practiceExperienceFiles.length > 0) {
      practiceExperienceFiles.forEach((file, index) => {
        formData.append(`practice_${index}_experienceFile`, file);
      });
    }

    // console.log("PAYLOAD => ", formData.entries);
    for (let [key, value] of formData.entries()) {
      console.log(`${key}: ${JSON.stringify(value, null, 2)}`);
    }
    // Make the PUT request with multipart/form-data
    const res = await formData_Request.put(
      API_URLS.update_doctor_api_url,
      formData,
    );

    if (res) {
      return res.data;
    }
  } catch (error) {
    throw new Error(error.message);
  }
};

export const fetchDoctorsWithFilters_api = async ({
  speciality = null,
  date = null,
  country = null,
  language = null,
  offset = null,
  limit = null,
} = {}) => {
  try {
    const url = `${API_URLS.fetch_doctors_api_url}`;
    const res = await request.get(url, {
      params: { speciality, date, country, language, offset, limit },
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const fetchDoctorWithId_api = async ({ doctorId = null } = {}) => {
  try {
    const url = `${API_URLS.fetch_doctor_with_id_api_url}${doctorId ? `/${doctorId}` : ""}`;
    const res = await request.get(url);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};
