import { request } from "../api_config/api_config";
import { API_URLS } from "../api_config/api_routes";

export const getWeeklyScheduleOfDoctor_api = async ({
  doctorId = null,
} = {}) => {
  try {
    const url = `${API_URLS.get_weekly_schedule_doctor_api_url}${doctorId ? `/${doctorId}` : ""}`;
    const res = await request.get(url);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getOverrideScheduleOfDoctor_api = async ({
  doctorId = null,
} = {}) => {
  try {
    const url = `${API_URLS.get_override_schedule_doctor_api_url}${doctorId ? `/${doctorId}` : ""}`;
    const res = await request.get(url);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const createWeeklyScheduleOfDoctor_api = async ({ schedules }) => {
  try {
    const url = `${API_URLS.create_weekly_schedule_doctor_api_url}`;
    const res = await request.post(url, { schedules });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const createOverrideScheduleOfDoctor_api = async ({
  overrideSchedules,
}) => {
  try {
    const url = `${API_URLS.create_override_schedule_doctor_api_url}`;
    const res = await request.post(url, { overrideSchedules });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getSlotDetailsOfDoctor_api = async ({
  doctorId = null,
  startDate,
  endDate,
  requestedTimezone = null,
} = {}) => {
  try {
    const url = `${API_URLS.get_slots_details_doctor_api_url}${doctorId ? `/${doctorId}` : ""}`;
    const params = { startDate, endDate };

    // Add requested timezone if provided
    if (requestedTimezone) {
      params.requestedTimezone = requestedTimezone;
    }

    const res = await request.get(url, { params });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};
