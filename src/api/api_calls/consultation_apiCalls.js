import { API_URLS } from "../api_config/api_routes";
import { request } from "../api_config/api_config";

export const updateDoctorConsultations_api = async ({ consultations }) => {
  try {
    const res = await request.put(API_URLS.update_consultations_api_url, {
      consultations,
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getDoctorConsultations_api = async ({ doctorId = null } = {}) => {
  try {
    const url = `${API_URLS.get_consultations_api_url}${doctorId ? `/${doctorId}` : ""}`;
    const res = await request.get(url);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};
