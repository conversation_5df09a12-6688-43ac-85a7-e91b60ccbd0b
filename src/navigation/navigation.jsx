import React from "react";

import { Routes, Route, Navigate } from "react-router-dom";

// Layouts
import PublicRoutes from "../components/layouts/PublicLayout";
import DoctorRoutes from "../components/layouts/DoctorLayout";
import PatientRoutes from "../components/layouts/PatientLayout";

// Public Routes
import Home from "../containers/public/home/<USER>";
import AboutUs from "../containers/public/about-us/AboutUs";
import Services from "../containers/public/services/Services";
import Blogs from "../containers/public/blogs/Blogs";

// Doctor Routes
// Doctor Auth
import DoctorLogin from "../containers/auth/Doctor/login/Login";
import DoctorSignUp from "../containers/auth/Doctor/signup/Signup";
import DoctorVerifyOtp from "../containers/auth/Doctor/verifyotp/VerifyOtp";
import DoctorInformationScreen from "../containers/doctor-flow/doctor-detail-screen/DoctorDetailScreen";
import ProfileUnderReview from "../containers/doctor-flow/doctor-profile-underview/DoctorProfileUnderReview";
import Doctor<PERSON><PERSON>gotPasswordEmailScreen from "../containers/auth/Doctor/forgot-password/email/Email";
import DoctorForgotPasswordVerificationCodeScreen from "../containers/auth/Doctor/forgot-password/verify/Verify";
import DoctorForgotPasswordCreatePasswordScreen from "../containers/auth/Doctor/forgot-password/create-password/CreatePassword";

// Doctor Pages
import DoctorDashboard from "../containers/doctor-flow/doctor-dashboard/DoctorDashboard";
import DoctorPatientslistings from "../containers/doctor-flow/doctor-patients-listing-page/DoctorPatientslisting";
import DoctorAppointmentsListings from "../containers/doctor-flow/doctor-appointments-listing-page/DoctorAppointmentsListings";
import DoctorPaymentHistoryListings from "../containers/doctor-flow/doctor-payments-listing-page/DoctorPaymentHistoryListing";
import DoctorSharedPatientEMRListings from "../containers/doctor-flow/emr-sharing-listing-page/DoctorSharedPatientEMRListings";
import DoctorSetAvailabilityScreen from "../containers/doctor-flow/doctor-set-availability/DoctorSetAvailability";
import DoctorProfileScreen from "../containers/doctor-flow/doctor-profile/DoctorProfile";
import DoctorViewCalendar from "../containers/doctor-flow/doctor-view-calendar/DoctorViewCalendar";
import AppointmentsDetails from "../containers/doctor-flow/appointments-details/AppointmentsDetails";
import DoctorPatientProfile from "../containers/doctor-flow/doctor-patient-profile/DoctorPatientProfile";
import DoctorConsultationPanel from "../containers/doctor-flow/doctor-consultation-panel/DoctorConsultationPanel";
import DoctorViewDoctorProfile from "../containers/doctor-flow/doctor-view-doctor-profile/DoctorViewDoctorProfile";

// Patient Routes
import PatientLogin from "../containers/auth/Patient/login/Login";
import PatientSignup from "../containers/auth/Patient/signup/Signup";
import PatientVerifyOtp from "../containers/auth/Patient/verifyotp/VerifyOtp";
import PatientForgotPasswordEmailScreen from "../containers/auth/Patient/forgot-password/email/Email";
import PatientForgotPasswordVerificationCodeScreen from "../containers/auth/Patient/forgot-password/verify/Verify";
import PatientForgotPasswordCreatePasswordScreen from "../containers/auth/Patient/forgot-password/create-password/CreatePassword";
import PatientFindDoctor from "../containers/patient-flow/patient-find-doctor/PatientFindDoctor";
import PatientProfile from "../containers/patient-flow/patient-profile/PatientProfile";
import PatientPaymentHistory from "../containers/patient-flow/patient-payment-history/PatientPaymentHistory";
import PatientPrescription from "../containers/patient-flow/patient-prescription/PatientPrescription";
import PrescriptionDetails from "../components/patient-side-components/prescription-details/PrescriptionDetails";
import PatientAppointment from "../containers/patient-flow/patient-appointment/PatientAppointment";
import PatientDoctorListings from "../containers/patient-flow/patient-doctor-listing/PatientDoctorListings";
import PatientDoctorProfile from "../containers/patient-flow/patient-doctor-profile/PatientDoctorProfile";
import PatientBookAppointment from "../containers/patient-flow/patient-book-appointment/PatientBookAppointment";
import PatientConfirmBooking from "../containers/patient-flow/patient-confirm-booking/PatientConfirmBooking";
import PatientQuestionnaire from "../containers/patient-flow/patient-questionnaire/PatientQuestionnaire";
import PatientAppointmentsDetails from "../containers/patient-flow/paitent-appointment-details/PatientAppointmentsDetails";
import PatientCompleteProfile from "../containers/patient-flow/patient-complete-profile/PatientCompleteProfile";

// GUARDS
import {
  DoctorProtectedRoute,
  DoctorProfileCompleteRoute,
} from "./doctorRoutesGuard";
import { PatientProtectedRoute } from "./patientRoutesGuard";
import { useSelector } from "react-redux";

const Navigation = () => {
  const user = useSelector((state) => state.userReducer.user);

  if (user) {
    // USER LOGGED IN AS DOCTOR
    if (user.userType === "doctor") {
      return (
        <Routes>
          <Route path="/doctor" element={<DoctorRoutes />}>
            {/* Add explicit route for the base doctor path */}
            <Route
              index
              element={<Navigate to="/doctor/dashboard" replace />}
            />

            {/* PRIVATE DOCTOR ROUTES*/}
            <Route element={<DoctorProtectedRoute />}>
              <Route path="information" element={<DoctorInformationScreen />} />
              <Route
                path="profileUnderReview"
                element={<ProfileUnderReview />}
              />
              {/* PRIVATE DOCTOR ROUTES IF HIS PROFILE IS COMPLETE AND VERIFIED */}
              <Route element={<DoctorProfileCompleteRoute />}>
                <Route path="dashboard" element={<DoctorDashboard />} />
                <Route
                  path="profileUnderReview"
                  element={<ProfileUnderReview />}
                />
                <Route path="patients" element={<DoctorPatientslistings />} />
                <Route
                  path="appointments"
                  element={<DoctorAppointmentsListings />}
                />
                <Route
                  path="payments"
                  element={<DoctorPaymentHistoryListings />}
                />
                <Route
                  path="emr-sharing"
                  element={<DoctorSharedPatientEMRListings />}
                />
                <Route
                  path="set-availability"
                  element={<DoctorSetAvailabilityScreen />}
                />
                <Route path="profile" element={<DoctorProfileScreen />} />
                <Route path="view-calendar" element={<DoctorViewCalendar />} />
                <Route
                  path="appointments-details"
                  element={<AppointmentsDetails />}
                />
                <Route
                  path="patient-profile"
                  element={<DoctorPatientProfile />}
                />
                <Route
                  path="doctor-consultation-panel"
                  element={<DoctorConsultationPanel />}
                />
                <Route
                  path="view-doctor-profile"
                  element={<DoctorViewDoctorProfile />}
                />
              </Route>
            </Route>
          </Route>
          {/* REDIRECT TO EITHER DASHBOARD OR PROFILE COMPLETE SCREEN IF ANY UNKNOWN ROUTE APPEARS */}
          {user.userType === "doctor" && !user.isProfileComplete ? (
            <Route
              path="*"
              element={<Navigate to="/doctor/information" replace />}
            />
          ) : user.userType === "doctor" && !user.isProfileVerfied ? (
            <Route
              path="*"
              element={<Navigate to="/doctor/profileUnderReview" replace />}
            />
          ) : (
            <Route
              path="*"
              element={<Navigate to="/doctor/dashboard" replace />}
            />
          )}
        </Routes>
      );
    } else if (user.userType === "patient") {
      // USER LOGGED IN AS PATIENT
      return (
        <Routes>
          <Route path="/patient" element={<PatientRoutes />}>
            {/* Add explicit route for the base patient path */}
            <Route
              path="complete-profile"
              element={
                user && !user.isProfileComplete ? (
                  <PatientCompleteProfile />
                ) : (
                  <Navigate to="/patient/find-doctor" replace />
                )
              }
            />
            <Route
              index
              element={<Navigate to="/patient/find-doctor" replace />}
            />

            {/* PRIVATE PATIENT ROUTES */}
            <Route element={<PatientProtectedRoute />}>
              <Route path="find-doctor" element={<PatientFindDoctor />} />
              <Route
                path="doctor-listing"
                element={<PatientDoctorListings />}
              />
              <Route path="doctor-profile" element={<PatientDoctorProfile />} />
              <Route
                path="book-appointment"
                element={<PatientBookAppointment />}
              />

              <Route
                path="confirm-appointment"
                element={<PatientConfirmBooking />}
              />

              <Route
                path="confirm-questionnaire"
                element={<PatientQuestionnaire />}
              />
              <Route
                path="appointments-details"
                element={<PatientAppointmentsDetails />}
              />

              <Route path="profile" element={<PatientProfile />} />
              <Route path="payments" element={<PatientPaymentHistory />} />
              <Route path="prescription" element={<PatientPrescription />} />
              <Route
                path="prescription-details"
                element={<PrescriptionDetails />}
              />
              <Route path="appointment" element={<PatientAppointment />} />
            </Route>
          </Route>
          {/* REDIRECT TO DASHBOARD IF ANY UNKNOW ROUTE APPEARS */}
          <Route
            path="*"
            element={<Navigate to="/patient/find-doctor" replace />}
          />
        </Routes>
      );
    } else {
      // FALL BACK TO HOME SCREEN IF USER-TYPE IS UNKNOWN
      return (
        <Routes>
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      );
    }
  } else {
    // PUBLIC ROUTES (NO USER IS LOGGED-IN)
    return (
      <Routes>
        {/* ALL PUBLIC ROUTES */}
        <Route path="/" element={<PublicRoutes />}>
          <Route index element={<Home />} />
          <Route path="about" element={<AboutUs />} />
          <Route path="services" element={<Services />} />
          <Route path="blogs" element={<Blogs />} />
        </Route>

        {/* DOCTOR PUBLIC ROUTES */}
        <Route path="/doctor" element={<DoctorRoutes />}>
          <Route index element={<Navigate to="/doctor/login" replace />} />
          <Route path="login" element={<DoctorLogin />} />
          <Route path="signup" element={<DoctorSignUp />} />
          <Route path="verify-otp" element={<DoctorVerifyOtp />} />
          <Route
            path="forgot-password/email"
            element={<DoctorForgotPasswordEmailScreen />}
          />
          <Route
            path="forgot-password/verify"
            element={<DoctorForgotPasswordVerificationCodeScreen />}
          />
          <Route
            path="forgot-password/create-password"
            element={<DoctorForgotPasswordCreatePasswordScreen />}
          />
        </Route>

        {/* PATIENT PUBLIC ROUTES */}
        <Route path="/patient" element={<PatientRoutes />}>
          <Route index element={<Navigate to="/patient/login" replace />} />
          <Route path="login" element={<PatientLogin />} />
          <Route path="signup" element={<PatientSignup />} />
          <Route path="verify-otp" element={<PatientVerifyOtp />} />
          <Route
            path="forgot-password/email"
            element={<PatientForgotPasswordEmailScreen />}
          />
          <Route
            path="forgot-password/verify"
            element={<PatientForgotPasswordVerificationCodeScreen />}
          />
          <Route
            path="forgot-password/create-password"
            element={<PatientForgotPasswordCreatePasswordScreen />}
          />
        </Route>

        {/* FALL BACK TO HOME SCREEN IF ANY UNKNOW ROUTE APPEARS*/}
        <Route path="*" element={<Navigate to="/" replace />} />
      </Routes>
    );
  }
};

export default Navigation;
