import React from "react";
import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";

export const DoctorProtectedRoute = () => {
  const user = useSelector((state) => state.userReducer.user);
  const location = useLocation();

  if (!user || user.userType !== "doctor") {
    return <Navigate to="/doctor/login" state={{ from: location }} replace />;
  }

  return <Outlet />;
};

export const DoctorProfileCompleteRoute = () => {
  const user = useSelector((state) => state.userReducer.user);
  const location = useLocation();

  if (!user || user.userType !== "doctor") {
    return <Navigate to="/doctor/login" state={{ from: location }} replace />;
  }

  if (user && user.userType === "doctor" && !user.isProfileComplete) {
    return <Navigate to="/doctor/information" replace />;
  }

  if (user && user.userType === "doctor" && !user.isProfileVerfied) {
    return <Navigate to="/doctor/profileUnderReview" replace />;
  }

  return <Outlet />;
};
