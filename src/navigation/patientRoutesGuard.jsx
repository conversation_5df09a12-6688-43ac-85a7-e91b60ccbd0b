import React from "react";
import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";

export const PatientProtectedRoute = () => {
  const user = useSelector((state) => state.userReducer.user);
  const location = useLocation();

  if (!user || user.userType !== "patient") {
    return <Navigate to="/patient/login" state={{ from: location }} replace />;
  }
  if (user && user.userType === "patient" && !user.isProfileComplete) {
    return <Navigate to="/patient/complete-profile" replace />;
  }

  return <Outlet />;
};
