import Countries from "../constants/countries";

export const getArrayCountryLabels = (values) => {
  if (!Array.isArray(values) || values.length === 0) {
    return "Not specified";
  }

  const labels = values
    .map((value) => {
      const country = Countries.find((country) => country.value === value);
      return country ? country.label : null;
    })
    .filter((label) => label !== null);

  return labels.length > 0 ? labels.join(", ") : "Not specified";
};

export const getSingleCountryLabel = (value) => {
  if (!value) {
    return "Not specified";
  }

  const country = Countries.find((country) => country.value === value);
  return country ? country.label : "Not specified";
};
