/**
 * Timezone conversion utilities for handling doctor-patient timezone differences
 */

/**
 * Get user's current timezone
 */
export const getUserTimezone = () => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.error("Error detecting timezone:", error);
    return "UTC";
  }
};

/**
 * Convert time from doctor's timezone to patient's timezone
 * @param {string} timeString - Time in HH:mm:ss format
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} doctorTimezone - Doctor's timezone (e.g., "Asia/Shanghai")
 * @param {string} patientTimezone - Patient's timezone (e.g., "Asia/Karachi")
 * @returns {Object} - { time: string, date: string }
 */
export const convertTimeToPatientTimezone = (
  timeString,
  date,
  doctorTimezone,
  patientTimezone,
) => {
  if (
    !doctorTimezone ||
    !patientTimezone ||
    doctorTimezone === patientTimezone
  ) {
    return { time: timeString, date };
  }

  try {
    // Extremely simple approach: Use known timezone offsets
    // Shanghai is UTC+8, Karachi is UTC+5
    // So Shanghai is 3 hours ahead of Karachi

    // Parse the time
    const [hours, minutes, seconds] = timeString.split(":").map(Number);

    // Create a date object for calculation
    let resultHours = hours;
    let resultMinutes = minutes;
    let resultSeconds = seconds;
    let resultDate = date;

    // Simple hardcoded conversion for common timezones
    // This is a temporary solution to test the concept
    if (
      doctorTimezone === "Asia/Shanghai" &&
      patientTimezone === "Asia/Karachi"
    ) {
      // Shanghai is UTC+8, Karachi is UTC+5, so subtract 3 hours
      resultHours = hours - 3;
      if (resultHours < 0) {
        resultHours += 24;
        // Date would change, but we're ignoring that for now
      }
    } else if (
      doctorTimezone === "America/New_York" &&
      patientTimezone === "Europe/London"
    ) {
      // New York is UTC-5 (or UTC-4 in DST), London is UTC+0 (or UTC+1 in DST)
      // Assuming standard time: add 5 hours
      resultHours = hours + 5;
      if (resultHours >= 24) {
        resultHours -= 24;
        // Date would change, but we're ignoring that for now
      }
    } else {
      // For other timezone combinations, use the dynamic approach
      const baseDate = new Date(`${date}T${timeString}`);
      const referenceDate = new Date();

      const doctorOffset = getDifferenceFromUTC(doctorTimezone, referenceDate);
      const patientOffset = getDifferenceFromUTC(
        patientTimezone,
        referenceDate,
      );

      const offsetDifference = patientOffset - doctorOffset;
      const convertedDate = new Date(baseDate.getTime() + offsetDifference);

      resultHours = convertedDate.getHours();
      resultMinutes = convertedDate.getMinutes();
      resultSeconds = convertedDate.getSeconds();

      const resultYear = convertedDate.getFullYear();
      const resultMonth = String(convertedDate.getMonth() + 1).padStart(2, "0");
      const resultDay = String(convertedDate.getDate()).padStart(2, "0");
      resultDate = `${resultYear}-${resultMonth}-${resultDay}`;
    }

    // Format the result
    const resultTime = `${String(resultHours).padStart(2, "0")}:${String(resultMinutes).padStart(2, "0")}:${String(resultSeconds).padStart(2, "0")}`;

    return {
      time: resultTime,
      date: resultDate,
    };
  } catch (error) {
    console.error("Error converting timezone:", error);
    return { time: timeString, date };
  }
};

/**
 * Get the time difference from UTC for a specific timezone
 * @param {string} timezone - Timezone identifier (e.g., "Asia/Shanghai")
 * @param {Date} referenceDate - Reference date to calculate offset
 * @returns {number} - Offset in milliseconds from UTC
 */
const getDifferenceFromUTC = (timezone, referenceDate) => {
  try {
    // Get the time in UTC
    const utcTime = new Date(
      referenceDate.toLocaleString("en-US", { timeZone: "UTC" }),
    );

    // Get the time in the target timezone
    const targetTime = new Date(
      referenceDate.toLocaleString("en-US", { timeZone: timezone }),
    );

    // Return the difference in milliseconds
    return targetTime.getTime() - utcTime.getTime();
  } catch (error) {
    console.error("Error getting timezone difference:", error);
    return 0;
  }
};

/**
 * Format time for display with timezone information
 * @param {string} timeString - Time in HH:mm:ss format
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} doctorTimezone - Doctor's timezone
 * @param {string} patientTimezone - Patient's timezone
 * @returns {Object} - Formatted time information
 */
export const formatTimeWithTimezone = (
  timeString,
  date,
  doctorTimezone,
  patientTimezone,
) => {
  const converted = convertTimeToPatientTimezone(
    timeString,
    date,
    doctorTimezone,
    patientTimezone,
  );

  // Format time for display (12-hour format)
  const formatTime = (time) => {
    const [hours, minutes] = time.split(":");
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? "PM" : "AM";
    const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    return `${displayHour}:${minutes} ${ampm}`;
  };

  const originalFormatted = formatTime(timeString);
  const convertedFormatted = formatTime(converted.time);

  return {
    original: {
      time: timeString,
      formatted: originalFormatted,
      timezone: doctorTimezone,
    },
    converted: {
      time: converted.time,
      formatted: convertedFormatted,
      timezone: patientTimezone,
      date: converted.date,
    },
    displayTime: convertedFormatted,
    displayDate: converted.date,
  };
};

/**
 * Get timezone display name
 * @param {string} timezone - Timezone identifier
 * @returns {string} - Human readable timezone name
 */
export const getTimezoneDisplayName = (timezone) => {
  try {
    const now = new Date();
    const formatter = new Intl.DateTimeFormat("en-US", {
      timeZone: timezone,
      timeZoneName: "short",
    });

    const parts = formatter.formatToParts(now);
    const timeZoneName = parts.find((part) => part.type === "timeZoneName");
    return timeZoneName ? timeZoneName.value : timezone;
  } catch (error) {
    return timezone;
  }
};

/**
 * Check if timezone conversion is needed
 * @param {string} doctorTimezone
 * @param {string} patientTimezone
 * @returns {boolean}
 */
export const isTimezoneConversionNeeded = (doctorTimezone, patientTimezone) => {
  return (
    doctorTimezone && patientTimezone && doctorTimezone !== patientTimezone
  );
};

/**
 * Test function to verify timezone conversion
 * Example: Doctor in Shanghai (UTC+8) at 09:00 should be 06:00 in Karachi (UTC+5)
 */
export const testTimezoneConversion = () => {
  console.log("Testing timezone conversion...");

  // Debug: Check what timezone offsets we're getting
  const now = new Date();
  console.log("Current time:", now);

  const shanghaiTime = new Date(
    now.toLocaleString("en-US", { timeZone: "Asia/Shanghai" }),
  );
  const karachiTime = new Date(
    now.toLocaleString("en-US", { timeZone: "Asia/Karachi" }),
  );
  const utcTime = new Date(now.toLocaleString("en-US", { timeZone: "UTC" }));

  console.log("Shanghai time:", shanghaiTime);
  console.log("Karachi time:", karachiTime);
  console.log("UTC time:", utcTime);

  const shanghaiOffset = shanghaiTime.getTime() - utcTime.getTime();
  const karachiOffset = karachiTime.getTime() - utcTime.getTime();

  console.log("Shanghai offset (ms):", shanghaiOffset);
  console.log("Karachi offset (ms):", karachiOffset);
  console.log("Difference (ms):", karachiOffset - shanghaiOffset);
  console.log(
    "Difference (hours):",
    (karachiOffset - shanghaiOffset) / (1000 * 60 * 60),
  );

  // Test 1: Shanghai to Karachi
  const result1 = convertTimeToPatientTimezone(
    "09:00:00",
    "2025-06-23",
    "Asia/Shanghai",
    "Asia/Karachi",
  );
  console.log("Shanghai 09:00 -> Karachi:", result1);
  // Expected: { time: "06:00:00", date: "2025-06-23" }

  return { test1: result1 };
};
