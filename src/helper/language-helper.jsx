import Languages from "../constants/languages";

export const getArrayLanguageLabels = (values) => {
  if (!Array.isArray(values) || values.length === 0) {
    return "Not specified";
  }

  const labels = values
    .map((value) => {
      const language = Languages.find((lang) => lang.value === value);
      return language ? language.label : null;
    })
    .filter((label) => label !== null);

  return labels.length > 0 ? labels.join(", ") : "Not specified";
};

export const getSingleLanguageLabel = (value) => {
  if (!value) {
    return "Not specified";
  }

  const language = Languages.find((lang) => lang.value === value);
  return language ? language.label : "Not specified";
};
