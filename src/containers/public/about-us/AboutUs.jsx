import { Helmet } from "react-helmet";
import React, { Suspense } from "react";
import LoadingSpinner from "../../../components/ui/LoadingSpinner";
import ErrorBoundary from "../../../components/public/ErrorBoundary/ErrorBoundary";
import AboutUsHeaderComponent from "../../../components/public/AboutUsHeaderComponent/AboutUsHeaderComponent";
import AboutUsWhoWeAreComponent from "../../../components/public/AboutUsWhoWeAreComponent/AboutUsWhoWeAreComponent";
import AboutUsOurValuesComponent from "../../../components/public/AboutUsOurValuesComponent/AboutUsOurValuesComponent";
import TheFutureIsHereComponent from "../../../components/public/TheFutureIsHereComponent/TheFutureIsHereComponent";
import LeadershipComponent from "../../../components/public/LeadershipComponent/LeadershipComponent";


const AboutUs = () => {
  return (
    <div className="flex flex-col min-h-screen">
      <Helmet>
        <title>About Us | DocMobil</title>
        <meta name="description" content="Learn about our mission and team" />
      </Helmet>

      <main className="flex-1 " aria-label="About us content">
        <AboutUsHeaderComponent />

        <ErrorBoundary>
          <Suspense fallback={<LoadingSpinner />}>
            <AboutUsWhoWeAreComponent id="who-we-are" />
            <AboutUsOurValuesComponent id="our-values" />
            <TheFutureIsHereComponent id="future" />
            <LeadershipComponent id="leadership" />
          </Suspense>
        </ErrorBoundary>
      </main>



      {/* Mobile-friendly sticky CTA */}
      {/* <div className="sticky bottom-0 bg-white py-4 shadow-lg lg:hidden px-4">
        <Button className="w-full">Book Appointment</Button>
      </div> */}
    </div>
  );
};

export default AboutUs;
