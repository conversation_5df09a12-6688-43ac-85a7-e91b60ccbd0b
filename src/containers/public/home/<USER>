import React from "react";
import { Suspense } from "react";
import { Helmet } from "react-helmet";
import LoadingSpinner from "../../../components/ui/LoadingSpinner";
import ErrorBoundary from "../../../components/public/ErrorBoundary/ErrorBoundary";


import { TestimonialCarousel } from "../../../components/TestimonialCarousel/TestimonialCarousel";
import HomeHeaderComponent from "../../../components/public/HomeHeaderComponent/HomeHeaderComponent";
import ServicesComponent from "../../../components/public/ServicesComponent/ServicesComponent";
import DifferentiatorsComponent from "../../../components/public/DifferentiatorsComponent/DifferentiatorsComponent";
import OurPartnersComponent from "../../../components/public/OurPartnersComponent/OurPartnersComponent";
import HowItWorksComponent from "../../../components/public/HowItWorksComponent/HowItWorksComponent";
import LatestPosts from "../../../components/public/LatestPosts/LatestPosts";
import LeadershipComponent from "../../../components/public/LeadershipComponent/LeadershipComponent";

const Home = () => {
  return (
    <div className="flex flex-col min-h-screen">
      <Helmet>
        <title>DocMobil | AI-Powered Hybrid Healthcare Platform</title>
        <meta
          name="description"
          content="Experience seamless healthcare combining the best of in-person and virtual care with DocMobil's AI-powered platform."
        />
      </Helmet>

      <main className="flex-1 " aria-label="Home Content">
        <HomeHeaderComponent />

        <ErrorBoundary>
          <Suspense fallback={<LoadingSpinner />}>
            <ServicesComponent id="best-medical-services" />
            <DifferentiatorsComponent id="whats-make-us-different" />
            <OurPartnersComponent id="our-partners" />
            <HowItWorksComponent id="how-does-it-works" />
            <TestimonialCarousel id="whats-our-patient-say" />
            <LeadershipComponent id="leadership" />
            <LatestPosts id="latest-posts" />
          </Suspense>
        </ErrorBoundary>
      </main>
    </div>
  );
};

export default Home;
