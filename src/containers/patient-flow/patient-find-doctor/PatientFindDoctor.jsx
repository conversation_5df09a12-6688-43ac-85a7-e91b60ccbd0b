import React from "react";
import { useEffect, useMemo, useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "../../../components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../../components/ui/popover";
import { Button } from "../../../components/ui/button";
import { FaAngleDown } from "react-icons/fa6";
import { Calendar } from "../../../components/ui/calendar";
import { cn } from "../../../lib/utils";
import { format } from "date-fns";
import { ReactComponent as CountryIcon } from "../../../assets/svgs/LanguageIcon.svg";
import { fetchDoctorsWithFilters_api } from "../../../api/api_calls/doctor_apiCalls";
import { toast } from "../../../hooks/use-toast";
import { ReactComponent as StethoscopeIcon } from "../../../assets/svgs/StethoscopeIcon.svg";
import { ReactComponent as CalendarIcon } from "../../../assets/svgs/CalendarIcon.svg";
import { ReactComponent as LocationBlueIcon } from "../../../assets/svgs/LocationBlueIcon.svg";
import DoctorCard from "../../../components/doctor-card/DoctorCard";
import { Label } from "../../../components/ui/label";
import { doctorSpecialties } from "../../../constants/doctorSpecialties";
import Nudge from "../../../components/nudge/Nudge";
import FadeInSection from "../../../components/animations/FadeInSection";
import { Checkbox } from "../../../components/ui/checkbox";
import { Badge } from "../../../components/ui/badge";
import LoadingSpinner from "../../../components/animations/LoadingSpinner";
import { useSelector } from "react-redux";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "../../../components/ui/carousel";

const PatientFindDoctor = () => {
  const today = new Date();
  const navigate = useNavigate();
  const user = useSelector((state) => state.userReducer.user);

  const [selectedSpecialty, setSelectedSpecialty] = useState([]);
  const [selectedCountry, setSelectedCountry] = useState([]);
  const [selectedLanguage, setSelectedLanguage] = useState([]);
  const allSpecializations = doctorSpecialties.map((item) => item.name);
  const [selectedDate, setSelectedDate] = useState();
  const [month, setMonth] = useState(today);
  const [availableDoctors, setAvailableDoctors] = useState([]);
  const [showNudge, setShowNudge] = useState(true);
  const [profileCompletion] = useState(70);
  const [isLoading, setIsLoading] = useState(false);

  // Refs and state for categories scrolling
  const categoriesRef = useRef(null);
  const [isDragging, setIsDragging] = useState(false);
  const [startX, setStartX] = useState(0);
  const [scrollLeft, setScrollLeft] = useState(0);

  // Refs and state for doctor cards scrolling
  const doctorCardsRef = useRef(null);
  const [isDoctorCardsDragging, setIsDoctorCardsDragging] = useState(false);
  const [doctorCardsStartX, setDoctorCardsStartX] = useState(0);
  const [doctorCardsScrollLeft, setDoctorCardsScrollLeft] = useState(0);

  const handleMonthChange = (newMonth) => {
    const currentDate = new Date();
    const firstDayOfNewMonth = new Date(
      newMonth.getFullYear(),
      newMonth.getMonth(),
      1,
    );
    const firstDayOfCurrentMonth = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      1,
    );

    if (firstDayOfNewMonth >= firstDayOfCurrentMonth) {
      setMonth(newMonth);
    } else {
      setMonth(currentDate);
    }
  };

  const fetchAvailableDoctors = async () => {
    try {
      setIsLoading(true);
      const res = await fetchDoctorsWithFilters_api({
        date: new Date(),
        offset: 0,
        limit: 10,
      });

      setAvailableDoctors(res.data.doctors);
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN fetchAvailableDoctors  => ", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearchAvailability = () => {
    navigate("/patient/doctor-listing", {
      state: {
        selectedSpecialty,
        selectedDate,
        selectedCountry,
        selectedLanguage,
      },
    });
  };

  const handleNudgeAction = () => {
    navigate("/patient/profile");
  };

  useEffect(() => {
    fetchAvailableDoctors();
  }, []);

  // Check if any filter is selected
  const isAnyFilterSelected = useMemo(() => {
    return (
      selectedSpecialty.length > 0 ||
      selectedDate !== undefined ||
      selectedCountry.length > 0 ||
      selectedLanguage.length > 0
    );
  }, [selectedSpecialty, selectedDate, selectedCountry, selectedLanguage]);

  // Mouse event handlers for categories scrolling
  const handleMouseDown = (e) => {
    if (!categoriesRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - categoriesRef.current.offsetLeft);
    setScrollLeft(categoriesRef.current.scrollLeft);
  };

  const handleMouseMove = (e) => {
    if (!isDragging || !categoriesRef.current) return;
    e.preventDefault();
    const x = e.pageX - categoriesRef.current.offsetLeft;
    const walk = (x - startX) * 2; // Scroll speed multiplier
    categoriesRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleMouseLeave = () => {
    setIsDragging(false);
  };

  // Mouse event handlers for doctor cards scrolling
  const handleDoctorCardsMouseDown = (e) => {
    if (!doctorCardsRef.current) return;
    setIsDoctorCardsDragging(true);
    setDoctorCardsStartX(e.pageX - doctorCardsRef.current.offsetLeft);
    setDoctorCardsScrollLeft(doctorCardsRef.current.scrollLeft);
  };

  const handleDoctorCardsMouseMove = (e) => {
    if (!isDoctorCardsDragging || !doctorCardsRef.current) return;
    e.preventDefault();
    const x = e.pageX - doctorCardsRef.current.offsetLeft;
    const walk = (x - doctorCardsStartX) * 2; // Scroll speed multiplier
    doctorCardsRef.current.scrollLeft = doctorCardsScrollLeft - walk;
  };

  const handleDoctorCardsMouseUp = () => {
    setIsDoctorCardsDragging(false);
  };

  const handleDoctorCardsMouseLeave = () => {
    setIsDoctorCardsDragging(false);
  };

  return (
    <div className="flex w-full items-center justify-center flex-col relative">
      {user?.insuranceDetails?.isInsuranceDetailsComplete ? null : (
        <Nudge
          isVisible={showNudge}
          message="Complete your profile for personalized experience"
          progress={profileCompletion}
          onAction={handleNudgeAction}
          onDismiss={() => setShowNudge(false)}
          position="top-right"
          variant="gradient"
        />
      )}

      <div className="w-full  p-4 md:p-8 lg:px-16 space-y-16">
        <div className="w-full flex flex-col gap-4 ">
          <FadeInSection>
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold">
              Find your Doctor
            </h1>

            <div className="w-full grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mt-8">
              <div>
                <Label>Specialist</Label>
                <div className="sm:col-span-1 lg:col-span-1 relative">
                  <Select
                    onValueChange={(value) => {
                      if (selectedSpecialty.includes(value)) {
                        setSelectedSpecialty((prev) =>
                          prev.filter((spec) => spec !== value),
                        );
                      } else {
                        setSelectedSpecialty((prev) => [...prev, value]);
                      }
                    }}
                  >
                    <SelectTrigger className="w-full">
                      <div className="flex items-center gap-2">
                        <StethoscopeIcon className="h-4 w-4 flex-shrink-0" />
                        <div className="flex flex-wrap gap-1 max-w-full overflow-hidden">
                          {selectedSpecialty.length === 0 ? (
                            <span className="text-muted-foreground">
                              Select specialty
                            </span>
                          ) : (
                            <>
                              {selectedSpecialty
                                .slice(0, 2)
                                .map((spec, index) => (
                                  <Badge
                                    key={index}
                                    variant="outline"
                                    className="flex items-center gap-1 px-2 py-1 rounded-full bg-blue-50 text-blue-700 border-blue-200"
                                  >
                                    <span className="truncate max-w-[45px]">
                                      {spec}
                                    </span>
                                  </Badge>
                                ))}
                              {selectedSpecialty.length > 2 && (
                                <Badge
                                  variant="outline"
                                  className="px-2 py-1 rounded-full bg-blue-50 text-blue-700 border-blue-200"
                                >
                                  +{selectedSpecialty.length - 2}
                                </Badge>
                              )}
                            </>
                          )}
                        </div>
                      </div>
                    </SelectTrigger>
                    <SelectContent>
                      {allSpecializations.map((spec, index) => (
                        <SelectItem
                          key={index}
                          value={spec}
                          className="flex items-center p-0 cursor-default focus:bg-transparent"
                          onSelect={(e) => {
                            e.preventDefault();
                          }}
                        >
                          <div className="flex items-center gap-2 w-full p-2 hover:bg-accent hover:text-accent-foreground rounded-sm">
                            <Checkbox
                              id={`specialty-${index}`}
                              checked={selectedSpecialty.includes(spec)}
                              onCheckedChange={() => {
                                if (selectedSpecialty.includes(spec)) {
                                  setSelectedSpecialty((prev) =>
                                    prev.filter((item) => item !== spec),
                                  );
                                } else {
                                  setSelectedSpecialty((prev) => [
                                    ...prev,
                                    spec,
                                  ]);
                                }
                              }}
                            />
                            <label
                              htmlFor={`specialty-${index}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer w-full"
                            >
                              {spec}
                            </label>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label>Date</Label>
                <div className="sm:col-span-1">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full text-left font-normal flex items-center justify-between",
                          !selectedDate && "text-muted-foreground",
                        )}
                      >
                        <div className="flex items-center flex-row gap-2">
                          <CalendarIcon />
                          <span className="text-[16px] text-start">
                            {selectedDate
                              ? format(selectedDate, "PPP")
                              : "Pick a date"}
                          </span>
                        </div>

                        <FaAngleDown
                          style={{
                            height: "16px",
                            width: "16px",
                            color: "#0052FD",
                          }}
                        />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={selectedDate}
                        onSelect={(date) => {
                          if (
                            date >= new Date(new Date().setHours(0, 0, 0, 0))
                          ) {
                            const utcDate = new Date(
                              Date.UTC(
                                date.getFullYear(),
                                date.getMonth(),
                                date.getDate(),
                              ),
                            );
                            setSelectedDate(utcDate);
                          }
                        }}
                        month={month}
                        onMonthChange={handleMonthChange}
                        defaultMonth={today}
                        disabled={(date) =>
                          date < new Date(today.setHours(0, 0, 0, 0))
                        }
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>

              <div>
                <Label>Location</Label>
                <div className="sm:col-span-1">
                  <div className="relative w-full">
                    <Select
                      onValueChange={(value) => {
                        if (selectedCountry.includes(value)) {
                          setSelectedCountry((prev) =>
                            prev.filter((country) => country !== value),
                          );
                        } else {
                          setSelectedCountry((prev) => [...prev, value]);
                        }
                      }}
                    >
                      <SelectTrigger className="w-full">
                        <div className="flex items-center gap-2">
                          <LocationBlueIcon className="h-4 w-4 flex-shrink-0" />
                          <div className="flex flex-wrap gap-1 max-w-full overflow-hidden">
                            {selectedCountry.length === 0 ? (
                              <span className="text-muted-foreground">
                                Select country
                              </span>
                            ) : (
                              <>
                                {selectedCountry
                                  .slice(0, 2)
                                  .map((country, index) => (
                                    <Badge
                                      key={index}
                                      variant="outline"
                                      className="flex items-center gap-1 px-2 py-1 rounded-full bg-blue-50 text-blue-700 border-blue-200"
                                    >
                                      <span className="truncate max-w-[45px]">
                                        {country === "usa"
                                          ? "United States"
                                          : country === "uk"
                                            ? "United Kingdom"
                                            : country === "canada"
                                              ? "Canada"
                                              : country}
                                      </span>
                                    </Badge>
                                  ))}
                                {selectedCountry.length > 2 && (
                                  <Badge
                                    variant="outline"
                                    className="px-2 py-1 rounded-full bg-blue-50 text-blue-700 border-blue-200"
                                  >
                                    +{selectedCountry.length - 2}
                                  </Badge>
                                )}
                              </>
                            )}
                          </div>
                        </div>
                      </SelectTrigger>
                      <SelectContent>
                        {[
                          { value: "usa", label: "United States" },
                          { value: "uk", label: "United Kingdom" },
                          { value: "canada", label: "Canada" },
                        ].map((country, index) => (
                          <SelectItem
                            key={index}
                            value={country.value}
                            className="flex items-center p-0 cursor-default focus:bg-transparent"
                            onSelect={(e) => {
                              e.preventDefault();
                            }}
                          >
                            <div className="flex items-center gap-2 w-full p-2 hover:bg-accent hover:text-accent-foreground rounded-sm">
                              <Checkbox
                                id={`country-${index}`}
                                checked={selectedCountry.includes(
                                  country.value,
                                )}
                                onCheckedChange={() => {
                                  if (selectedCountry.includes(country.value)) {
                                    setSelectedCountry((prev) =>
                                      prev.filter(
                                        (item) => item !== country.value,
                                      ),
                                    );
                                  } else {
                                    setSelectedCountry((prev) => [
                                      ...prev,
                                      country.value,
                                    ]);
                                  }
                                }}
                              />
                              <label
                                htmlFor={`country-${index}`}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer w-full"
                              >
                                {country.label}
                              </label>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div>
                <Label>Language</Label>
                <div className="sm:col-span-1">
                  <div className="relative w-full">
                    <Select
                      onValueChange={(value) => {
                        if (selectedLanguage.includes(value)) {
                          setSelectedLanguage((prev) =>
                            prev.filter((lang) => lang !== value),
                          );
                        } else {
                          setSelectedLanguage((prev) => [...prev, value]);
                        }
                      }}
                    >
                      <SelectTrigger className="w-full">
                        <div className="flex items-center gap-2">
                          <CountryIcon className="h-4 w-4 flex-shrink-0" />
                          <div className="flex flex-wrap gap-1 max-w-full overflow-hidden">
                            {selectedLanguage.length === 0 ? (
                              <span className="text-muted-foreground">
                                Select language
                              </span>
                            ) : (
                              <>
                                {selectedLanguage
                                  .slice(0, 2)
                                  .map((lang, index) => (
                                    <Badge
                                      key={index}
                                      variant="outline"
                                      className="flex items-center gap-1 px-2 py-1 rounded-full bg-blue-50 text-blue-700 border-blue-200"
                                    >
                                      <span className="truncate max-w-[45px]">
                                        {lang.charAt(0).toUpperCase() +
                                          lang.slice(1)}
                                      </span>
                                    </Badge>
                                  ))}
                                {selectedLanguage.length > 2 && (
                                  <Badge
                                    variant="outline"
                                    className="px-2 py-1 rounded-full bg-blue-50 text-blue-700 border-blue-200"
                                  >
                                    +{selectedLanguage.length - 2}
                                  </Badge>
                                )}
                              </>
                            )}
                          </div>
                        </div>
                      </SelectTrigger>
                      <SelectContent>
                        {[
                          { value: "english", label: "English" },
                          { value: "spanish", label: "Spanish" },
                          { value: "french", label: "French" },
                        ].map((language, index) => (
                          <SelectItem
                            key={index}
                            value={language.value}
                            className="flex items-center p-0 cursor-default focus:bg-transparent"
                            onSelect={(e) => {
                              e.preventDefault();
                            }}
                          >
                            <div className="flex items-center gap-2 w-full p-2 hover:bg-accent hover:text-accent-foreground rounded-sm">
                              <Checkbox
                                id={`language-${index}`}
                                checked={selectedLanguage.includes(
                                  language.value,
                                )}
                                onCheckedChange={() => {
                                  if (
                                    selectedLanguage.includes(language.value)
                                  ) {
                                    setSelectedLanguage((prev) =>
                                      prev.filter(
                                        (item) => item !== language.value,
                                      ),
                                    );
                                  } else {
                                    setSelectedLanguage((prev) => [
                                      ...prev,
                                      language.value,
                                    ]);
                                  }
                                }}
                              />
                              <label
                                htmlFor={`language-${index}`}
                                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer w-full"
                              >
                                {language.label}
                              </label>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="sm:col-span-2 lg:col-span-1 items-end justify-end flex">
                <Button
                  variant={"primary"}
                  size={"default"}
                  className={`w-full ${!isAnyFilterSelected ? "opacity-50 cursor-not-allowed" : ""}`}
                  disabled={!isAnyFilterSelected}
                  onClick={() => {
                    handleSearchAvailability();
                  }}
                >
                  Search Availability
                </Button>
              </div>
            </div>
          </FadeInSection>
        </div>

        <div className="w-full flex flex-col gap-4">
          <FadeInSection>
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold">
              Categories
            </h1>
            <Carousel className="w-full mt-8">
              <CarouselContent className="-ml-2 md:-ml-4">
                {doctorSpecialties.map(({ name, Icon }, index) => (
                  <CarouselItem key={index} className="pl-2 md:pl-4 basis-auto">
                    <div
                      className="flex-shrink-0 border border-[#E7e8e9] p-3 sm:p-4 rounded-lg hover:bg-[#0052FD08] transition-colors duration-200 cursor-pointer w-[180px]"
                      onClick={() => {
                        navigate("/patient/doctor-listing", {
                          state: {
                            selectedSpecialty: [name],
                          },
                        });
                      }}
                    >
                      <Icon className="text-primary w-8 h-8 sm:w-8 sm:h-8" />
                      <h1 className="mt-2 sm:mt-3 md:mt-4 font-semibold text-sm sm:text-base">
                        {name}
                      </h1>
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="left-0 opacity-70 hover:opacity-100" />
              <CarouselNext className="right-0 opacity-70 hover:opacity-100" />
            </Carousel>
          </FadeInSection>
        </div>

        <div className="w-full flex flex-col gap-4">
          <FadeInSection>
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold">
              Available Doctors
            </h1>

            {isLoading ? (
              <div className="w-full flex justify-center items-center py-12">
                <LoadingSpinner size="lg" />
              </div>
            ) : (
              <>
                <Carousel className="w-full mt-8">
                  <CarouselContent className="-ml-2 md:-ml-4">
                    {availableDoctors.length > 0 ? (
                      availableDoctors.map((doctor, index) => (
                        <CarouselItem
                          key={index}
                          className="pl-2 md:pl-4 basis-auto"
                        >
                          <DoctorCard isexpendable={false} doctor={doctor} />
                        </CarouselItem>
                      ))
                    ) : (
                      <CarouselItem className="pl-2 md:pl-4 w-full">
                        <div className="w-full text-center py-8 text-gray-500">
                          No doctors available at the moment
                        </div>
                      </CarouselItem>
                    )}
                  </CarouselContent>
                  <CarouselPrevious className="left-0 opacity-70 hover:opacity-100" />
                  <CarouselNext className="right-0 opacity-70 hover:opacity-100" />
                </Carousel>
              </>
            )}
          </FadeInSection>
        </div>
      </div>
    </div>
  );
};

export default PatientFindDoctor;
