import React, { useEffect, useState } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../../../components/ui/breadcrumb";
import { useLocation } from "react-router-dom";
import { fetchDoctorsWithFilters_api } from "../../../api/api_calls/doctor_apiCalls";
import { toast } from "../../../hooks/use-toast";
import Doctor<PERSON><PERSON> from "../../../components/doctor-card/DoctorCard";
import { format } from "date-fns";
import LoadingSpinner from "../../../components/animations/LoadingSpinner";

const PatientDoctorListings = () => {
  const location = useLocation();
  const { state } = location;

  const [doctors, setDoctors] = useState([]);
  const [totalDocs, setTotalDocs] = useState();
  const [isLoading, setIsLoading] = useState(false);

  const fetchDoctors = async () => {
    try {
      setIsLoading(true);
      const res = await fetchDoctorsWithFilters_api({
        date: state?.selectedDate || null,
        country: state?.selectedCountry || null,
        language: state?.selectedLanguage || null,
        speciality: state?.selectedSpecialty || null,
      });
      setDoctors(res?.data?.doctors);
      setTotalDocs(res?.data?.total);
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN fetchAvailableDoctors  => ", error);
    } finally {
      setIsLoading(false);
    }
  };

  const joinPhrases = (phrases) => {
    if (phrases?.length === 1) return phrases[0];
    if (phrases?.length === 2) return phrases.join(" and ");
    return (
      phrases.slice(0, -1).join(", ") + " and " + phrases[phrases?.length - 1]
    );
  };

  const generateTagline = () => {
    const {
      selectedSpecialty,
      selectedLanguage,
      selectedCountry,
      selectedDate,
    } = state;

    let tagline = `${totalDocs} Best ${selectedSpecialty} ${totalDocs > 1 ? "Doctors" : "Doctor"}`;

    const phrases = [];
    if (selectedLanguage?.length > 0)
      phrases.push(
        `${totalDocs?.length < 2 ? `speaks ${selectedLanguage}` : `speak ${selectedLanguage}`}`,
      );
    if (selectedCountry?.length > 0)
      phrases.push(
        `${totalDocs?.length < 2 ? `lives in ${selectedCountry}` : `live in ${selectedCountry}`}`,
      );
    if (selectedDate) {
      phrases.push(`are available on ${format(selectedDate, "MMM dd yyyy")}`);
    }

    if (phrases?.length > 0) {
      tagline += " who " + joinPhrases(phrases);
    }

    return tagline;
  };

  const DoctorTagline = () => {
    const tagline = generateTagline();
    return tagline;
  };

  useEffect(() => {
    fetchDoctors();
  }, [state]);

  return (
    <div className="flex items-center justify-center w-full flex-col px-2 sm:px-4">
      <div className="flex items-center justify-center flex-col w-full max-w-7xl p-4 sm:p-6 md:p-8">
        <div className="flex items-start justify-start w-full mb-2 sm:mb-2 max-w-6xl">
          <Breadcrumb>
            <BreadcrumbList className="flex flex-wrap gap-1 sm:gap-2">
              <BreadcrumbItem>
                <BreadcrumbLink href="/" className="text-sm sm:text-base">
                  Home
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="w-4 h-4 sm:w-5 sm:h-5" />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-sm sm:text-base">
                  Search Results
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        <div className="flex items-start justify-start w-full flex-col my-4 sm:my-6 gap-3 sm:gap-4 max-w-6xl">
          <h1 className="font-bold text-xl sm:text-2xl md:text-3xl text-[#1E1E1E] text-start sm:text-left">
            <DoctorTagline />
          </h1>
        </div>
        <div className="flex items-center justify-center w-full flex-col gap-4 sm:gap-6">
          {isLoading ? (
            <div className="w-full flex justify-center items-center py-12">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <div className="flex items-center justify-center w-full flex-col gap-4 sm:gap-6">
              {doctors.length > 0 ? (
                doctors.map((doc, index) => (
                  <DoctorCard isexpendable={true} key={index} doctor={doc} />
                ))
              ) : (
                <div className="text-center py-12">
                  <p className="text-gray-500 text-lg">
                    No doctors match your search criteria.
                  </p>
                  <p className="text-gray-400 mt-2">
                    Try adjusting your filters for more results.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PatientDoctorListings;
