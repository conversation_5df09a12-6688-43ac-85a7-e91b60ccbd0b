import React, { useEffect, useState, useRef, useCallback } from "react";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../../../components/ui/breadcrumb";
import { <PERSON><PERSON> } from "../../../components/ui/button";
import { useLocation, useNavigate } from "react-router-dom";
import { ReactComponent as LocationBlueIcon } from "../../../assets/svgs/LocationBlueIcon.svg";
import { ReactComponent as ExpierienceIcon } from "../../../assets/svgs/ExpierienceIcon.svg";
import { ReactComponent as OfficeBuilding } from "../../../assets/svgs/OfficeBuilding.svg";
import { ReactComponent as ThumbUp } from "../../../assets/svgs/ThumbUp.svg";
import { ReactComponent as AwardIconColor } from "../../../assets/svgs/AwardIconColor.svg";
import { ReactComponent as ShareBlue } from "../../../assets/svgs/ShareBlue.svg";
import { ReactComponent as CalendarIcon } from "../../../assets/svgs/CalendarIcon.svg";
import { ReactComponent as ReviewIcon } from "../../../assets/svgs/review.svg";
import { doctorSpecialties } from "../../../constants/doctorSpecialties";
import { Badge } from "../../../components/ui/badge";
import { Separator } from "../../../components/ui/separator";
import DoctorQualificationComponent from "../../../components/patient-side-components/doctor-qualification-component/DoctorQualificationComponent";
import PracticeExperienceComponent from "../../../components/patient-side-components/practice-experience-component/PracticeExperienceComponent";
import { FaCheck } from "react-icons/fa";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../../../components/ui/accordion-availability";
import { FaChevronRight, FaChevronLeft } from "react-icons/fa";
import { fetchDoctorWithId_api } from "../../../api/api_calls/doctor_apiCalls";
import { toast } from "../../../hooks/use-toast";
import { format, parseISO, parse } from "date-fns";
import { getArrayLanguageLabels } from "../../../helper/language-helper";
import { getSingleCountryLabel } from "../../../helper/country-helper";
import LoadingSpinner from "../../../components/animations/LoadingSpinner";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import { formatTime } from "../../../helpers/dateTimeHelpers";

const PatientDoctorProfile = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { state } = location;

  const [doctor, setDoctor] = useState(null);
  const [awardsStart, setAwardsStart] = useState(0);
  const [isLoading, setIsLoading] = useState(true);

  // Create refs for each section
  const bioRef = useRef(null);
  const qualificationRef = useRef(null);
  const experienceRef = useRef(null);
  const servicesRef = useRef(null);
  const specialtyRef = useRef(null);
  const membershipRef = useRef(null);
  const availabilityRef = useRef(null);
  const awardsRef = useRef(null);
  const reviewRef = useRef(null);

  // Function to scroll to a section
  const scrollToSection = useCallback((sectionRef) => {
    if (sectionRef && sectionRef.current) {
      sectionRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  }, []);

  // Map section names to their refs
  const sectionRefs = {
    "Doctor Bio": bioRef,
    Qualification: qualificationRef,
    Experience: experienceRef,
    Services: servicesRef,
    Specialty: specialtyRef,
    Membership: membershipRef,
    Availability: availabilityRef,
    Awards: awardsRef,
    Review: reviewRef,
  };

  const fetchDoctorProfile = useCallback(async () => {
    if (!state?.doctorId) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const res = await fetchDoctorWithId_api({
        doctorId: state.doctorId,
      });
      setDoctor(res.data);
    } catch (error) {
      toast({
        description: error.message || "Failed to fetch doctor profile",
        variant: "destructive",
      });
      console.error("ERROR IN fetchDoctorProfile => ", error);
    } finally {
      setIsLoading(false);
    }
  }, [state?.doctorId]);

  useEffect(() => {
    fetchDoctorProfile();
  }, [fetchDoctorProfile]);

  // Using imported helper function for time formatting

  // Format date safely
  const formatDate = useCallback((dateString) => {
    try {
      return format(parseISO(dateString), "yyyy");
    } catch (error) {
      console.error("Date formatting error:", error);
      return "N/A";
    }
  }, []);

  // If loading, show a full-page spinner
  if (isLoading) {
    return (
      <div className="w-full min-h-screen flex flex-col items-center justify-center">
        <LoadingSpinner size="xl" className="mb-4" />
        <p className="text-primary font-medium">Loading doctor profile...</p>
      </div>
    );
  }

  return (
    <div className="w-full px-2 sm:px-4 md:px-6 lg:px-8">
      <div className="mx-auto w-full max-w-7xl p-3 sm:p-4 md:p-6 lg:p-8">
        {/* Breadcrumb */}
        <div className="w-full mb-4">
          <Breadcrumb>
            <BreadcrumbList className="flex flex-wrap gap-1 sm:gap-2">
              <BreadcrumbItem>
                <BreadcrumbLink
                  href="/"
                  className="text-xs sm:text-sm lg:text-base"
                >
                  Home
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="w-4 h-4 sm:w-5 sm:h-5" />
              <BreadcrumbItem>
                <BreadcrumbLink
                  href="/"
                  className="text-xs sm:text-sm lg:text-base"
                >
                  Search Results
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="w-4 h-4 sm:w-5 sm:h-5" />
              <BreadcrumbItem>
                <BreadcrumbPage className="text-xs sm:text-sm lg:text-base">
                  Doctor Profile
                </BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>

        {/* Doctor Profile Card */}
        <div className="flex flex-col gap-4 sm:gap-6 w-full">
          <div className="flex border border-solid border-[#E7E8E9] rounded-xl w-full hover:bg-[#0052FD08] flex-col p-3 sm:p-4 md:p-5">
            <div className="flex flex-col gap-4">
              {/* Doctor Info Section */}
              <div className="flex flex-col sm:flex-row items-start gap-4">
                {/* Doctor Image */}
                <div className="w-full sm:w-auto flex justify-center sm:justify-start mb-3 sm:mb-0">
                  <div className="w-20 h-20 sm:w-24 sm:h-24 md:w-32 md:h-32 rounded-2xl overflow-hidden border border-solid border-[#0052FD]">
                    {doctor?.profilePicture?.url ? (
                      <img
                        alt="Doctor"
                        src={doctor.profilePicture.url}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.onerror = null;
                          e.target.src = DefaultAvatar;
                        }}
                      />
                    ) : (
                      <div className="w-full h-full bg-gray-200 flex items-center justify-center">
                        <span className="text-gray-500 text-xs">No Image</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Doctor Details */}
                <div className="flex-1 flex flex-col gap-3 sm:gap-4 ">
                  <div className="flex flex-col sm:flex-row items-center sm:items-start justify-between gap-3">
                    <h1 className="font-semibold text-sm sm:text-base md:text-lg lg:text-xl text-center sm:text-left">
                      {`${doctor?.firstName || ""} ${doctor?.lastName || ""}
                      `.trim() || "Unknown Doctor"}
                    </h1>

                    <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full flex-shrink-0">
                      <ShareBlue className="w-3 h-3 sm:w-4 sm:h-4" />
                    </div>
                  </div>

                  <div className="flex flex-col lg:flex-row items-start justify-between gap-4">
                    {/* Left Column */}
                    <div className="w-full lg:w-7/12 flex flex-col gap-2 sm:gap-3">
                      <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1EB2] text-center sm:text-left">
                        {doctor?.qualifications?.map((qual, idx) => (
                          <span key={qual.id || idx}>
                            {qual.degreeName}
                            {idx < doctor.qualifications.length - 1 ? ", " : ""}
                          </span>
                        ))}
                      </h4>

                      <div className="flex flex-col sm:flex-row items-center sm:items-start gap-2 sm:gap-4 flex-wrap">
                        {doctor?.speciality
                          ?.slice(0, 2)
                          .map((specialty, index) => {
                            const specialtyData = doctorSpecialties.find(
                              (item) => item.name === specialty,
                            );
                            const SpecialtyIcon = specialtyData?.Icon;

                            return (
                              <div
                                key={index}
                                className="flex items-center gap-1 sm:gap-2"
                              >
                                {SpecialtyIcon && (
                                  <SpecialtyIcon className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                                )}
                                <h1 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1EB2]">
                                  {specialty}
                                </h1>
                              </div>
                            );
                          })}
                      </div>

                      <div className="flex flex-col sm:flex-row items-center sm:items-start gap-2 sm:gap-4 flex-wrap">
                        <div className="flex items-center gap-1">
                          <h2 className="font-bold text-xs sm:text-sm md:text-base text-[#1E1E1EB2]">
                            Language:{" "}
                            <span className="font-medium">
                              {getArrayLanguageLabels(doctor?.language) ||
                                "Not specified"}
                            </span>
                          </h2>
                        </div>
                        <div className="flex items-center gap-1 sm:gap-2">
                          <LocationBlueIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                          <h1 className="font-bold text-xs sm:text-sm md:text-base text-[#1E1E1EB2]">
                            {getSingleCountryLabel(
                              doctor?.country?.value || doctor?.country,
                            ) || "Location not specified"}
                          </h1>
                        </div>
                      </div>
                    </div>

                    {/* Right Column */}
                    <div className="w-full lg:w-5/12 flex flex-col gap-2 sm:gap-3 items-start lg:items-start mt-3 sm:mt-0">
                      <div className="flex items-center gap-2 w-full">
                        <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full flex-shrink-0">
                          <ExpierienceIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                        </div>
                        <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1E]">
                          {`${doctor?.yearsOfExperience || 0} Years Experience`}
                        </h4>
                      </div>
                      <div className="flex items-center gap-2 w-full">
                        <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full flex-shrink-0 mt-0.5">
                          <OfficeBuilding className="w-3 h-3 sm:w-4 sm:h-4" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1E] break-words">
                            {doctor?.qualifications?.length > 0
                              ? doctor.qualifications.map((qual, idx) => (
                                  <span key={qual.id || idx}>
                                    {qual.institutionName}
                                    {idx < doctor.qualifications.length - 1
                                      ? ", "
                                      : ""}
                                  </span>
                                ))
                              : "No institution specified"}
                          </h4>
                        </div>
                      </div>

                      <div className="flex items-center gap-2 w-full">
                        <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full flex-shrink-0">
                          <ThumbUp className="w-3 h-3 sm:w-4 sm:h-4" />
                        </div>
                        <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1E]">
                          95% Recommended
                          <span className="text-red-600 font-bold">
                            {" "}
                            (Hard Coded)
                          </span>
                        </h4>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="border-b border-solid border-[#E7E8E9] mx-2 sm:mx-4 my-2" />

              {/* Awards and Book Appointment */}
              <div className="flex flex-col sm:flex-row items-center justify-between gap-4 p-2 sm:p-3">
                <div className="flex items-center flex-wrap gap-4 sm:gap-8">
                  <div className="flex items-center gap-2 mb-3 sm:mb-0">
                    <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full">
                      <CalendarIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                    </div>

                    <h4 className="font-bold text-xs sm:text-sm md:text-base text-[#1E1E1E]">
                      200+ Appointments Booked{" "}
                      <span className="text-red-600 font-bold">
                        {" "}
                        (Hard Coded)
                      </span>
                    </h4>
                  </div>

                  <div className="flex items-center gap-2 mb-3 sm:mb-0">
                    <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full">
                      <AwardIconColor className="w-3 h-3 sm:w-4 sm:h-4" />
                    </div>
                    {doctor?.awards?.length > 0 && (
                      <h4 className="font-bold text-xs sm:text-sm md:text-base text-[#1E1E1E]">
                        {doctor.awards.length > 1
                          ? `${doctor.awards.length - 1}+ awards`
                          : `${doctor.awards.length} award`}
                      </h4>
                    )}
                  </div>

                  <div className="flex items-center gap-2 mb-3 sm:mb-0">
                    <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full">
                      <ReviewIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                    </div>

                    <h4 className="font-bold text-xs sm:text-sm md:text-base text-[#1E1E1E]">
                      5.0{" "}
                      <span className="text-gray-400 font-normal">
                        120 Review{" "}
                        <span className="text-red-600 font-bold">
                          {" "}
                          (Hard Coded)
                        </span>
                      </span>
                    </h4>
                  </div>
                </div>

                <Button
                  variant="primary"
                  className="w-full sm:w-auto"
                  onClick={() =>
                    navigate("/patient/book-appointment", {
                      state: {
                        doctorId: doctor?.id,
                      },
                    })
                  }
                >
                  Book Appointment
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Badges Section */}
        <div className="flex flex-wrap items-center justify-start w-full gap-2 sm:gap-3 p-4 sm:p-6">
          {Object.keys(sectionRefs).map((item) => (
            <Badge
              key={item}
              variant="outline"
              className="text-xs sm:text-sm py-1 px-2 sm:px-3 rounded-lg cursor-pointer hover:bg-[#0052FD14] transition-colors"
              onClick={() => scrollToSection(sectionRefs[item])}
            >
              {item}
            </Badge>
          ))}
        </div>

        {/* Doctor Bio Section */}
        <div ref={bioRef} className="w-full flex flex-col gap-4 scroll-mt-20">
          <div className="border-y border-solid border-[#E7E8E9] py-4">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl">
              Doctor Bio
            </h3>
            <h6 className="font-light text-sm sm:text-base lg:text-lg mt-2">
              {doctor?.bio || "No biography provided"}
            </h6>
          </div>
        </div>

        {/* Qualification Section */}
        <div
          ref={qualificationRef}
          className="w-full flex flex-col gap-4 scroll-mt-20"
        >
          <div className="py-4">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl">
              Qualification
            </h3>
          </div>
          <div className="flex flex-col gap-4 relative">
            {doctor?.qualifications?.length > 0 ? (
              doctor.qualifications.map((qual) => (
                <DoctorQualificationComponent
                  key={qual.id || qual.degreeName}
                  qualification={qual}
                />
              ))
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <p className="text-gray-500">No qualifications available</p>
              </div>
            )}
          </div>
        </div>

        {/* Practice Experience Section */}
        <div
          ref={experienceRef}
          className="w-full flex flex-col gap-4 scroll-mt-20"
        >
          <div className="py-4">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl">
              Practice Experience
            </h3>
          </div>
          <div className="flex flex-col gap-4 relative">
            {doctor?.practiceExperiences?.length > 0 ? (
              doctor.practiceExperiences.map((exp) => (
                <PracticeExperienceComponent
                  key={exp.id || exp.hospitalName}
                  practiceExperience={exp}
                />
              ))
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <p className="text-gray-500">
                  No practice experience available
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Services & Pricing Section */}
        <div
          ref={servicesRef}
          className="w-full flex flex-col gap-4 scroll-mt-20"
        >
          <div className="py-4">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl">
              Services & Pricing{" "}
              <span className="font-normal text-sm sm:text-base lg:text-lg">
                (per session)
              </span>
            </h3>
          </div>
          <div className="flex flex-wrap gap-2 sm:gap-4">
            {doctor?.consultations?.map((clt) => {
              return (
                <Badge
                  key={clt.id || clt.serviceName}
                  variant="outline"
                  className="text-sm sm:text-base lg:text-lg font-semibold py-1 px-4 rounded-lg bg-[#0052FD14] flex gap-2"
                >
                  <span>{clt?.serviceName}</span>{" "}
                  <span>{`$${clt?.price}`}</span>{" "}
                </Badge>
              );
            })}
          </div>
        </div>

        <Separator className="my-6 sm:my-8" />

        {/* Specialty Section */}
        <div
          ref={specialtyRef}
          className="w-full flex flex-col gap-4 scroll-mt-20"
        >
          <div className="py-4">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl">
              Specialty
            </h3>
          </div>
          <div className="flex flex-wrap gap-2 sm:gap-4">
            {doctor?.speciality?.map((spclt, idx) => (
              <Badge
                key={idx}
                variant="outline"
                className="text-sm sm:text-base lg:text-lg font-semibold py-1 px-4 rounded-lg bg-[#0052FD14]"
              >
                {spclt}
              </Badge>
            ))}
          </div>
        </div>

        {/* Membership Section */}
        <div
          ref={membershipRef}
          className="w-full flex flex-col gap-4 mt-4 scroll-mt-20"
        >
          <div className="py-4">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl">
              Membership
            </h3>
          </div>
          <div className="flex flex-col gap-4">
            <div className="flex w-full items-start gap-4 border border-solid border-[#E7E8E9] p-4 rounded-md">
              <div className="w-10 flex items-center justify-center">
                <div className="h-8 w-8 bg-[#34C759] rounded-full flex items-center justify-center">
                  <FaCheck className="text-white w-4 h-4" />
                </div>
              </div>
              <div className="flex-1">
                <h1 className="font-normal text-sm sm:text-base lg:text-lg text-[#1E1E1EB2]">
                  {doctor?.membershipDetails}
                </h1>
              </div>
            </div>
          </div>
        </div>

        {/* Awards and Availability Section */}
        <div className="w-full mt-4 flex flex-col lg:flex-row gap-6">
          {/* Awards Section */}
          <div ref={awardsRef} className="w-full lg:w-1/2 scroll-mt-20">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl mb-4">
              Awards
            </h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 min-h-[300px]">
              {doctor?.awards && doctor.awards.length > 0 ? (
                doctor.awards
                  .slice(awardsStart, awardsStart + 4)
                  .map((award) => (
                    <div
                      key={award?.id}
                      className="flex flex-col border border-[#E7E8E9] p-4 rounded-lg hover:shadow-sm transition-shadow"
                    >
                      <AwardIconColor className="w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8" />
                      <h3 className="font-bold text-[#1E1E1E] text-sm sm:text-base md:text-[15px] mt-2">
                        {`${award?.awardName} (${format(parseISO(award.issueDate), "yyyy")})`}{" "}
                      </h3>
                      <p className="font-normal text-[#1E1E1EB2] text-xs sm:text-sm md:text-[14px] mt-2">
                        {award?.awardDetails}
                      </p>
                    </div>
                  ))
              ) : (
                <div className="col-span-2 flex items-center justify-center h-full border border-dashed border-[#E7E8E9] rounded-lg p-6">
                  <p className="text-[#1E1E1EB2] text-center">
                    No awards to display
                  </p>
                </div>
              )}
            </div>

            {doctor?.awards?.length > 4 && (
              <div className="flex justify-center gap-4 mt-6">
                <button
                  className="h-8 w-8 border border-[#D9D9D9] bg-[#F2F2F7] rounded-full flex items-center justify-center hover:bg-[#0052FD10] transition-colors"
                  disabled={awardsStart === 0}
                  onClick={() => {
                    setAwardsStart((prev) => prev - 4);
                  }}
                >
                  <FaChevronLeft className="text-[#0052FD] text-sm" />
                </button>
                <button
                  className="h-8 w-8 border border-[#D9D9D9] bg-[#F2F2F7] rounded-full flex items-center justify-center hover:bg-[#0052FD10] transition-colors"
                  onClick={() => {
                    setAwardsStart((prev) => prev + 4);
                  }}
                  disabled={awardsStart + 4 >= (doctor?.awards?.length || 0)}
                >
                  <FaChevronRight className="text-[#0052FD] text-sm" />
                </button>
              </div>
            )}
          </div>

          <div ref={availabilityRef} className="w-full lg:w-1/2 scroll-mt-20">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl mb-4">
              Availability
            </h3>

            <div className="border border-[#E7E8E9] rounded-lg overflow-hidden min-h-[300px]">
              <Accordion type="single" collapsible className="w-full">
                {doctor?.weeklySchedules?.length > 0 ? (
                  doctor.weeklySchedules.map((schdl) => (
                    <AccordionItem
                      key={schdl.dayOfWeek.toLowerCase()}
                      value={schdl.dayOfWeek.toLowerCase()}
                    >
                      <AccordionTrigger className="flex justify-between items-center px-4 py-3 hover:no-underline hover:bg-[#F9F9F9]">
                        <span className="font-semibold text-[#1E1E1E] text-base flex-1 text-left">
                          {(schdl.dayOfWeek || "").replace(/^./, (c) =>
                            c.toUpperCase(),
                          )}
                        </span>
                        <span className="font-semibold text-[#1E1E1EB2] text-base mr-2 sm:mr-8">
                          {schdl?.sessions?.length > 0
                            ? "Available"
                            : "Not-Available"}
                        </span>
                      </AccordionTrigger>
                      {schdl?.sessions?.length > 0 && (
                        <AccordionContent className="px-4 pb-4">
                          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                            {schdl?.sessions?.map((slot) => (
                              <div
                                key={slot.id}
                                className="border border-[#E7E8E9] rounded-md p-2 text-center hover:bg-[#F9F9F9]"
                              >
                                <h4 className="font-semibold text-[#1E1E1E] text-[14px] sm:text-[15px]">
                                  {`${format(parse(slot.startTime, "HH:mm:ss", new Date()), "hh:mm aa")} - ${format(parse(slot.endTime, "HH:mm:ss", new Date()), "hh:mm aa")}`}
                                </h4>
                              </div>
                            ))}
                          </div>
                        </AccordionContent>
                      )}
                    </AccordionItem>
                  ))
                ) : (
                  <div className="flex items-center justify-center h-[200px]">
                    <p className="text-[#1E1E1EB2] text-center">
                      No availability information
                    </p>
                  </div>
                )}
              </Accordion>
            </div>
          </div>
        </div>

        {/* Review Section */}
        <div
          ref={reviewRef}
          className="w-full px-2 sm:px-4 md:px-6 mt-4 scroll-mt-20"
        >
          <h3 className="font-bold text-lg sm:text-xl lg:text-2xl mb-4">
            Reviews
          </h3>
          {/* ... review content */}
        </div>
      </div>
    </div>
  );
};

export default PatientDoctorProfile;
