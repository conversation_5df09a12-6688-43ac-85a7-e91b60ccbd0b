import React from "react";
import { useState } from "react";
import PatientInformationDialog from "../../../components/patient-side-components/patient-personal-information-dialog/PatientPersonalInformationDialog";
import PatientInsuranceDialog from "../../../components/patient-side-components/patient-insurance-dialog/PatientInsuranceDialog";
import { logOutUser } from "../../../common/logoutUser";

const PatientCompleteProfile = () => {
  const [isNewPatientDialogOpen, setIsNewPatientDialogOpen] = useState(true);
  const [isInsuranceDialogOpen, setIsInsuranceDialogOpen] = useState(false);

  const handleInsuranceNext = () => {
    setIsInsuranceDialogOpen(false);

    console.log("Insurance information completed");
  };

  const handlePatientInformationCancel = () => {
    logOutUser();
  };

  const handleInsuranceCancel = () => {
    console.log("Sign out functionality will be added later");
    setIsInsuranceDialogOpen(false);
  };

  const handleInsuranceDone = () => {
    setIsInsuranceDialogOpen(false);
  };

  const handlePatientBack = () => {
    setIsNewPatientDialogOpen(false);
    setIsInsuranceDialogOpen(true);
  };

  const handleNewPatientNext = () => {
    setIsNewPatientDialogOpen(false);
    setIsInsuranceDialogOpen(true);
  };

  const handleInsuranceBackButton = () => {
    setIsInsuranceDialogOpen(false);
    setIsNewPatientDialogOpen(true);
  };

  return (
    <div className="fixed inset-0 bg-white flex items-center justify-center z-50">
      <PatientInformationDialog
        isOpen={isNewPatientDialogOpen}
        onClose={handlePatientInformationCancel}
        onNext={handleNewPatientNext}
        onBack={handlePatientBack}
        callingFrom={"patientInformation"}
      />

      <PatientInsuranceDialog
        isOpen={isInsuranceDialogOpen}
        onClose={handleInsuranceCancel}
        onDone={handleInsuranceDone}
        onBack={handleInsuranceBackButton}
        onNext={handleInsuranceNext}
        callingFrom={"patientInformation"}
      />
    </div>
  );
};

export default PatientCompleteProfile;
