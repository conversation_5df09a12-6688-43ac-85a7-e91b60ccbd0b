import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "../../../components/ui/button";
import { useLocation, useNavigate } from "react-router-dom";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../../../components/ui/breadcrumb";
import { ReactComponent as LocationBlueIcon } from "../../../assets/svgs/LocationBlueIcon.svg";
import { ReactComponent as GeneralPhysicianIcon } from "../../../assets/svgs/doctorTypes/GeneralPhysicianIcon.svg";
import { ReactComponent as ExpierienceIcon } from "../../../assets/svgs/ExpierienceIcon.svg";
import { ReactComponent as OfficeBuilding } from "../../../assets/svgs/OfficeBuilding.svg";
import { ReactComponent as AwardIconColor } from "../../../assets/svgs/AwardIconColor.svg";
import { ReactComponent as EntIcon } from "../../../assets/svgs/EntIcon.svg";
import { ReactComponent as CellIconBlue } from "../../../assets/svgs/CellIconBlue.svg";
import { ReactComponent as CameraIconBlue } from "../../../assets/svgs/CameraIconBlue.svg";
import { ReactComponent as ThumbUp } from "../../../assets/svgs/ThumbUp.svg";
import { ReactComponent as ShareBlue } from "../../../assets/svgs/ShareBlue.svg";
import { ReactComponent as CalendarIcon } from "../../../assets/svgs/CalendarIcon.svg";
import { ReactComponent as ReviewIcon } from "../../../assets/svgs/review.svg";
import { Badge } from "../../../components/ui/badge";
import { RadioGroup, RadioGroupItem } from "../../../components/ui/radio-group";
import { Separator } from "../../../components/ui/separator";
import { fetchDoctorWithId_api } from "../../../api/api_calls/doctor_apiCalls";
import { toast } from "../../../hooks/use-toast";
import WeeklyCalendar from "../../../components/patient-side-components/doctor-view-calendar/WeeklyCalendar";
import { getArrayLanguageLabels } from "../../../helper/language-helper";
import { getSingleCountryLabel } from "../../../helper/country-helper";
import { doctorSpecialties } from "../../../constants/doctorSpecialties";

const PatientBookAppointment = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const { state } = location;

  const [doctor, setDoctor] = useState();
  const [selectedDate, setSelectedDate] = useState(null);
  const [selectedIndex, setSelectedIndex] = useState();
  // const [selectedOption, setSelectedOption] = useState("audio");

  const fetchDoctorProfile = async () => {
    try {
      const res = await fetchDoctorWithId_api({
        doctorId: state?.doctorId || null,
      });
      console.log("RES => ", res);
      console.log("=== DOCTOR CONSULTATIONS STRUCTURE ===");
      console.log("Doctor consultations:", res.data?.consultations);
      if (res.data?.consultations?.length > 0) {
        console.log("First consultation structure:", res.data.consultations[0]);
        console.log("First consultation keys:", Object.keys(res.data.consultations[0] || {}));
      }
      console.log("=====================================");
      setDoctor(res.data);
    } catch (error) {
      toast({
        description: error.message,
        variant: "destructive",
      });
      console.log("ERROR IN fetchDoctorProfile  => ", error);
    }
  };

  useEffect(() => {
    fetchDoctorProfile();
  }, [state]);

  useEffect(() => {
    console.log("SELECTED DATE => ", selectedDate);
  }, [selectedDate]);

  const handleBookAppointment = () => {
    if (!selectedDate) {
      toast({ description: "Please select a slot", variant: "destructive" });
      return;
    }

    if (selectedIndex === undefined) {
      toast({ description: "Please select a service", variant: "destructive" });
      return;
    }

    const selectedService = doctor?.consultations?.[selectedIndex];

    if (!selectedService) {
      toast({
        description: "Invalid service selected",
        variant: "destructive",
      });
      return;
    }

    // Console log the selected service to see its structure
    console.log("=== SELECTED SERVICE STRUCTURE ===");
    console.log("Selected Service:", selectedService);
    console.log("Available properties:", Object.keys(selectedService || {}));
    console.log("==================================");

    navigate("/patient/confirm-appointment", {
      state: {
        doctorId: state?.doctorId,
        selectedDate,
        selectedService, // Pass the complete service object
      },
    });
  };

  return (
    <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div className="w-full py-4 sm:py-6 lg:py-8">
        <Breadcrumb>
          <BreadcrumbList className="flex flex-wrap items-center gap-1 sm:gap-2">
            <BreadcrumbItem>
              <BreadcrumbLink
                href="/"
                className="text-xs sm:text-sm md:text-base hover:text-primary"
              >
                Home
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="text-muted-foreground w-3 h-3 sm:w-4 sm:h-4" />
            <BreadcrumbItem>
              <BreadcrumbLink
                href="/"
                className="text-xs sm:text-sm md:text-base hover:text-primary"
              >
                Search Results
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="text-muted-foreground w-3 h-3 sm:w-4 sm:h-4" />
            <BreadcrumbItem>
              <BreadcrumbLink
                href="/"
                className="text-xs sm:text-sm md:text-base hover:text-primary"
              >
                Doctor Profile
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="text-muted-foreground w-3 h-3 sm:w-4 sm:h-4" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-xs sm:text-sm md:text-base font-medium">
                Book Appointment
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="flex flex-col gap-4 sm:gap-6 w-full">
        <div className="flex border border-solid border-[#E7E8E9] rounded-xl w-full hover:bg-[#0052FD08] flex-col p-3 sm:p-4 md:p-5">
          <div className="flex flex-col gap-4">
            {/* Doctor Info Section */}
            <div className="flex flex-col sm:flex-row items-start gap-4">
              {/* Doctor Image */}
              <div className="w-full sm:w-auto flex justify-center sm:justify-start mb-3 sm:mb-0">
                <div className="w-20 h-20 sm:w-24 sm:h-24 md:w-32 md:h-32 rounded-2xl overflow-hidden border border-solid border-[#0052FD]">
                  <img
                    alt="Doctor"
                    src={doctor?.profilePicture?.url}
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>

              {/* Doctor Details */}
              <div className="flex-1 flex flex-col gap-3 sm:gap-4 ">
                <div className="flex flex-col sm:flex-row items-center sm:items-start justify-between gap-3">
                  <h1 className="font-semibold text-sm sm:text-base md:text-lg lg:text-xl text-center sm:text-left">
                    {`${doctor?.firstName || ""} ${doctor?.lastName || ""}
                            `.trim() || "Unknown Doctor"}
                  </h1>

                  <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full flex-shrink-0">
                    <ShareBlue className="w-3 h-3 sm:w-4 sm:h-4" />
                  </div>
                </div>

                <div className="flex flex-col lg:flex-row items-start justify-between gap-4">
                  {/* Left Column */}
                  <div className="w-full lg:w-7/12 flex flex-col gap-2 sm:gap-3">
                    <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1EB2] text-center sm:text-left">
                      {doctor?.qualifications?.map((qual, idx) => {
                        return idx === doctor?.qualifications?.length - 1
                          ? qual.degreeName
                          : `${qual.degreeName}, `;
                      })}
                    </h4>

                    <div className="flex flex-col sm:flex-row items-center sm:items-start gap-2 sm:gap-4 flex-wrap">
                      {doctor?.speciality
                        ?.slice(0, 2)
                        .map((specialty, index) => {
                          const specialtyData = doctorSpecialties.find(
                            (item) => item.name === specialty,
                          );
                          const SpecialtyIcon = specialtyData?.Icon;

                          return (
                            <div
                              key={index}
                              className="flex items-center gap-1 sm:gap-2"
                            >
                              {SpecialtyIcon && (
                                <SpecialtyIcon className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                              )}
                              <h1 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1EB2]">
                                {specialty}
                              </h1>
                            </div>
                          );
                        })}
                    </div>

                    <div className="flex flex-col sm:flex-row items-center sm:items-start gap-2 sm:gap-4 flex-wrap">
                      <div className="flex items-center gap-1">
                        <h2 className="font-bold text-xs sm:text-sm md:text-base text-[#1E1E1EB2]">
                          Language:{" "}
                          <span className="font-medium">
                            {getArrayLanguageLabels(doctor?.language)}
                          </span>
                        </h2>
                      </div>
                      <div className="flex items-center gap-1 sm:gap-2">
                        <LocationBlueIcon className="w-4 h-4 sm:w-5 sm:h-5" />
                        <h1 className="font-bold text-xs sm:text-sm md:text-base text-[#1E1E1EB2]">
                          {getSingleCountryLabel(
                            doctor?.country?.value || doctor?.country,
                          )}
                        </h1>
                      </div>
                    </div>
                  </div>

                  {/* Right Column */}
                  <div className="w-full lg:w-5/12 flex flex-col gap-2 sm:gap-3 items-start lg:items-start mt-3 sm:mt-0">
                    <div className="flex items-center gap-2 w-full">
                      <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full flex-shrink-0">
                        <ExpierienceIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                      </div>
                      <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1E]">
                        {`${doctor?.yearsOfExperience} Years Experience`}
                      </h4>
                    </div>
                    <div className="flex items-center gap-2 w-full">
                      <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full flex-shrink-0 mt-0.5">
                        <OfficeBuilding className="w-3 h-3 sm:w-4 sm:h-4" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1E] break-words">
                          {doctor?.qualifications?.map((qual, idx) => {
                            return idx === doctor?.qualifications?.length - 1
                              ? qual.institutionName
                              : `${qual.institutionName}, `;
                          })}
                        </h4>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 w-full">
                      <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full flex-shrink-0">
                        <ThumbUp className="w-3 h-3 sm:w-4 sm:h-4" />
                      </div>
                      <h4 className="font-medium text-xs sm:text-sm md:text-base text-[#1E1E1E]">
                        95% Recommended
                        <span className="text-red-600 font-bold">
                          {" "}
                          (Hard Coded)
                        </span>
                      </h4>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="border-b border-solid border-[#E7E8E9] mx-2 sm:mx-4 my-2" />

            {/* Awards and Book Appointment */}
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4 p-2 sm:p-3">
              <div className="flex items-center flex-row gap-8">
                <div className="flex items-center gap-2 mb-3 sm:mb-0">
                  <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full">
                    <CalendarIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                  </div>

                  <h4 className="font-bold text-xs sm:text-sm md:text-base text-[#1E1E1E]">
                    200+ Appointments Booked{" "}
                    <span className="text-red-600 font-bold">
                      {" "}
                      (Hard Coded)
                    </span>
                  </h4>
                </div>

                <div className="flex items-center gap-2 mb-3 sm:mb-0">
                  <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full">
                    <AwardIconColor className="w-3 h-3 sm:w-4 sm:h-4" />
                  </div>
                  {doctor?.awards.length > 0 && (
                    <h4 className="font-bold text-xs sm:text-sm md:text-base text-[#1E1E1E]">
                      {doctor?.awards?.length > 1
                        ? `${doctor?.awards?.length - 1}+ awards`
                        : `${doctor?.awards?.length} award`}
                    </h4>
                  )}
                </div>

                <div className="flex items-center gap-2 mb-3 sm:mb-0">
                  <div className="h-7 w-7 sm:h-8 sm:w-8 flex items-center justify-center border border-solid border-[#E7E8E9] rounded-full">
                    <ReviewIcon className="w-3 h-3 sm:w-4 sm:h-4" />
                  </div>

                  <h4 className="font-bold text-xs sm:text-sm md:text-base text-[#1E1E1E]">
                    5.0{" "}
                    <span className="text-gray-400 font-normal">
                      120 Review{" "}
                      <span className="text-red-600 font-bold">
                        {" "}
                        (Hard Coded)
                      </span>
                    </span>
                  </h4>
                </div>
              </div>

              <Button
                variant="primary"
                className="w-full sm:w-auto"
                onClick={() =>
                  navigate("/patient/doctor-profile", {
                    state: {
                      doctorId: state?.doctorId,
                    },
                  })
                }
              >
                View Profile
              </Button>
            </div>
          </div>
        </div>
      </div>

      <Separator className="my-8" />
      <WeeklyCalendar
        doctorId={state?.doctorId}
        minimalMode={true}
        setSelectedDate={setSelectedDate}
        selectedDate={selectedDate}
      />

      <div className="w-full px-4 sm:px-6 lg:px-8 flex flex-col gap-4">
        <div className="py-4">
          <h3 className="font-bold text-lg sm:text-xl lg:text-2xl">
            Services & Pricing{" "}
            <span className="font-normal text-sm sm:text-base lg:text-lg">
              (per session)
            </span>
          </h3>
        </div>
        <div className="flex flex-wrap gap-2 sm:gap-4">
          {doctor?.consultations?.map((consultation, idx) => {
            const isSelected = idx === selectedIndex;
            return (
              <Badge
                key={idx}
                onClick={() => setSelectedIndex(idx)}
                variant="outline"
                className={`cursor-pointer text-sm sm:text-base lg:text-lg font-semibold py-1 px-4 rounded-lg flex gap-2 items-center transition-colors ${
                  isSelected ? "bg-[#0052FD] text-white" : "bg-[#0052FD14]"
                }`}
              >
                <span>{consultation?.serviceName}</span>
                <span>${consultation?.price}</span>
              </Badge>
            );
          })}
        </div>
      </div>

      <Separator className="my-8" />

      {/* <div className="w-full px-4 sm:px-6 lg:px-8 flex flex-col gap-4 ">
        <div className="py-4">
          <h3 className="font-bold text-lg sm:text-xl lg:text-2xl">
            Select consultation method
          </h3>
        </div>

        <RadioGroup
          value={selectedOption}
          onValueChange={setSelectedOption}
          className="flex flex-wrap gap-2 sm:gap-4 items-center"
        >
          <label
            htmlFor="audio"
            className={`flex items-center justify-start border px-4 py-2 gap-4 w-[200px] rounded-md cursor-pointer ${
              selectedOption === "audio"
                ? "border-[#0052FD] bg-[#0052FD14]"
                : "border-[#E7E8E9]"
            }`}
          >
            <RadioGroupItem value="audio" id="audio" />
            <CellIconBlue />
            <span className="font-semibold text-[16px]">Audio</span>
          </label>

          <label
            htmlFor="video"
            className={`flex items-center justify-start border px-4 py-2 gap-4 w-[200px] rounded-md cursor-pointer ${
              selectedOption === "video"
                ? "border-[#0052FD] bg-[#0052FD14]"
                : "border-[#E7E8E9]"
            }`}
          >
            <RadioGroupItem value="video" id="video" />
            <CameraIconBlue />
            <span className="font-semibold text-[16px]">Video</span>
          </label>
        </RadioGroup>
      </div> */}

      <div className="w-full items-center justify-center flex my-6">
        {/* <Button
          variant={"primary"}
          onClick={() => navigate("/patient/confirm-appointment")}
        >
          Book Appointment
        </Button> */}

        <Button
          variant={"primary"}
          // onClick={() =>
          //   navigate("/patient/confirm-appointment", {
          //     state: { selectedDate },
          //   })
          // }
          onClick={handleBookAppointment}
        >
          Book Appointment
        </Button>
      </div>
    </div>
  );
};

export default PatientBookAppointment;
