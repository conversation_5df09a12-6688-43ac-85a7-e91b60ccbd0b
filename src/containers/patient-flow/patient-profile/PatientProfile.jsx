import React from "react";
import { useState } from "react";
import { Edit } from "lucide-react";
import PatientInformationDialog from "../../../components/patient-side-components/patient-personal-information-dialog/PatientPersonalInformationDialog";
import PatientInsuranceDialog from "../../../components//patient-side-components/patient-insurance-dialog/PatientInsuranceDialog";
import ProfileCompletionModal from "../../../components/patient-side-components/patient-complete-profile-modal/PatientCompleteProfileModal";
import FadeInSection from "../../../components/animations/FadeInSection";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import { ReactComponent as PhoneIcon } from "../../../assets/svgs/PhoneIcon.svg";
import { ReactComponent as Globe } from "../../../assets/svgs/GlobeGray.svg";
import { ReactComponent as Location } from "../../../assets/svgs/LocationIcon.svg";
import { useSelector } from "react-redux";
import { getSingleCountryLabel } from "../../../helper/country-helper";

const PatientProfile = () => {
  const [isNewPatientDialogOpen, setIsNewPatientDialogOpen] = useState(false);
  const [isInsuranceDialogOpen, setIsInsuranceDialogOpen] = useState(false);
  const [isCompletionModalOpen, setIsCompletionModalOpen] = useState(false);
  const user = useSelector((state) => state.userReducer.user);

  const patientData = {
    firstName: user?.firstName || "",
    lastName: user?.lastName || "",
    profileImage: user?.profilePicture?.url || DefaultAvatar,
    gender: user?.gender || "",
    dob: user?.dob || "",
    contactNumber: user?.contactNumber || "",
    country: user?.country || "",
    address: user?.address || "",
    medicalConcerns: user?.medicalConcerns || "",
  };

  const insuranceData = {
    planType: user?.insuranceDetails?.planType || "-",
    companyName: user?.insuranceDetails?.companyName || "-",
    companyLocation: user?.insuranceDetails?.companyLocation || "-",
    policyOwner: user?.insuranceDetails?.policyOwner || "-",
    policyNumber: user?.insuranceDetails?.policyNumber || "-",
    patientRegisteredName: user?.insuranceDetails?.patientRegisteredName || "-",
    groupNumber: user?.insuranceDetails?.groupNumber || "-",
    planDetails: user?.insuranceDetails?.planDetails || "-",
    consentForMedInfoRelease: user?.insuranceDetails?.consentForMedInfoRelease
      ? "Yes"
      : "No",
    consentForFinances: user?.insuranceDetails?.consentForFinances
      ? "Yes"
      : "No",
    coPay: user?.insuranceDetails?.coPay || "-",
  };

  const handleNewPatientNext = () => {
    setIsNewPatientDialogOpen(false);
  };

  const handleInsuranceDone = () => {
    setIsInsuranceDialogOpen(false);
  };

  const handleCompletionContinue = () => {
    setIsCompletionModalOpen(false);
  };

  return (
    <div className="container mx-auto p-4 max-w-7xl">
      <FadeInSection delay={0.2}>
        <div className="flex items-center mb-4">
          <h1 className="text-2xl font-bold">Profile</h1>
        </div>
      </FadeInSection>

      <FadeInSection delay={0.4}>
        <div className="bg-white rounded-lg border mb-6">
          <div className="p-4 sm:p-6 flex justify-between items-center">
            <h2 className="text-lg sm:text-xl font-semibold">
              Personal Information
            </h2>
            <button
              onClick={() => setIsNewPatientDialogOpen(true)}
              className="p-2 hover:bg-gray-100 rounded transition-colors"
            >
              <Edit className="h-5 w-5 text-blue-500" />
            </button>
          </div>

          <div className="flex flex-col w-full p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row w-full gap-6">
              <div className="w-full sm:w-2/12 flex items-center justify-center md:items-start md:justify-end">
                <div className="h-24 w-24 sm:h-28 sm:w-28 rounded-full border-2 border-blue-500 overflow-hidden">
                  <img
                    src={patientData?.profileImage}
                    alt={`${patientData.firstName} ${patientData.lastName}`}
                    className="h-full w-full object-cover"
                  />
                </div>
              </div>

              <div className="w-full sm:w-10/12">
                <div className="flex flex-col sm:flex-row sm:gap-8 lg:gap-12">
                  <div className="mb-6 sm:mb-0">
                    <h2 className="text-xl sm:text-2xl font-bold">
                      {`${patientData.firstName} ${patientData.lastName}`}
                    </h2>
                    <div className="mt-2">
                      <p className="text-gray-500 text-sm sm:text-base">
                        Gender:{" "}
                        <span className="text-gray-900 font-medium">
                          {patientData.gender}
                        </span>
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-sm sm:text-base">
                        Date of Birth:{" "}
                        <span className="text-gray-900 font-medium">
                          {patientData.dob}
                        </span>
                      </p>
                    </div>
                  </div>

                  <div>
                    <div className="flex items-center gap-2 mb-3 sm:mb-4">
                      <PhoneIcon className="h-5 w-5 text-gray-500" />
                      <p className="font-medium text-sm sm:text-base">
                        {patientData.contactNumber}
                      </p>
                    </div>

                    <div className="flex items-center gap-2 mb-3 sm:mb-4">
                      <Globe className="h-5 w-5 text-gray-500" />
                      <p className="font-medium text-sm sm:text-base">
                        {getSingleCountryLabel(patientData.country)}
                      </p>
                    </div>

                    <div className="flex items-start gap-2">
                      <Location className="h-5 w-5 text-gray-500" />
                      <p className="font-medium text-sm sm:text-base">
                        {patientData.address}
                      </p>
                    </div>
                  </div>
                </div>

                <div className="mt-6 sm:mt-8">
                  <h3 className="text-gray-700 font-medium text-base sm:text-lg mb-2">
                    Medical Concern
                  </h3>
                  <p className="text-gray-700 text-sm sm:text-base">
                    {patientData.medicalConcerns}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </FadeInSection>

      {console.log("user", user?.insuranceDetails?.insuranceStatus)}

      <FadeInSection delay={0.6}>
        <div className="bg-white rounded-lg border mb-6">
          <div className="p-4 flex justify-between items-center">
            <div className="flex flex-row">
              <h2 className="text-lg font-semibold">Insurance Detail</h2>
              <div className="flex items-center mb-4">
                {user?.insuranceDetails?.insuranceStatus === "pending" ? (
                  <span className="ml-4 bg-amber-100 text-amber-800 text-xs px-2 py-1 rounded-full">
                    Pending
                  </span>
                ) : (
                  <span className="ml-4 bg-[#00c950] text-white  text-xs px-2 py-1 rounded-full">
                    Verified
                  </span>
                )}
              </div>
            </div>

            <button
              onClick={() => setIsInsuranceDialogOpen(true)}
              className="p-2 hover:bg-gray-100 rounded transition-colors"
            >
              <Edit className="h-5 w-5 text-blue-500" />
            </button>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <p className="text-gray-500">Insurance Plan Type</p>
                <p className="font-medium">{insuranceData?.planType}</p>
              </div>

              <div>
                <p className="text-gray-500">Insurance Company Name</p>
                <p className="font-medium">{insuranceData?.companyName}</p>
              </div>

              <div>
                <p className="text-gray-500">Location</p>
                <p className="font-medium">{insuranceData?.companyLocation}</p>
              </div>

              <div>
                <p className="text-gray-500">Policy Owner Name</p>
                <p className="font-medium">{insuranceData?.policyOwner}</p>
              </div>

              <div>
                <p className="text-gray-500">Policy Number</p>
                <p className="font-medium">{insuranceData?.policyNumber}</p>
              </div>

              <div>
                <p className="text-gray-500">Patient Name</p>
                <p className="font-medium">
                  {insuranceData?.patientRegisteredName}
                </p>
              </div>

              <div>
                <p className="text-gray-500">Group Number</p>
                <p className="font-medium">{insuranceData?.groupNumber}</p>
              </div>

              <div>
                <p className="text-gray-500">Co-pay</p>
                <p className="font-medium">{insuranceData?.coPay}</p>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-gray-500">Plan</p>
              <p className="text-gray-700 mt-1">{insuranceData?.planDetails}</p>
            </div>

            <div>
              <p className="text-gray-700 font-medium">
                Authorization and Consent:{" "}
                {/* <span className="font-normal text-gray-500 text-sm">
                  (Please read consent question below to the patient)
                </span> */}
              </p>

              <div className="mt-4">
                <p className="text-gray-600">
                  Do you authorize the release of any medical information
                  necessary to process insurance claims?
                </p>
                <p className="font-medium">
                  {insuranceData?.consentForMedInfoRelease}
                </p>
              </div>

              <div className="mt-4">
                <p className="text-gray-600">
                  Do you understand that you will be financially responsible for
                  all charges not covered by insurance?
                </p>
                <p className="font-medium">
                  {insuranceData?.consentForFinances}
                </p>
              </div>
            </div>
          </div>
        </div>
      </FadeInSection>

      <PatientInformationDialog
        isOpen={isNewPatientDialogOpen}
        onClose={() => setIsNewPatientDialogOpen(false)}
        onNext={handleNewPatientNext}
      />

      <PatientInsuranceDialog
        isOpen={isInsuranceDialogOpen}
        onClose={() => setIsInsuranceDialogOpen(false)}
        onDone={handleInsuranceDone}
      />

      <ProfileCompletionModal
        isOpen={isCompletionModalOpen}
        onClose={() => setIsCompletionModalOpen(false)}
        onContinue={handleCompletionContinue}
      />
    </div>
  );
};

export default PatientProfile;
