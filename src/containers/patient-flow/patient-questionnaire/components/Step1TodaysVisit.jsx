"use client";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../../components/ui/form";
import { Input } from "../../../../components/ui/input";

import { Checkbox } from "../../../../components/ui/checkbox";
import ToggleRadioGroup from "./ToggleRadioGroup";

const Step1TodaysVisit = ({ form, firstInputRef, isSubmitting }) => {
  return (
    <div className="space-y-6">
      <div>
        {/* <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
          Today's Visit
        </h3> */}
        <div className="space-y-4 mb-6">
          <FormField
            control={form.control}
            name="isMedCertificateRequested"
            render={({ field }) => (
              <FormItem className="flex flex-row items-start space-x-3 space-y-0  p-4">
                <FormControl>
                  <Checkbox
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    disabled={isSubmitting}
                  />
                </FormControl>
                <div className="space-y-1 leading-none">
                  <FormLabel>Request Medical Certificate</FormLabel>
                </div>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="goalForHealth"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel htmlFor="goalForHealth" className="font-medium">
                  What are you hoping to accomplish today? *
                </FormLabel>
                <FormControl>
                  <Input
                    id="goalForHealth"
                    placeholder="Enter your health goals for today"
                    {...field}
                    ref={firstInputRef}
                    className={`transition-all ${
                      fieldState.error
                        ? "border-destructive focus:ring-destructive/50"
                        : "focus:ring-primary/50 focus:border-primary"
                    } focus:ring-2`}
                    disabled={isSubmitting}
                  />
                </FormControl>
                {fieldState.error && (
                  <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                    {fieldState.error.message}
                  </FormMessage>
                )}
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="agendaForAppointment"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel
                  htmlFor="agendaForAppointment"
                  className="font-medium"
                >
                  Is there anything else you&apos;d like to work on to improve
                  your health?
                </FormLabel>
                <FormControl>
                  <Input
                    id="agendaForAppointment"
                    placeholder="Enter additional health concerns"
                    {...field}
                    className={`transition-all ${
                      fieldState.error
                        ? "border-destructive focus:ring-destructive/50"
                        : "focus:ring-primary/50 focus:border-primary"
                    } focus:ring-2`}
                    disabled={isSubmitting}
                  />
                </FormControl>
                {fieldState.error && (
                  <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                    {fieldState.error.message}
                  </FormMessage>
                )}
              </FormItem>
            )}
          />
        </div>
      </div>

      <div>
        {/* <h3 className="text-lg font-medium mb-4 bg-[#0052FD10] p-2">
          Condition-Specific Information
        </h3> */}
        <p className="font-medium mb-4">
          If you have one of the following conditions, please answer:
        </p>

        <div className="mb-6  p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-3">Diabetes:</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="diabetes_isProblemWithMedication"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Any problems with medications?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="diabetes_homeGlucoseReadings"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Home glucose readings
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter value"
                      {...field}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="mb-6  p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-3">High blood pressure</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="highBloodPressure_isProblemWithMedication"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Any problems with medications?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="highBloodPressure_homeBpReadings"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Home BP readings
                  </FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter value"
                      {...field}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="mb-6  p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-3">Depression</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="depression_isProblemWithMedication"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Any problems with medications?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="depression_isSuicidalThoughts"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Any suicidal thoughts?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="mb-6  p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-3">High cholesterol</h3>
          <div className="grid md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="highCholesterol_isProblemWithMedication"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Any problems with medications?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step1TodaysVisit;
