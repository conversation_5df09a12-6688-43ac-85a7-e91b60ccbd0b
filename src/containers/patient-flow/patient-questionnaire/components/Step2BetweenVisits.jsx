"use client";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../../components/ui/form";
import { Textarea } from "../../../../components/ui/textarea";
import ToggleRadioGroup from "./ToggleRadioGroup";

const Step2BetweenVisits = ({ form, firstInputRef, isSubmitting }) => {
  return (
    <div className="space-y-6">
      <div>
        <div className="space-y-4 mb-6  p-4 rounded-lg">
          <FormField
            control={form.control}
            name="otherMedVisit"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium">
                  Have you been to the ER, hospital, or another doctor since
                  last seen here?
                </FormLabel>
                <FormControl>
                  <ToggleRadioGroup
                    value={
                      field.value === true
                        ? "yes"
                        : field.value === false
                          ? "no"
                          : null
                    }
                    onValueChange={(value) => {
                      if (value === "yes") field.onChange(true);
                      else if (value === "no") field.onChange(false);
                      else field.onChange(null);
                    }}
                    disabled={isSubmitting}
                    className="flex space-x-4"
                  >
                    <div value="yes">Yes</div>
                    <div value="no">No</div>
                  </ToggleRadioGroup>
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="otherMedVisit_details"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel
                  htmlFor="otherMedVisitDetails"
                  className="font-medium"
                >
                  Please explain:
                </FormLabel>
                <FormControl>
                  <Textarea
                    id="otherMedVisitDetails"
                    placeholder="Provide details about your visit"
                    className={`min-h-[150px] transition-all ${
                      fieldState.error
                        ? "border-destructive focus:ring-destructive/50"
                        : "focus:ring-primary/50 focus:border-primary"
                    } focus:ring-2`}
                    {...field}
                    ref={firstInputRef}
                    disabled={isSubmitting}
                  />
                </FormControl>
                {fieldState.error && (
                  <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                    {fieldState.error.message}
                  </FormMessage>
                )}
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default Step2BetweenVisits;
