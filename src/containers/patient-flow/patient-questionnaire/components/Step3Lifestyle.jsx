import React from "react";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../../components/ui/form";
import { Input } from "../../../../components/ui/input";
import ToggleRadioGroup from "./ToggleRadioGroup";

const Step3Lifestyle = ({ form, firstInputRef, isSubmitting }) => {
  return (
    <div className="space-y-8">
      {/* Exercise Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">Exercise</h3>
        <div className="space-y-4 ">
          <FormField
            control={form.control}
            name="exercice"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel htmlFor="exercice">What do you do?</FormLabel>
                <FormControl>
                  <Input
                    id="exercice"
                    placeholder="Enter value"
                    {...field}
                    ref={firstInputRef}
                    className={`transition-all ${
                      fieldState.error
                        ? "border-destructive focus:ring-destructive/50"
                        : "focus:ring-primary/50 focus:border-primary"
                    } focus:ring-2`}
                    disabled={isSubmitting}
                  />
                </FormControl>
                {fieldState.error && (
                  <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                    {fieldState.error.message}
                  </FormMessage>
                )}
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="exercice_howLong"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="exercice-how-long">How long?</FormLabel>
                  <FormControl>
                    <Input
                      id="exercice-how-long"
                      placeholder="Enter value"
                      {...field}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="exercice_howOften"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="exercice-how-often">How Often?</FormLabel>
                  <FormControl>
                    <Input
                      id="exercice-how-often"
                      placeholder="Enter value"
                      {...field}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="isShortnessOfBreath"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium">
                  Can you walk a block or climb a flight of stairs without
                  getting short of breath?
                </FormLabel>
                <FormControl>
                  <ToggleRadioGroup
                    value={
                      field.value === true
                        ? "yes"
                        : field.value === false
                          ? "no"
                          : null
                    }
                    onValueChange={(value) => {
                      if (value === "yes") field.onChange(true);
                      else if (value === "no") field.onChange(false);
                      else field.onChange(null);
                    }}
                    disabled={isSubmitting}
                    className="flex space-x-4"
                  >
                    <div value="yes">Yes</div>
                    <div value="no">No</div>
                  </ToggleRadioGroup>
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Smoking Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">Smoking</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 border-solid border-[1px] border-[#E7E8E9] p-4 rounded-lg">
          <FormField
            control={form.control}
            name="smoking_isInterestedInQuitting"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium">
                  Are you interested in quitting?
                </FormLabel>
                <FormControl>
                  <ToggleRadioGroup
                    value={
                      field.value === true
                        ? "yes"
                        : field.value === false
                          ? "no"
                          : null
                    }
                    onValueChange={(value) => {
                      if (value === "yes") field.onChange(true);
                      else if (value === "no") field.onChange(false);
                      else field.onChange(null);
                    }}
                    disabled={isSubmitting}
                    className="flex space-x-4"
                  >
                    <div value="yes">Yes</div>
                    <div value="no">No</div>
                  </ToggleRadioGroup>
                </FormControl>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="smoking_howMuch"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel htmlFor="smoking-how-much">
                  How much do you smoke?
                </FormLabel>
                <FormControl>
                  <Input
                    id="smoking-how-much"
                    placeholder="Enter value"
                    {...field}
                    className={`transition-all ${
                      fieldState.error
                        ? "border-destructive focus:ring-destructive/50"
                        : "focus:ring-primary/50 focus:border-primary"
                    } focus:ring-2`}
                    disabled={isSubmitting}
                  />
                </FormControl>
                {fieldState.error && (
                  <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                    {fieldState.error.message}
                  </FormMessage>
                )}
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Alcohol Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">Alcohol</h3>
        <div className="space-y-4 ">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="alcohol_numberOfDrinks_week"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="alcohol-drinks-week">
                    How many drinking days do you have per week?
                  </FormLabel>
                  <FormControl>
                    <Input
                      id="alcohol-drinks-week"
                      placeholder="Enter value"
                      {...field}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="alcohol_numberOfDrinks_day"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="alcohol-drinks-day">
                    On average how many drinks per drinking day?
                  </FormLabel>
                  <FormControl>
                    <Input
                      id="alcohol-drinks-day"
                      placeholder="Enter value"
                      {...field}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="alcohol_moreThanFourDrinks"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Have you had more than 4 drinks in a day in the past 3
                    months?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="alcohol_othersConcerned"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Are you or others concerned about your drinking?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>

      {/* Falls Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">Falls</h3>
        <div className="space-y-4 ">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="fallen"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Have you fallen in the past year?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="balanceProblems"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Do you have problems with walking or balance?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>

      {/* Caffeine Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">Caffeine:</h3>
        <div className="space-y-4 ">
          <FormField
            control={form.control}
            name="caffeinePerDay"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel htmlFor="caffeine-per-day">
                  How much caffeine do you consume per day? (e.g. coffee, tea,
                  chocolate, soda)
                </FormLabel>
                <FormControl>
                  <Input
                    id="caffeine-per-day"
                    placeholder="Enter value"
                    {...field}
                    className={`transition-all ${
                      fieldState.error
                        ? "border-destructive focus:ring-destructive/50"
                        : "focus:ring-primary/50 focus:border-primary"
                    } focus:ring-2`}
                    disabled={isSubmitting}
                  />
                </FormControl>
                {fieldState.error && (
                  <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                    {fieldState.error.message}
                  </FormMessage>
                )}
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Birth Control Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">Birth control:</h3>
        <div className="space-y-4 ">
          <FormField
            control={form.control}
            name="birthControlMethod"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel htmlFor="birth-control-method">
                  Birth control method (if applicable)
                </FormLabel>
                <FormControl>
                  <Input
                    id="birth-control-method"
                    placeholder="Enter value"
                    {...field}
                    className={`transition-all ${
                      fieldState.error
                        ? "border-destructive focus:ring-destructive/50"
                        : "focus:ring-primary/50 focus:border-primary"
                    } focus:ring-2`}
                    disabled={isSubmitting}
                  />
                </FormControl>
                {fieldState.error && (
                  <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                    {fieldState.error.message}
                  </FormMessage>
                )}
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Safety Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">Safety:</h3>
        <div className="space-y-4 ">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="isUnsafeRelationship"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Are you in a relationship where you feel unsafe or have been
                    hurt?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="isWearSeatbelt"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Do you regularly wear a seatbelt?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>

      {/* HIV Testing Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">HIV testing:</h3>
        <div className="space-y-4 ">
          <FormField
            control={form.control}
            name="isHivTestRequested"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium">
                  Would you like HIV testing?
                </FormLabel>
                <FormControl>
                  <ToggleRadioGroup
                    value={
                      field.value === true
                        ? "yes"
                        : field.value === false
                          ? "no"
                          : null
                    }
                    onValueChange={(value) => {
                      if (value === "yes") field.onChange(true);
                      else if (value === "no") field.onChange(false);
                      else field.onChange(null);
                    }}
                    disabled={isSubmitting}
                    className="flex space-x-4"
                  >
                    <div value="yes">Yes</div>
                    <div value="no">No</div>
                  </ToggleRadioGroup>
                </FormControl>
                <p className="text-xs mt-2">
                  (If yes, please tell the nurse.) HIV testing is recommended
                  for anyone at risk for HIV infections, including persons with
                  a sexually transmitted disease or history of injection drug
                  use, sex workers, sexual partners of HIV-infected persons, or
                  persons at risk.
                </p>
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Sleep Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">Sleep:</h3>
        <div className="space-y-4 ">
          <FormField
            control={form.control}
            name="isSleepApnea"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium">
                  Do you stop breathing during sleep or have concerns about
                  sleep apnea?
                </FormLabel>
                <FormControl>
                  <ToggleRadioGroup
                    value={
                      field.value === true
                        ? "yes"
                        : field.value === false
                          ? "no"
                          : null
                    }
                    onValueChange={(value) => {
                      if (value === "yes") field.onChange(true);
                      else if (value === "no") field.onChange(false);
                      else field.onChange(null);
                    }}
                    disabled={isSubmitting}
                    className="flex space-x-4"
                  >
                    <div value="yes">Yes</div>
                    <div value="no">No</div>
                  </ToggleRadioGroup>
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Depression Screen Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">Depression screen:</h3>
        <div className="space-y-4 ">
          <FormField
            control={form.control}
            name="isFeelingDown"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium">
                  Over the last 2 weeks have you been bothered by little
                  interest or pleasure in doing things, or feeling down,
                  hopeless, or depressed?
                </FormLabel>
                <FormControl>
                  <ToggleRadioGroup
                    value={
                      field.value === true
                        ? "yes"
                        : field.value === false
                          ? "no"
                          : null
                    }
                    onValueChange={(value) => {
                      if (value === "yes") field.onChange(true);
                      else if (value === "no") field.onChange(false);
                      else field.onChange(null);
                    }}
                    disabled={isSubmitting}
                    className="flex space-x-4"
                  >
                    <div value="yes">Yes</div>
                    <div value="no">No</div>
                  </ToggleRadioGroup>
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Medication Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">Medication:</h3>
        <div className="space-y-4 ">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="isProblemWithMedication"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">
                    Do you have any trouble taking any of your medications?
                  </FormLabel>
                  <FormControl>
                    <ToggleRadioGroup
                      value={
                        field.value === true
                          ? "yes"
                          : field.value === false
                            ? "no"
                            : null
                      }
                      onValueChange={(value) => {
                        if (value === "yes") field.onChange(true);
                        else if (value === "no") field.onChange(false);
                        else field.onChange(null);
                      }}
                      disabled={isSubmitting}
                      className="flex space-x-4"
                    >
                      <div value="yes">Yes</div>
                      <div value="no">No</div>
                    </ToggleRadioGroup>
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="problemWithMedicationDetails"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="medication-trouble-details">
                    If so, what sort of trouble?
                  </FormLabel>
                  <FormControl>
                    <Input
                      id="medication-trouble-details"
                      placeholder="Enter value"
                      {...field}
                      className={`transition-all ${
                        fieldState.error
                          ? "border-destructive focus:ring-destructive/50"
                          : "focus:ring-primary/50 focus:border-primary"
                      } focus:ring-2`}
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>
        </div>
      </div>

      {/* Bladder Control Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">Bladder Control:</h3>
        <div className="space-y-4 ">
          <FormField
            control={form.control}
            name="isProblemBladderControl"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium">
                  Do you lose control of your urine to the point you would like
                  to know how to treat it?
                </FormLabel>
                <FormControl>
                  <ToggleRadioGroup
                    value={
                      field.value === true
                        ? "yes"
                        : field.value === false
                          ? "no"
                          : null
                    }
                    onValueChange={(value) => {
                      if (value === "yes") field.onChange(true);
                      else if (value === "no") field.onChange(false);
                      else field.onChange(null);
                    }}
                    disabled={isSubmitting}
                    className="flex space-x-4"
                  >
                    <div value="yes">Yes</div>
                    <div value="no">No</div>
                  </ToggleRadioGroup>
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* End-of-life Care Section */}
      <div>
        <h3 className="text-lg font-medium mb-4">End-of-life care:</h3>
        <div className="space-y-4 ">
          <FormField
            control={form.control}
            name="discussEndOfLifeCare"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="font-medium">
                  Do you want to discuss end-of-life issues?
                </FormLabel>
                <FormControl>
                  <ToggleRadioGroup
                    value={
                      field.value === true
                        ? "yes"
                        : field.value === false
                          ? "no"
                          : null
                    }
                    onValueChange={(value) => {
                      if (value === "yes") field.onChange(true);
                      else if (value === "no") field.onChange(false);
                      else field.onChange(null);
                    }}
                    disabled={isSubmitting}
                    className="flex space-x-4"
                  >
                    <div value="yes">Yes</div>
                    <div value="no">No</div>
                  </ToggleRadioGroup>
                </FormControl>
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
};

export default Step3Lifestyle;
