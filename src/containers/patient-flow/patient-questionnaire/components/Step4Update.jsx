import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../../components/ui/form";
import { Input } from "../../../../components/ui/input";
import { Checkbox } from "../../../../components/ui/checkbox";
import { Label } from "../../../../components/ui/label";
import { Textarea } from "../../../../components/ui/textarea";
import { formatSymptomName } from "../../../../helpers/symptomHelpers";

const Step4Update = ({ form, firstInputRef, isSubmitting }) => {
  const symptomCategories = [
    {
      title: "Constitutional symptoms",
      field: "constitutionalSymptoms",
      symptoms: ["fever", "weightLoss", "extremeFatigue"],
    },
    {
      title: "Eyes",
      field: "eyeProblems",
      symptoms: ["doubleVision", "suddenLossOfVision"],
    },
    {
      title: "Ears, nose, mouth, and throat",
      field: "enmtProblems",
      symptoms: ["soreThroat", "runnyNose", "earPain"],
    },
    {
      title: "Cardiovascular",
      field: "cardiovascularProblems",
      symptoms: ["chestPain", "palpitations"],
    },
    {
      title: "Respiratory",
      field: "respiratoryProblems",
      symptoms: ["cough", "wheezing", "shotnessOfBreath"],
    },
    {
      title: "Gastrointestinal",
      field: "gastrointestinalProblems",
      symptoms: [
        "nausea",
        "vomiting",
        "abdominalPain",
        "constipation",
        "diarrhea",
        "bloodInStools",
      ],
    },
    {
      title: "Genitourinary",
      field: "genitourinaryProblems",
      symptoms: [
        "irregularMenses",
        "vaginalBleeding",
        "abdominalPain",
        "constipation",
        "diarrhea",
      ],
    },
    {
      title: "Skin",
      field: "skinProblems",
      symptoms: ["rash", "changingMole"],
    },
    {
      title: "Sleep",
      field: "sleepProblems",
      symptoms: ["snoring", "difficultySleeping"],
    },
    {
      title: "Neurological",
      field: "neurologicalProblems",
      symptoms: ["headache", "falling", "numbnessOneSide"],
    },
    {
      title: "Musculoskeletal",
      field: "musculoskeletalProblems",
      symptoms: ["jointPain", "muscleWeakness", "psychiatric"],
    },
    {
      title: "Psychiatric",
      field: "psychiatricProblems",
      symptoms: ["depression", "anxiety", "suicidalThoughts"],
    },
    {
      title: "Endocrine",
      field: "endocrineProblems",
      symptoms: ["excessiveThirst", "coldHeatTolerance", "breastMass"],
    },
    {
      title: "Hematologic",
      field: "hematologicProblems",
      symptoms: ["unusualBruisingBleeding", "enlargedLymphNodes"],
    },
    {
      title: "Allergic",
      field: "allergicProblems",
      symptoms: ["highFever"],
    },
  ];

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
          <FormField
            control={form.control}
            name="newIllnessInFamily"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel className="font-medium">
                  Has anything new come up in your family history? (new illness
                  among blood relatives)
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter value"
                    {...field}
                    ref={firstInputRef}
                    className={`transition-all ${
                      fieldState.error
                        ? "border-destructive focus:ring-destructive/50"
                        : "focus:ring-primary/50 focus:border-primary"
                    } focus:ring-2`}
                    disabled={isSubmitting}
                  />
                </FormControl>
                {fieldState.error && (
                  <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                    {fieldState.error.message}
                  </FormMessage>
                )}
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="newDrugAllergies"
            render={({ field, fieldState }) => (
              <FormItem>
                <FormLabel className="font-medium">
                  Have you developed any new drug allergies?
                </FormLabel>
                <FormControl>
                  <Input
                    placeholder="Enter value"
                    {...field}
                    className={`transition-all ${
                      fieldState.error
                        ? "border-destructive focus:ring-destructive/50"
                        : "focus:ring-primary/50 focus:border-primary"
                    } focus:ring-2`}
                    disabled={isSubmitting}
                  />
                </FormControl>
                {fieldState.error && (
                  <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                    {fieldState.error.message}
                  </FormMessage>
                )}
              </FormItem>
            )}
          />
        </div>
      </div>

      <div>
        <p className="font-medium mb-4">
          Are you experiencing any of the following?
        </p>

        {/* Render each symptom category separately */}
        {symptomCategories.map((category) => (
          <FormField
            key={category.field}
            control={form.control}
            name={category.field}
            render={({ field }) => (
              <div className="mb-6 rounded-lg">
                <h3 className="font-semibold mb-4">{category.title}:</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
                  {category.symptoms.map((symptom) => (
                    <div
                      className="flex items-center space-x-2 p-2"
                      key={symptom}
                    >
                      <Checkbox
                        id={`${category.field}-${symptom}`}
                        checked={field.value?.includes(symptom)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            field.onChange([...(field.value || []), symptom]);
                          } else {
                            field.onChange(
                              field.value?.filter((val) => val !== symptom) ||
                                [],
                            );
                          }
                        }}
                        disabled={isSubmitting}
                      />
                      <Label htmlFor={`${category.field}-${symptom}`}>
                        {formatSymptomName(symptom)}
                      </Label>
                    </div>
                  ))}
                </div>
              </div>
            )}
          />
        ))}
      </div>

      <div>
        <FormField
          control={form.control}
          name="specificIssues"
          render={({ field, fieldState }) => (
            <FormItem>
              <FormLabel htmlFor="specific-issues" className="font-medium">
                Please identify any issues above which are new or that you
                specifically want to address.
              </FormLabel>
              <FormControl>
                <Textarea
                  id="specific-issues"
                  placeholder="Enter value"
                  className={`min-h-[100px] transition-all ${
                    fieldState.error
                      ? "border-destructive focus:ring-destructive/50"
                      : "focus:ring-primary/50 focus:border-primary"
                  } focus:ring-2`}
                  {...field}
                  disabled={isSubmitting}
                />
              </FormControl>
              {fieldState.error && (
                <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                  {fieldState.error.message}
                </FormMessage>
              )}
            </FormItem>
          )}
        />
      </div>

      <div className="text-sm space-y-4 bg-gray-50 p-4 rounded-lg">
        <p>
          If you need help between appointments, please call our office at{" "}
          <span className="font-medium">+111 222 333 44</span>
        </p>
        <p>
          Our goal is to see you the day you call in or the next day. It is
          helpful if you call first thing in the morning. One of our nurses will
          help you decide if you need to be seen and if any tests are needed
          prior to your appointment.
        </p>
      </div>
    </div>
  );
};

export default Step4Update;
