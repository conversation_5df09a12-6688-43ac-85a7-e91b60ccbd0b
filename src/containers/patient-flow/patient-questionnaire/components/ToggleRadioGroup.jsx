import { Label } from "../../../../components/ui/label";

// Custom Toggle Radio Group Component
const ToggleRadioGroup = ({
  value,
  onValueChange,
  disabled,
  children,
  className,
}) => {
  const handleValueChange = (newValue) => {
    // If the same value is clicked again, set to null (unselect)
    if (value === newValue) {
      onValueChange(null);
    } else {
      onValueChange(newValue);
    }
  };

  return (
    <div className={className}>
      {children.map((child, index) => {
        const childValue = child.props.value;
        const isSelected = value === childValue;

        return (
          <div
            key={index}
            className={`flex items-center space-x-2 border rounded-md px-4 py-2 cursor-pointer transition-colors ${
              isSelected ? "bg-blue-50 border-blue-300" : "hover:bg-gray-50"
            } ${disabled ? "opacity-50 cursor-not-allowed" : ""}`}
            onClick={() => !disabled && handleValueChange(childValue)}
          >
            <div
              className={`w-4 h-4 rounded-full border-2 flex items-center justify-center ${
                isSelected ? "border-blue-600" : "border-gray-300"
              }`}
            >
              {isSelected && (
                <div className="w-2 h-2 rounded-full bg-blue-600"></div>
              )}
            </div>
            <Label
              className={`cursor-pointer ${disabled ? "cursor-not-allowed" : ""}`}
            >
              {child.props.children}
            </Label>
          </div>
        );
      })}
    </div>
  );
};

export default ToggleRadioGroup;
