import React, { useState, useEffect, useCallback } from "react";
import {
  <PERSON><PERSON><PERSON>rumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "../../../components/ui/breadcrumb";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
} from "../../../components/ui/card";
import { Label } from "../../../components/ui/label";
import { Input } from "../../../components/ui/input";
import { Separator } from "../../../components/ui/separator";
import { RadioGroup, RadioGroupItem } from "../../../components/ui/radio-group";
import { Button } from "../../../components/ui/button";
import { ReactComponent as CalendarIconWhite } from "../../../assets/svgs/CalendarIconWhite.svg";
import { ReactComponent as CalendarIcon } from "../../../assets/svgs/CalendarIcon.svg";
import { ReactComponent as ClockIconBlue } from "../../../assets/svgs/ClockIconBlue.svg";
import { ReactComponent as StripeLogo } from "../../../assets/svgs/StripeLogo.svg";
import { ReactComponent as InfoIconBlue } from "../../../assets/svgs/InfoIconBlue.svg";
import { ReactComponent as PayIconWhite } from "../../../assets/svgs/PayIconWhite.svg";
import SendGif from "../../../assets/images/PendingCenterItem.gif";
import GreenCheckCircle from "../../../assets/images/Check.png";
import { useNavigate, useLocation } from "react-router-dom";
import { format, isValid } from "date-fns";
import { fetchDoctorWithId_api } from "../../../api/api_calls/doctor_apiCalls";
import { bookAppointment_apiCalls } from "../../../api/api_calls/appointment_apiCalls";
import { toast } from "../../../hooks/use-toast";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import { useSelector } from "react-redux";
import LoadingSpinner from "../../../components/animations/LoadingSpinner";

const PatientConfirmBooking = () => {
  const navigate = useNavigate();
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showPaymentDoneModal, setShowPaymentDoneModal] = useState(false);
  const { state } = useLocation();
  const [doctor, setDoctor] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const user = useSelector((state) => state.userReducer.user);
  // Navigation handler - memoized
  const handleNavigation = useCallback(
    (appointmentId) => {
      navigate("/patient/confirm-questionnaire", {
        state: { appointmentId },
      });
    },
    [navigate],
  );

  // Modal handlers - memoized
  const handleConfirmBooking = useCallback(() => {
    setShowDetailModal(true);
  }, []);

  const handleCloseDetailModal = useCallback(() => {
    setShowDetailModal(false);
  }, []);

  const handlePayNow = useCallback(async () => {
    setIsSubmitting(true);
    setShowDetailModal(false);

    try {
      // Extract consultationId from selectedService object with fallback logic
      const consultationId =
        state?.selectedService?.id ||
        state?.selectedService?.consultationId ||
        state?.selectedService?._id;

      // Create ISO timestamps from date and time
      const createISOTimestamp = (date, timeString) => {
        if (!date || !timeString) return null;

        // Extract only the date part (year, month, day) from the date object
        const year = date.getFullYear();
        const month = date.getMonth(); // 0-based month
        const day = date.getDate();

        // Parse the time string (format: "HH:mm:ss")
        const [hours, minutes, seconds] = timeString.split(":").map(Number);

        // Create a new date with the extracted date and provided time
        // Use UTC to avoid timezone issues
        const appointmentDateTime = new Date(
          Date.UTC(year, month, day, hours, minutes, seconds || 0, 0),
        );

        // Return ISO string
        return appointmentDateTime.toISOString();
      };

      const startDateTime = createISOTimestamp(
        state?.selectedDate?.date,
        state?.selectedDate?.startTime,
      );

      const endDateTime = createISOTimestamp(
        state?.selectedDate?.date,
        state?.selectedDate?.endTime,
      );

      // Validate required data
      if (!state?.doctorId) {
        throw new Error("Doctor ID is missing");
      }

      if (!consultationId) {
        throw new Error("Consultation ID is missing");
      }

      if (!startDateTime || !endDateTime) {
        throw new Error("Appointment date and time are missing");
      }

      // Prepare the API payload
      const appointmentPayload = {
        doctorId: state?.doctorId,
        consultationId,
        startDateTime,
        endDateTime,
      };

      console.log("=== BOOKING APPOINTMENT ===");
      console.log("Selected Date Object:", state?.selectedDate);
      console.log("Start Time:", state?.selectedDate?.startTime);
      console.log("End Time:", state?.selectedDate?.endTime);
      console.log("Date:", state?.selectedDate?.date);
      console.log("Date toString:", state?.selectedDate?.date?.toString());
      console.log(
        "Date toISOString:",
        state?.selectedDate?.date?.toISOString(),
      );
      console.log("Generated Start DateTime:", startDateTime);
      console.log("Generated End DateTime:", endDateTime);

      // Additional debugging for backend validation
      if (startDateTime && endDateTime) {
        const startMoment = new Date(startDateTime);
        const endMoment = new Date(endDateTime);
        console.log("=== BACKEND VALIDATION DEBUG ===");
        console.log("Start Date Object:", startMoment);
        console.log("End Date Object:", endMoment);
        console.log(
          "Appointment Date (YYYY-MM-DD):",
          startMoment.toISOString().split("T")[0],
        );
        console.log(
          "Appointment Start Time (HH:mm:ss):",
          startMoment.toISOString().split("T")[1].split(".")[0],
        );
        console.log(
          "Appointment End Time (HH:mm:ss):",
          endMoment.toISOString().split("T")[1].split(".")[0],
        );
        console.log(
          "Day of Week:",
          startMoment
            .toLocaleDateString("en-US", { weekday: "long" })
            .toLowerCase(),
        );
        console.log("Original selectedDate structure:");
        console.log("  - date:", state?.selectedDate?.date);
        console.log("  - startTime:", state?.selectedDate?.startTime);
        console.log("  - endTime:", state?.selectedDate?.endTime);
        console.log("================================");
      }

      console.log("API Payload:", appointmentPayload);
      console.log(
        "API Payload JSON:",
        JSON.stringify(appointmentPayload, null, 2),
      );
      console.log("===========================");

      // Call the booking API
      const response = await bookAppointment_apiCalls(appointmentPayload);

      console.log("=== BOOKING RESPONSE ===");
      console.log("API Response:", response);
      console.log("Appointment ID:", response?.data?.id);
      console.log("========================");

      // Extract appointmentId from response
      const appointmentId = response?.data?.id;

      if (!appointmentId) {
        throw new Error("Appointment ID not received from server");
      }

      // Show success modal
      setIsSubmitting(false);
      setShowPaymentDoneModal(true);

      // Show success toast
      toast({
        description: "Appointment booked successfully!",
        variant: "default",
      });

      // Navigate after showing success message with appointmentId
      setTimeout(() => {
        handleNavigation(appointmentId);
      }, 2000);
    } catch (error) {
      setIsSubmitting(false);
      setShowDetailModal(true); // Show modal again on error

      console.error("=== BOOKING ERROR ===");
      console.error("Error:", error);
      console.error("=====================");

      toast({
        description:
          error.message || "Failed to book appointment. Please try again.",
        variant: "destructive",
      });
    }
  }, [handleNavigation, state, bookAppointment_apiCalls]);

  // API call - memoized
  const fetchDoctorProfile = useCallback(async () => {
    if (!state?.doctorId) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const res = await fetchDoctorWithId_api({
        doctorId: state.doctorId,
      });
      setDoctor(res.data);
    } catch (error) {
      toast({
        description: error.message || "Failed to fetch doctor profile",
        variant: "destructive",
      });
      console.error("ERROR IN fetchDoctorProfile => ", error);
    } finally {
      setIsLoading(false);
    }
  }, [state?.doctorId]);

  // Effect to fetch doctor data
  useEffect(() => {
    fetchDoctorProfile();
  }, [fetchDoctorProfile]);

  // Console log the navigation state data for debugging
  useEffect(() => {
    const consultationId =
      state?.selectedService?.id ||
      state?.selectedService?.consultationId ||
      state?.selectedService?._id;

    // Helper function to create ISO timestamp (same as in handlePayNow)
    const createISOTimestamp = (date, timeString) => {
      if (!date || !timeString) return null;
      const year = date.getFullYear();
      const month = date.getMonth();
      const day = date.getDate();
      const [hours, minutes, seconds] = timeString.split(":").map(Number);
      const appointmentDateTime = new Date(
        Date.UTC(year, month, day, hours, minutes, seconds || 0, 0),
      );
      return appointmentDateTime.toISOString();
    };

    const startDateTime = createISOTimestamp(
      state?.selectedDate?.date,
      state?.selectedDate?.startTime,
    );

    const endDateTime = createISOTimestamp(
      state?.selectedDate?.date,
      state?.selectedDate?.endTime,
    );

    console.log("=== NAVIGATION STATE DATA ===");
    console.log("Full state object:", state);
    console.log("Doctor ID:", state?.doctorId);
    console.log("Selected Date:", state?.selectedDate);
    console.log("Selected Service:", state?.selectedService);
    console.log("Extracted Consultation ID:", consultationId);
    console.log("Generated Start DateTime:", startDateTime);
    console.log("Generated End DateTime:", endDateTime);
    console.log("============================");

    // Check if essential data is missing (e.g., after page refresh)
    if (!state?.doctorId) {
      toast({
        description:
          "Appointment data is missing. Please start the booking process again.",
        variant: "destructive",
      });
      // Optionally redirect back to find doctor page
      setTimeout(() => {
        navigate("/patient/find-doctor");
      }, 3000);
    }
  }, [state, navigate]);

  // Extract date and time from state for easier access
  const appointmentDate = state?.selectedDate
    ? (() => {
        // Check if selectedDate is an object with a date property
        const dateValue =
          typeof state.selectedDate === "object" &&
          state.selectedDate !== null &&
          "date" in state.selectedDate
            ? state.selectedDate.date
            : state.selectedDate;

        try {
          const date = new Date(dateValue);
          if (isValid(date)) {
            return format(date, "EEEE, MMM d, yyyy");
          }
          return "Date not available";
        } catch (error) {
          console.error("Date formatting error:", error);
          return "Date not available";
        }
      })()
    : "Wednesday, Jan 21, 2025";

  const appointmentTime = state?.selectedDate?.startTime
    ? state.selectedDate.startTime.substring(0, 5)
    : "12:00 PM";

  // Render patient info section
  const renderPatientInfo = () => (
    <Card className="w-full">
      <CardHeader>
        <h2 className="text-lg sm:text-xl lg:text-2xl font-bold">
          Appointment Detail
        </h2>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Label htmlFor="patientName" className="text-sm sm:text-base">
            Patient Name
          </Label>
          <Input
            id="patientName"
            value={`${user?.firstName || ""} ${user?.lastName || ""}`}
            readOnly
            className="text-sm sm:text-base"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="contactNumber" className="text-sm sm:text-base">
            Contact Number
          </Label>
          <Input
            id="contactNumber"
            value={user?.contactNumber ? `${user.contactNumber}` : ""}
            readOnly
            className="text-sm sm:text-base"
          />
        </div>

        <div className="flex items-start gap-2">
          <InfoIconBlue className="w-4 h-4 sm:w-5 sm:h-5 mt-0.5 flex-shrink-0" />
          <p className="text-xs sm:text-sm lg:text-base font-semibold text-[#0D0D0DB2]">
            You will be contacted through this number
          </p>
        </div>

        <Separator className="my-2" />

        <div>
          <h3 className="text-base sm:text-lg lg:text-xl font-bold mb-3">
            Payment Method
          </h3>
          <RadioGroup defaultValue="stripe">
            <div className="flex items-center space-x-2 border rounded p-3">
              <div className="w-8 h-5 flex items-center justify-center">
                <StripeLogo />
              </div>
              <Label
                htmlFor="payment-stripe"
                className="flex-1 text-sm sm:text-base"
              >
                Payment Via Stripe
              </Label>
              <RadioGroupItem id="payment-stripe" value="stripe" />
            </div>
          </RadioGroup>
        </div>
      </CardContent>
    </Card>
  );

  // Render doctor info section
  const renderDoctorInfo = () => (
    <Card className="w-full">
      <CardContent className="flex flex-col items-center pt-6">
        {isLoading ? (
          <div className="w-full flex justify-center items-center py-8">
            <LoadingSpinner size="lg" />
          </div>
        ) : (
          <>
            <div className="w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full overflow-hidden mb-3">
              <img
                src={doctor?.profilePicture?.url || DefaultAvatar}
                alt={`Dr. ${doctor?.firstName || ""} ${doctor?.lastName || ""}`}
                className="object-cover w-full h-full"
              />
            </div>

            <h2 className="text-base sm:text-lg lg:text-xl font-bold text-[#1E1E1E]">
              Dr. {doctor?.firstName || ""} {doctor?.lastName || ""}
            </h2>

            <div className="text-center text-sm sm:text-base lg:text-lg mt-1 mb-2 text-[#1E1E1EB2] font-medium">
              {doctor?.qualifications?.length > 0 ? (
                <p>
                  {doctor.qualifications.map((qual, index) => (
                    <span key={index}>
                      {qual.degreeName}
                      {index < doctor.qualifications.length - 1 ? ", " : ""}
                    </span>
                  ))}
                </p>
              ) : (
                <p>No qualifications listed</p>
              )}
            </div>

            <p className="text-sm sm:text-base lg:text-lg font-medium mb-6">
              {state?.selectedService?.serviceName || ""} $
              {state?.selectedService?.price || ""}
            </p>

            <div className="w-full flex flex-col sm:flex-row gap-4 mb-6 items-center justify-center">
              <div className="flex flex-col sm:flex-row items-center gap-2 border-[1px] border-[#E7E8E9] border-solid p-2 rounded-md w-full sm:w-auto">
                <div className="flex items-center gap-2 border-r sm:border-r-[2px] border-r-[#E7E8E9] border-solid px-4 py-2 sm:py-0">
                  <CalendarIcon className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                  <span className="text-xs sm:text-sm lg:text-base font-semibold">
                    {appointmentDate}
                  </span>
                </div>
                <div className="flex items-center gap-2 px-4 py-2 sm:py-0">
                  <ClockIconBlue className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
                  <span className="text-xs sm:text-sm lg:text-base font-semibold">
                    {appointmentTime}
                  </span>
                </div>
              </div>
            </div>

            <Button
              variant="primary"
              className="w-full sm:w-auto text-sm sm:text-base px-4 py-2"
              size="fm"
              onClick={handleConfirmBooking}
              disabled={isSubmitting}
            >
              <CalendarIconWhite className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
              Confirm Booking
            </Button>
          </>
        )}
      </CardContent>
    </Card>
  );

  // If the page is in a loading state, show a full-page loader
  if (isLoading && !doctor) {
    return (
      <div className="w-full min-h-screen flex flex-col items-center justify-center">
        <LoadingSpinner size="xl" className="mb-4" />
        <p className="text-primary font-medium">
          Loading appointment details...
        </p>
      </div>
    );
  }

  return (
    <div className="w-full min-h-screen flex flex-col px-4 sm:px-6 lg:px-8 py-4 relative">
      {/* Overlay loading spinner for submission */}
      {isSubmitting && (
        <div className="fixed inset-0 bg-white bg-opacity-70 flex flex-col items-center justify-center z-50">
          <LoadingSpinner size="xl" className="mb-4" />
          <p className="text-primary font-medium">Processing payment...</p>
        </div>
      )}

      <div className="w-full py-4 sm:py-6 lg:py-8">
        <Breadcrumb>
          <BreadcrumbList className="flex flex-wrap items-center gap-1 sm:gap-2">
            <BreadcrumbItem>
              <BreadcrumbLink
                href="/"
                className="text-xs sm:text-sm lg:text-base hover:text-primary"
              >
                Home
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="text-muted-foreground w-3 h-3 sm:w-4 sm:h-4" />
            <BreadcrumbItem>
              <BreadcrumbLink
                href="/"
                className="text-xs sm:text-sm lg:text-base hover:text-primary"
              >
                Search Results
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="text-muted-foreground w-3 h-3 sm:w-4 sm:h-4" />
            <BreadcrumbItem>
              <BreadcrumbLink
                href="/"
                className="text-xs sm:text-sm lg:text-base hover:text-primary"
              >
                Doctor Profile
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator className="text-muted-foreground w-3 h-3 sm:w-4 sm:h-4" />
            <BreadcrumbItem>
              <BreadcrumbPage className="text-xs sm:text-sm lg:text-base font-medium">
                Book Appointment
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>

      <div className="flex-1 flex items-center justify-center w-full p-4">
        <div className="w-full max-w-7xl grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
          {renderPatientInfo()}
          {renderDoctorInfo()}
        </div>
      </div>

      {showDetailModal && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          onClick={handleCloseDetailModal}
        >
          <DetailModal
            onPayNow={handlePayNow}
            onClose={handleCloseDetailModal}
            doctor={doctor}
            selectedDate={appointmentDate}
            selectedTime={appointmentTime}
            selectedService={state?.selectedService}
            isSubmitting={isSubmitting}
          />
        </div>
      )}

      {showPaymentDoneModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <PaymentDoneModal />
        </div>
      )}
    </div>
  );
};

// Optimized DetailModal component
const DetailModal = ({
  onPayNow,
  onClose,
  doctor,
  selectedDate,
  selectedTime,
  selectedService,
  isSubmitting,
}) => {
  return (
    <Card className="relative" onClick={(e) => e.stopPropagation()}>
      {/* Close Button */}
      <button
        onClick={onClose}
        className="absolute top-4 right-4 z-10 p-1 rounded-full hover:bg-gray-100 transition-colors"
        aria-label="Close modal"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-6 w-6 text-gray-500 hover:text-gray-700"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M6 18L18 6M6 6l12 12"
          />
        </svg>
      </button>

      <CardContent className="flex flex-col items-center pt-6">
        <div className="w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-full overflow-hidden mb-3">
          <img
            src={doctor?.profilePicture?.url || DefaultAvatar}
            alt={`Dr. ${doctor?.firstName || "Unknown"} ${doctor?.lastName || "Doctor"}`}
            className="object-cover w-full h-full"
          />
        </div>

        <h2 className="text-base sm:text-lg lg:text-xl font-bold text-[#1E1E1E]">
          Dr. {doctor?.firstName || "Unknown"} {doctor?.lastName || "Doctor"}
        </h2>

        <div className="text-center text-sm sm:text-base lg:text-lg mt-1 mb-2 text-[#1E1E1EB2] font-medium">
          {doctor?.qualifications?.length > 0 ? (
            <p>
              {doctor.qualifications.map((qual, index) => (
                <span key={index}>
                  {qual.degreeName}
                  {index < doctor.qualifications.length - 1 ? ", " : ""}
                </span>
              ))}
            </p>
          ) : (
            <p>No qualifications listed</p>
          )}
        </div>

        <p className="text-base sm:text-lg lg:text-xl font-bold text-[#1E1E1E] mb-6">
          Fee: ${selectedService?.price || "150"}
        </p>

        <div className="w-full flex flex-col sm:flex-row gap-4 mb-6 items-center justify-center">
          <div className="flex flex-col sm:flex-row items-center gap-2 border-[1px] border-[#E7E8E9] border-solid p-2 rounded-md w-full sm:w-auto">
            <div className="flex items-center gap-2 border-r sm:border-r-[2px] border-r-[#E7E8E9] border-solid px-4 py-2 sm:py-0">
              <CalendarIcon className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
              <span className="text-xs sm:text-sm lg:text-base font-semibold">
                {selectedDate}
              </span>
            </div>
            <div className="flex items-center gap-2 px-4 py-2 sm:py-0">
              <ClockIconBlue className="w-4 h-4 sm:w-5 sm:h-5 text-primary" />
              <span className="text-xs sm:text-sm lg:text-base font-semibold">
                {selectedTime}
              </span>
            </div>
          </div>
        </div>

        <Button
          variant="primary"
          className="w-full sm:w-auto text-sm sm:text-base px-4 py-2"
          size="lg"
          onClick={onPayNow}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <LoadingSpinner size="sm" className="mr-2" />
          ) : (
            <PayIconWhite className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
          )}
          {isSubmitting ? "Processing..." : "Pay Now"}
        </Button>
      </CardContent>
    </Card>
  );
};

// Simple PaymentDoneModal component
const PaymentDoneModal = () => {
  return (
    <Card className="w-full max-w-md bg-white shadow-lg rounded-2xl p-6 flex flex-col items-center">
      <CardContent className="flex flex-col items-center">
        <img
          src={GreenCheckCircle}
          alt="Payment successful animation"
          className="w-24 h-24 object-contain"
        />
        <p className="font-bold text-[24px] text-[#16A34A]">Success</p>
      </CardContent>
      <CardFooter className="text-center">
        <p className="font-semibold text-lg text-gray-700">
          Your payment has been done successfully
        </p>
      </CardFooter>
    </Card>
  );
};

export default PatientConfirmBooking;
