import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "../../../components/ui/avatar";
import { Button } from "../../../components/ui/button";
import { Eye, Loader2 } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../../components/ui/table";
import { getAppointmentsByPatientId_apiCalls } from "../../../api/api_calls/appointment_apiCalls";
import { toast } from "../../../hooks/use-toast";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import { formatDateShort, formatTime } from "../../../helpers/dateTimeHelpers";

const PreviousAppointments = ({ patient }) => {
  const navigate = useNavigate();

  // State management
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [dateFilter, setDateFilter] = useState("All Time");

  // Fetch completed appointments for the patient
  const fetchPatientAppointments = async () => {
    if (!patient?.id) return;

    try {
      setLoading(true);

      // Calculate date range based on filter
      const today = new Date();
      let startDate = null;
      let endDate = null;

      if (dateFilter === "This Week") {
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay() + 1); // Monday
        startDate = startOfWeek.toISOString().split("T")[0];

        const endOfWeek = new Date(startOfWeek);
        endOfWeek.setDate(startOfWeek.getDate() + 6); // Sunday
        endDate = endOfWeek.toISOString().split("T")[0];
      } else if (dateFilter === "Last Week") {
        const startOfLastWeek = new Date(today);
        startOfLastWeek.setDate(today.getDate() - today.getDay() - 6); // Last Monday
        startDate = startOfLastWeek.toISOString().split("T")[0];

        const endOfLastWeek = new Date(startOfLastWeek);
        endOfLastWeek.setDate(startOfLastWeek.getDate() + 6); // Last Sunday
        endDate = endOfLastWeek.toISOString().split("T")[0];
      } else if (dateFilter === "Next Week") {
        const startOfNextWeek = new Date(today);
        startOfNextWeek.setDate(today.getDate() - today.getDay() + 8); // Next Monday
        startDate = startOfNextWeek.toISOString().split("T")[0];

        const endOfNextWeek = new Date(startOfNextWeek);
        endOfNextWeek.setDate(startOfNextWeek.getDate() + 6); // Next Sunday
        endDate = endOfNextWeek.toISOString().split("T")[0];
      }

      const response = await getAppointmentsByPatientId_apiCalls({
        patientId: patient.id,
        appointmentStatus: "completed", // Only fetch completed appointments
        startDate,
        endDate,
        limit: 100, // Get more records for this view
      });

      setAppointments(response.data?.appointments || []);
    } catch (error) {
      toast({
        description: error.message || "Failed to fetch appointments",
        variant: "destructive",
      });
      console.error("ERROR IN fetchPatientAppointments => ", error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch appointments when component mounts or filter changes
  useEffect(() => {
    fetchPatientAppointments();
  }, [patient?.id, dateFilter]);

  // Helper functions to extract data from appointment object
  const getAppointmentId = (appointment) => {
    return appointment?.id || "N/A";
  };

  const getDoctorName = (appointment) => {
    const doctor = appointment?.doctor;
    if (doctor?.firstName && doctor?.lastName) {
      return `${doctor.firstName} ${doctor.lastName}`;
    }
    return "N/A";
  };

  const getDoctorImage = (appointment) => {
    return appointment?.doctor?.profilePicture?.url || DefaultAvatar;
  };

  const getServiceName = (appointment) => {
    return appointment?.consultation?.serviceName || "N/A";
  };

  const formatStatus = (status) => {
    if (!status) return "N/A";
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const getStatusBadgeClass = (status) => {
    switch (status) {
      case "completed":
      case "Completed":
        return "bg-blue-100 text-blue-600";
      case "upcoming":
      case "Upcoming":
        return "bg-yellow-100 text-yellow-600";
      case "cancelled":
      case "Cancelled":
        return "bg-red-100 text-red-600";
      default:
        return "bg-gray-100 text-gray-600";
    }
  };

  return (
    <div className="w-full">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Previous Appointments</h2>
        <div className="flex gap-4">
          <Select
            value={dateFilter}
            onValueChange={(value) => setDateFilter(value)}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All Time">All Time</SelectItem>
              <SelectItem value="This Week">This Week</SelectItem>
              <SelectItem value="Last Week">Last Week</SelectItem>
              <SelectItem value="Next Week">Next Week</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
      <div className="overflow-x-auto">
        <Table>
          <TableHeader className="bg-[#DFE0E2]">
            <TableRow>
              <TableHead>Appointment ID</TableHead>
              <TableHead>Doctor Name</TableHead>
              <TableHead>Service</TableHead>
              <TableHead>Appointment Status</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Time</TableHead>
              <TableHead>Payment Status</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex justify-center items-center">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    Loading appointments...
                  </div>
                </TableCell>
              </TableRow>
            ) : appointments.length === 0 ? (
              <TableRow>
                <TableCell
                  colSpan={8}
                  className="text-center py-8 text-gray-500"
                >
                  No completed appointments found.
                </TableCell>
              </TableRow>
            ) : (
              appointments.map((appointment, index) => (
                <TableRow key={getAppointmentId(appointment) || index}>
                  <TableCell>{getAppointmentId(appointment)}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2 flex-row">
                      <div className="h-9 w-9">
                        <Avatar className="w-full h-full object-cover">
                          <AvatarImage src={getDoctorImage(appointment)} />
                          <AvatarFallback>
                            {getDoctorName(appointment)
                              .split(" ")
                              .map((name) => name[0])
                              .join("")
                              .toUpperCase() || "DR"}
                          </AvatarFallback>
                        </Avatar>
                      </div>
                      {getDoctorName(appointment)}
                    </div>
                  </TableCell>
                  <TableCell>{getServiceName(appointment)}</TableCell>
                  <TableCell>
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                        appointment.appointmentStatus,
                      )}`}
                    >
                      {formatStatus(appointment.appointmentStatus)}
                    </span>
                  </TableCell>
                  <TableCell>
                    {formatDateShort(appointment.startDateTime)}
                  </TableCell>
                  <TableCell>{formatTime(appointment.startDateTime)}</TableCell>
                  <TableCell>
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeClass(
                        appointment.paymentStatus || "Pending",
                      )}`}
                    >
                      {appointment.paymentStatus || "Pending"}
                    </span>
                  </TableCell>
                  <TableCell>
                    <button
                      className="text-gray-500 hover:text-blue-600"
                      onClick={() =>
                        navigate("/doctor/appointments-details", {
                          state: {
                            appointmentId: getAppointmentId(appointment),
                          },
                        })
                      }
                    >
                      <Eye size={20} />
                    </button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default PreviousAppointments;
