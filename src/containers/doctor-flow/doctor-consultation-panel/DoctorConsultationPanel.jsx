import { useState, useEffect } from "react";
import PreQuestioner from "../../../components/appointments-details-components/PreQuestioner";
import PatientSOAP from "../../../components/appointments-details-components/PatientSOAP";
import Labs from "../../../components/appointments-details-components/Labs";
import Prescription from "../../../components/appointments-details-components/Prescription";
import ROS from "../../../components/appointments-details-components/ROS";
import PatientCompliance from "../../../components/appointments-details-components/PatientCompliance";
import DoctorNotes from "../../../components/appointments-details-components/DoctorNotes";
import Imaging from "../../../components/appointments-details-components/Imaging";
import MedicalCertificate from "../../../components/appointments-details-components/MedicalCertificate";
import { Dialog, DialogTrigger } from "../../../components/ui/dialog";
import { Button } from "../../../components/ui/button";
import { Edit } from "lucide-react";
import DoctorNotesDialog from "../../../components/doctor-notes-dialog/DoctorNotesDialog";

const DoctorConsultationPanel = () => {
  const [callDuration, setCallDuration] = useState(0);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOff, setIsVideoOff] = useState(false);
  const [callStatus, setCallStatus] = useState("active");
  const [showFormsPanel, setShowFormsPanel] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  const [doctorNotes, setDoctorNotes] = useState([]);
  const [isDoctorNotesDialogOpen, setIsDoctorNotesDialogOpen] = useState(false);

  // Document components mapping
  const documentComponents = {
    "Pre-Questioner": PreQuestioner,
    "Patient SOAP": PatientSOAP,
    Labs: Labs,
    Prescription: Prescription,
    ROS: ROS,
    "Doctor's Notes": DoctorNotes,
    Imaging: Imaging,
    "Medical Certificate": MedicalCertificate,
  };

  // Simulate call timer
  useEffect(() => {
    const timer = setInterval(() => {
      setCallDuration((prev) => prev + 1);
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const formatTime = (seconds) => {
    const mins = Math.floor(seconds / 60)
      .toString()
      .padStart(2, "0");
    const secs = (seconds % 60).toString().padStart(2, "0");
    return `${mins}:${secs}`;
  };

  const handleEndCall = () => {
    setCallStatus("ended");
  };

  const toggleFormsPanel = () => {
    setShowFormsPanel(!showFormsPanel);
    if (!showFormsPanel) {
      setSelectedDocument(null);
    }
  };

  const selectDocument = (doc) => {
    setSelectedDocument(doc);
  };

  const handleBackToList = () => {
    setSelectedDocument(null);
  };

  const handleSaveForm = () => {
    // Handle form saving logic here
    console.log(`${selectedDocument} form saved`);
    handleBackToList();
  };

  const handleAddNote = (note) => {
    const newNote = {
      id: Date.now().toString(),
      ...note,
      createdAt: new Date(),
    };
    setDoctorNotes((prev) => [newNote, ...prev]);
    setIsDoctorNotesDialogOpen(false);
  };

  // Get the current document component
  const DocumentComponent = selectedDocument
    ? documentComponents[selectedDocument]
    : null;

  // Special handling for Doctor's Notes to pass notes data
  const getDocumentProps = () => {
    if (selectedDocument === "Doctor's Notes") {
      return {
        notes: doctorNotes,
        isLoading: false,
        onSave: handleSaveForm,
        onCancel: handleBackToList,
      };
    }
    return {
      onSave: handleSaveForm,
      onCancel: handleBackToList,
    };
  };

  return (
    <div className="flex h-screen bg-gray-900 overflow-hidden">
      {/* Main Video Area */}
      <div
        className={`flex flex-col ${showFormsPanel || selectedDocument ? "w-2/3" : "flex-1"}`}
      >
        {/* Header */}
        <div className="bg-gray-800 p-4 text-white">
          <div className="flex justify-between items-center">
            <div>
              <div className="text-lg font-medium">
                Patient Name: Sarah Johnson
              </div>
              <div className="flex items-center mt-1">
                <div className="w-3 h-3 rounded-sm mr-2 bg-blue-500"></div>
                <span className="text-sm">Video Call</span>
              </div>
            </div>
            <div className="text-sm text-right">
              <div>Monday July 22, 2024</div>
              <div className="mt-1">10:00 AM – 10:15 AM (15 minutes)</div>
            </div>
          </div>
        </div>

        {/* Video Area - Two Equal Panels */}
        <div className="flex-1 flex p-4 gap-4 relative">
          {/* Doctor's Video */}
          <div className="flex-1 bg-white rounded-lg overflow-hidden relative">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-24 h-24 rounded-full bg-blue-600 flex items-center justify-center text-white text-3xl font-bold">
                JP
              </div>
            </div>
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-gray-800 font-medium">
              Dr. John Peterson
            </div>
          </div>

          {/* Patient's Video */}
          <div className="flex-1 bg-white rounded-lg overflow-hidden relative">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-24 h-24 rounded-full bg-blue-600 flex items-center justify-center text-white text-3xl font-bold">
                SJ
              </div>
            </div>
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-gray-800 font-medium">
              Sarah Johnson
            </div>
          </div>

          {/* Controls - Positioned at bottom center of video area */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-700 rounded-full p-2 flex space-x-2">
            <button
              onClick={() => setIsMuted(!isMuted)}
              className="p-3 rounded-full bg-gray-600 text-white hover:bg-gray-500"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"
                />
              </svg>
            </button>

            <button
              onClick={() => setIsVideoOff(!isVideoOff)}
              className="p-3 rounded-full bg-gray-600 text-white hover:bg-gray-500"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                />
              </svg>
            </button>

            <button
              onClick={handleEndCall}
              className="p-3 rounded-full bg-red-600 text-white hover:bg-red-700"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M16 8l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2M5 3a2 2 0 00-2 2v1c0 8.284 6.716 15 15 15h1a2 2 0 002-2v-3.28a1 1 0 00-.684-.948l-4.493-1.498a1 1 0 00-1.21.502l-1.13 2.257a11.042 11.042 0 01-5.516-5.517l2.257-1.128a1 1 0 00.502-1.21L9.228 3.683A1 1 0 008.279 3H5z"
                />
              </svg>
            </button>

            <button
              onClick={toggleFormsPanel}
              className="p-3 rounded-full bg-gray-600 text-white hover:bg-gray-500"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                />
              </svg>
            </button>

            <button
              onClick={toggleFormsPanel}
              className="p-3 rounded-full bg-gray-600 text-white hover:bg-gray-500"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Forms Panel - Takes 1/3 width when active */}
      {(showFormsPanel || selectedDocument) && (
        <div className="w-1/3 bg-white flex flex-col">
          {/* Show Forms List */}
          {!selectedDocument && (
            <>
              <div className="flex-1 overflow-y-auto">
                {Object.keys(documentComponents).map((doc, index) => (
                  <div
                    key={index}
                    className="p-4 border-b border-gray-100 cursor-pointer hover:bg-[#0052FD1A] transition-colors group"
                    onClick={() => selectDocument(doc)}
                  >
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700 group-hover:text-[#0052FD] transition-colors">
                        {doc}
                      </span>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 text-gray-400 group-hover:text-[#0052FD] transition-colors"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}

          {/* Show Selected Form */}
          {selectedDocument && (
            <>
              <div className="p-4 border-b border-gray-200 flex justify-between items-center">
                <div className="flex items-center flex-row gap-2">
                  <button
                    onClick={handleBackToList}
                    className="flex items-center text-blue-600 hover:text-blue-800"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-1"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </button>

                  <h2 className="font-semibold text-lg text-gray-800">
                    {selectedDocument}
                  </h2>
                </div>

                {/* Add Note Button - Only show for Doctor's Notes */}
                {selectedDocument === "Doctor's Notes" && (
                  <Dialog
                    open={isDoctorNotesDialogOpen}
                    onOpenChange={setIsDoctorNotesDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant={"secondary"}
                        size={"icon"}
                        className="h-8 w-8"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DoctorNotesDialog onAddNote={handleAddNote} />
                  </Dialog>
                )}
              </div>
              <div className="flex-1 overflow-y-auto p-4">
                {DocumentComponent && (
                  <DocumentComponent {...getDocumentProps()} />
                )}
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

export default DoctorConsultationPanel;
