import React, { useEffect, useState, useRef, useCallback } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "../../../components/ui/avatar";
import { <PERSON><PERSON> } from "../../../components/ui/button";
import { Card, CardContent } from "../../../components/ui/card";
import { Badge } from "../../../components/ui/badge";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "../../../components/ui/tabs";
import {
  Mail,
  Phone,
  MapPin,
  ArrowLeft,
  Clock,
  Award,
  GraduationCap,
  Stethoscope,
} from "lucide-react";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import FadeInSection from "../../../components/animations/FadeInSection";
import { ReactComponent as LocationIcon } from "../../../assets/svgs/LocationWithCircleGrey.svg";
import { ReactComponent as EmailIcon } from "../../../assets/svgs/MailGrey.svg";
import { ReactComponent as PhoneIcon } from "../../../assets/svgs/CellIconGray.svg";
import { fetchDoctorWithId_api } from "../../../api/api_calls/doctor_apiCalls";
import { toast } from "../../../hooks/use-toast";
import DoctorQualificationComponent from "../../../components/patient-side-components/doctor-qualification-component/DoctorQualificationComponent";
import PracticeExperienceComponent from "../../../components/patient-side-components/practice-experience-component/PracticeExperienceComponent";

import { FaCheck } from "react-icons/fa";
import { ReactComponent as AwardIconColor } from "../../../assets/svgs/AwardIconColor.svg";
import { format, parseISO, parse } from "date-fns";
import { FaChevronRight, FaChevronLeft } from "react-icons/fa";

// Hardcoded doctor profile data
const defaultDoctorData = {
  id: 1,
  name: "Brooklyn Simmons",
  specialization:
    "MBBS, FCPS (Otorhinolaryngology), MBBS, MD - (General Medicine)",
  avatar: "https://github.com/shadcn.png",
  email: "<EMAIL>",
  phone: "(*************",
  address: "108 Juvinate3, New York, USA",
  country: "United States",
  experience: "10",
  rating: 4.8,
  totalReviews: 245,
  consultationFee: "$150",
  languages: ["English", "Spanish", "French"],
  education: [
    {
      degree: "MBBS",
      institution: "Harvard Medical School",
      year: "2008",
    },
    {
      degree: "FCPS (Otorhinolaryngology)",
      institution: "Royal College of Physicians",
      year: "2012",
    },
  ],
  experience_details: [
    {
      position: "Senior ENT Specialist",
      hospital: "New York Presbyterian Hospital",
      duration: "2015 - Present",
    },
    {
      position: "ENT Resident",
      hospital: "Mount Sinai Hospital",
      duration: "2012 - 2015",
    },
  ],
  awards: [
    "Best Doctor Award 2023",
    "Excellence in Patient Care 2022",
    "Medical Innovation Award 2021",
  ],
  about:
    "Brooklyn Simmons is a highly experienced ENT specialist with over 10 years of practice. She specializes in treating conditions related to the ear, nose, and throat, with particular expertise in minimally invasive surgical procedures. Brooklyn is known for her compassionate patient care and innovative treatment approaches.",
};

const DoctorViewDoctorProfile = () => {
  // State
  const [doctor, setDoctor] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [awardsStart, setAwardsStart] = useState(0);

  // Derived state
  const navigate = useNavigate();
  const location = useLocation();
  const { state } = location;

  // Get doctor ID from navigation state or use the passed doctor data
  const doctorId = state?.doctorData?.id || state?.doctorId;
  const doctorData = doctor || defaultDoctorData; // Use API data if available, fallback to default

  // Create refs for each section
  const bioRef = useRef(null);
  const qualificationRef = useRef(null);
  const experienceRef = useRef(null);
  const membershipRef = useRef(null);
  const awardsRef = useRef(null);

  // Function to scroll to a section
  const scrollToSection = useCallback((sectionRef) => {
    if (sectionRef && sectionRef.current) {
      sectionRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    }
  }, []);

  const sectionRefs = {
    "Doctor Bio": bioRef,
    Qualification: qualificationRef,
    Experience: experienceRef,
    Membership: membershipRef,
    Awards: awardsRef,
  };

  // Functions
  const fetchDoctorProfile = useCallback(async () => {
    if (!doctorId) {
      toast({
        description: "Doctor ID is required to view profile",
        variant: "destructive",
      });
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const res = await fetchDoctorWithId_api({
        doctorId: doctorId,
      });
      setDoctor(res.data);
      console.log("Doctor data fetched successfully:", res.data);
    } catch (error) {
      toast({
        description: error.message || "Failed to fetch doctor profile",
        variant: "destructive",
      });
      console.error("ERROR IN fetchDoctorProfile => ", error);
    } finally {
      setIsLoading(false);
    }
  }, [doctorId]);

  // Effects
  useEffect(() => {
    fetchDoctorProfile();
  }, [fetchDoctorProfile]);

  console.log("Current doctor state:", doctor);
  console.log("Using doctor ID:", doctorId);

  if (isLoading) {
    return (
      <div className="p-4 w-full flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading doctor profile...</p>
        </div>
      </div>
    );
  }

  if (!doctorId) {
    return (
      <div className="p-4 w-full flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-gray-600 mb-4">
            Doctor ID is required to view profile
          </p>
          <Button onClick={() => navigate(-1)} variant="outline">
            Go Back
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 w-full">
      {/* Doctor Profile Card */}
      <FadeInSection delay={0.1}>
        <Card className="mb-8 bg-white border border-gray-200 rounded-2xl shadow-sm">
          <CardContent className="p-8">
            <div className="flex items-center gap-8">
              {/* Doctor Avatar */}
              <div className="flex-shrink-0">
                <Avatar className="h-32 w-32 border-4 border-blue-600 rounded-full">
                  <AvatarImage
                    src={doctorData?.profilePicture?.url || DefaultAvatar}
                    alt={`${doctorData?.firstName || doctorData?.name || "Doctor"}'s profile picture`}
                    className="rounded-full"
                  />
                  <AvatarFallback className="text-xl bg-blue-100 text-blue-600 font-semibold rounded-full">
                    {doctorData?.firstName && doctorData?.lastName
                      ? `${doctorData.firstName[0]}${doctorData.lastName[0]}`
                      : doctorData?.name
                          ?.split(" ")
                          .map((n) => n[0])
                          .join("") || "DR"}
                  </AvatarFallback>
                </Avatar>
              </div>

              {/* Doctor Information */}
              <div className="flex-1 min-w-0">
                {/* Name and Specialization */}
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    {doctorData?.firstName && doctorData?.lastName
                      ? `${doctorData.firstName} ${doctorData.lastName}`
                      : doctorData?.name || "Doctor Name"}
                  </h2>
                  <p className="text-gray-600 text-base mb-4">
                    {doctorData?.speciality?.join(", ") ||
                      doctorData?.specialization ||
                      "Specialization"}
                  </p>
                </div>

                {/* Contact Information Row */}
                <div className="flex flex-wrap items-center gap-6 mb-6 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="gap-2 py-2 px-3">
                      <LocationIcon className="w-5 h-5 text-[#1E1E1E80]" />
                      <span className="text-[#1E1E1E] text-[16px]">
                        {doctorData?.address ||
                          doctorData?.location ||
                          "Address not available"}
                      </span>
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="gap-2 py-2 px-3">
                      <EmailIcon className="w-5 h-5 text-[#1E1E1E80]" />
                      <span className="text-[#1E1E1E] text-[16px]">
                        {doctorData?.email || "Email not available"}
                      </span>
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="gap-2 py-2 px-3">
                      <PhoneIcon className="w-5 h-5 text-[#1E1E1E80]" />
                      <span className="text-[#1E1E1E] text-[16px]">
                        {doctorData?.contactNumber || "Phone not available"}
                      </span>
                    </Badge>
                  </div>
                </div>

                {/* Badges Row */}
                <div className="flex flex-wrap items-center gap-4">
                  <div className="flex items-center gap-1">
                    <Badge variant="secondary" className="gap-2 py-2 px-3">
                      <span className="text-[#1E1E1EB2] text-[16px]">
                        Language:
                      </span>
                      <div className="flex gap-1">
                        {(doctorData?.languages || doctorData?.language || [])
                          .slice(0, 2)
                          .map((language, index) => (
                            <span
                              key={index}
                              variant="secondary"
                              className="gap-2"
                            >
                              <span className="text-[#1E1E1E] text-[16px]">
                                {language}
                              </span>
                            </span>
                          ))}
                      </div>
                    </Badge>
                  </div>

                  <div className="flex items-center gap-1">
                    <Badge variant="secondary" className="gap-2 py-2 px-3">
                      <span className="text-[#1E1E1EB2] text-[16px]">
                        License Number:
                      </span>
                      <span className="text-[#1E1E1E] text-[16px]">
                        {doctorData?.medicalLicenseNumber || "Not available"}
                      </span>
                    </Badge>
                  </div>

                  <div className="flex items-center gap-1">
                    <Badge variant="secondary" className="gap-2 py-2 px-3">
                      <span className="text-[#1E1E1EB2] text-[16px]">
                        Years of Experience:
                      </span>
                      <span className="text-[#1E1E1E] text-[16px]">
                        {doctorData?.yearsOfExperience ||
                          doctorData?.experience?.replace(" experience", "") ||
                          "Not available"}
                      </span>
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </FadeInSection>

      <FadeInSection delay={0.2}>
        <div className="flex flex-wrap items-center justify-start w-full gap-3 mb-8 ">
          {Object.keys(sectionRefs).map((item) => (
            <Badge
              key={item}
              variant="outline"
              className="text-sm py-2 px-4 rounded-lg cursor-pointer hover:bg-[#0052FD14] transition-colors"
              onClick={() => scrollToSection(sectionRefs[item])}
            >
              {item}
            </Badge>
          ))}
        </div>

        {/* Doctor Bio */}
        <div ref={bioRef} className="w-full flex flex-col scroll-mt-20 mb-8">
          <div className="border-y border-solid border-[#E7E8E9] py-6">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl mb-4">
              Doctor Bio
            </h3>
            <h6 className="font-light text-sm sm:text-base lg:text-lg">
              {doctorData?.bio ||
                doctorData?.about ||
                "Highly motivated and experienced doctor with a passion for providing excellent care to patients. Experienced in a wide variety of medical settings, with particular expertise in diagnostics, primary care and emergency medicine. Skilled in using the latest technology to streamline patient care. Committed to delivering compassionate, personalized care to each and every patient"}
            </h6>
          </div>
        </div>

        {/* Qualification Section */}
        <div
          ref={qualificationRef}
          className="w-full flex flex-col scroll-mt-20 mb-8"
        >
          <div className="py-6">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl mb-4">
              Qualification
            </h3>
          </div>
          <div className="flex flex-col gap-6 relative min-h-[120px]">
            {doctor?.qualifications?.length > 0 ? (
              doctor.qualifications.map((qual) => (
                <DoctorQualificationComponent
                  key={qual.id || qual.degreeName}
                  qualification={qual}
                />
              ))
            ) : (
              <div className="flex items-center justify-center h-full border border-dashed border-[#E7E8E9] rounded-lg p-8">
                <p className="text-gray-500">No qualifications available</p>
              </div>
            )}
          </div>
        </div>

        {/* Practice Experience Section */}
        <div
          ref={experienceRef}
          className="w-full flex flex-col scroll-mt-20 mb-8"
        >
          <div className="py-6">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl mb-4">
              Practice Experience
            </h3>
          </div>
          <div className="flex flex-col gap-6 relative min-h-[120px]">
            {doctor?.practiceExperiences?.length > 0 ? (
              doctor.practiceExperiences.map((exp) => (
                <PracticeExperienceComponent
                  key={exp.id || exp.hospitalName}
                  practiceExperience={exp}
                />
              ))
            ) : (
              <div className="flex items-center justify-center h-full border border-dashed border-[#E7E8E9] rounded-lg p-8">
                <p className="text-gray-500">
                  No practice experience available
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Membership Section */}
        <div
          ref={membershipRef}
          className="w-full flex flex-col scroll-mt-20 mb-8"
        >
          <div className="py-6">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl mb-4">
              Membership
            </h3>
          </div>
          <div className="flex flex-col gap-6">
            {doctor?.membershipDetails ? (
              <div className="flex w-full items-start gap-4 border border-solid border-[#E7E8E9] p-6 rounded-lg">
                <div className="w-10 flex items-center justify-center">
                  <div className="h-8 w-8 bg-[#34C759] rounded-full flex items-center justify-center">
                    <FaCheck className="text-white w-4 h-4" />
                  </div>
                </div>
                <div className="flex-1">
                  <h1 className="font-normal text-sm sm:text-base lg:text-lg text-[#1E1E1EB2]">
                    {doctor.membershipDetails}
                  </h1>
                </div>
              </div>
            ) : (
              <div className="w-full border border-dashed border-[#E7E8E9] rounded-lg p-8 text-center">
                <p className="text-gray-500">No membership details available</p>
              </div>
            )}
          </div>
        </div>

        {/* Awards Section */}
        <div ref={awardsRef} className="w-full scroll-mt-20 mb-8">
          <div className="py-6">
            <h3 className="font-bold text-lg sm:text-xl lg:text-2xl mb-4">
              Awards
            </h3>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 min-h-[300px]">
            {doctor?.awards && doctor.awards.length > 0 ? (
              doctor.awards.slice(awardsStart, awardsStart + 4).map((award) => (
                <div
                  key={award?.id}
                  className="flex flex-col border border-[#E7E8E9] p-6 rounded-lg hover:shadow-sm transition-shadow"
                >
                  <AwardIconColor className="w-8 h-8 mb-4" />
                  <h3 className="font-bold text-[#1E1E1E] text-base mb-3">
                    {`${award?.awardName} (${format(parseISO(award.issueDate), "yyyy")})`}
                  </h3>
                  <p className="font-normal text-[#1E1E1EB2] text-sm">
                    {award?.awardDetails}
                  </p>
                </div>
              ))
            ) : (
              <div className="col-span-2 flex items-center justify-center h-full border border-dashed border-[#E7E8E9] rounded-lg p-8">
                <p className="text-[#1E1E1EB2] text-center">
                  No awards to display
                </p>
              </div>
            )}
          </div>

          {doctor?.awards?.length > 4 && (
            <div className="flex justify-center gap-4 mt-8">
              <button
                className="h-10 w-10 border border-[#D9D9D9] bg-[#F2F2F7] rounded-full flex items-center justify-center hover:bg-[#0052FD10] transition-colors"
                disabled={awardsStart === 0}
                onClick={() => {
                  setAwardsStart((prev) => prev - 4);
                }}
              >
                <FaChevronLeft className="text-[#0052FD] text-sm" />
              </button>
              <button
                className="h-10 w-10 border border-[#D9D9D9] bg-[#F2F2F7] rounded-full flex items-center justify-center hover:bg-[#0052FD10] transition-colors"
                onClick={() => {
                  setAwardsStart((prev) => prev + 4);
                }}
                disabled={awardsStart + 4 >= (doctor?.awards?.length || 0)}
              >
                <FaChevronRight className="text-[#0052FD] text-sm" />
              </button>
            </div>
          )}
        </div>
      </FadeInSection>
    </div>
  );
};

export default DoctorViewDoctorProfile;
