import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "../../../components/ui/card";
import SendGif from "../../../assets/images/PendingCenterItem.gif";
import { X } from "lucide-react";
import { logOutUser } from "../../../common/logoutUser";

const ProfileUnderReview = () => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black">
      <Card className="w-full max-w-md bg-white shadow-lg rounded-2xl p-6 flex flex-col items-center">
        <CardHeader className="w-full items-end justify-end">
          <X
            className="cursor-pointer"
            onClick={() => {
              logOutUser();
            }}
          />
        </CardHeader>
        <CardContent className="flex flex-col items-center">
          <img
            src={SendGif}
            alt="Profile under review animation"
            className="w-48 h-48 object-contain"
          />
        </Card<PERSON>ontent>
        <CardFooter className="text-center">
          <p className="font-semibold text-lg text-gray-700">
            Your request has been sent successfully. We will get back to you
            within 24 hours.
          </p>
        </CardFooter>
      </Card>
    </div>
  );
};

export default ProfileUnderReview;
