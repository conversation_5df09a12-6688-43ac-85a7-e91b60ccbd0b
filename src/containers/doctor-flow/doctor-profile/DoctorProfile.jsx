import React from "react";
import { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Button } from "../../../components/ui/button";
import { EditIcon, Plus, Trash2 } from "lucide-react";
import { useToast } from "../../../hooks/use-toast";

// Import all modal components
import MembershipDetailsModal from "./components/MembershipDetailsModal";
import PractiseExpierienceModal from "./components/PractiseExpierienceModal";
import SignatureModal from "./components/SignatureModal";
import AwardsModal from "./components/AwardsModal";
import QualificationModal from "./components/QualificationModal";
import UploadIdCard from "./components/UploadIdCard";
import ServiceAndPricing from "./components/ServicesAndPriceModal";
import SpecializationModal from "./components/SpecializationModal";
import PersonalInformationModal from "./components/PersonalInformationModal";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../../../components/ui/alert-dialog";

import { ReactComponent as PhoneIcon } from "../../../assets/svgs/CellIconGray.svg";
import { ReactComponent as GlobeGrey } from "../../../assets/svgs/GlobeGray.svg";
import { ReactComponent as AddressIconGray } from "../../../assets/svgs/AddressIconGray.svg";
import { updateDoctor_api } from "../../../api/api_calls/doctor_apiCalls";
import { me_api } from "../../../api/api_calls/auth_apiCalls";
import { setUser } from "../../../redux/slices/userSlice";
import { updateDoctorConsultations_api } from "../../../api/api_calls/consultation_apiCalls";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import { getSingleCountryLabel } from "../../../helper/country-helper";
import { getArrayLanguageLabels } from "../../../helper/language-helper";

const Profile = () => {
  // TEST CODE
  const dispatch = useDispatch();
  const user = useSelector((state) => state.userReducer.user);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const [membershipModalOpen, setMembershipModalOpen] = useState(false);
  const [experienceModalOpen, setExperienceModalOpen] = useState(false);
  const [signatureModalOpen, setSignatureModalOpen] = useState(false);
  const [awardsModalOpen, setAwardsModalOpen] = useState(false);
  const [qualificationModalOpen, setQualificationModalOpen] = useState(false);
  const [idCardModalOpen, setIdCardModalOpen] = useState(false);
  const [serviceModalOpen, setServiceModalOpen] = useState(false);
  const [specialtyModalOpen, setSpecialtyModalOpen] = useState(false);
  const [personalInfoModalOpen, setPersonalInfoModalOpen] = useState(false);

  const [membershipDetails, setMembershipDetails] = useState(null);
  const [experiences, setExperiences] = useState([]);
  const [signature, setSignature] = useState(null);
  const [awards, setAwards] = useState([]);
  const [qualifications, setQualifications] = useState([]);
  const [idCardFront, setIdCardFront] = useState(null);
  const [idCardBack, setIdCardBack] = useState(null);
  const [selectedExperience, setSelectedExperience] = useState(null);
  const [selectedAward, setSelectedAward] = useState(null);
  const [selectedQualification, setSelectedQualification] = useState(null);
  const [services, setServices] = useState([]);
  const [specialties, setSpecialties] = useState([]);
  const [selectedService, setSelectedService] = useState(null);
  const [selectedSpecialty, setSelectedSpecialty] = useState(null);

  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState({ type: null, id: null });

  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (user) {
      setLoading(false);
      if (user.membershipDetails) {
        setMembershipDetails({ details: user.membershipDetails });
      }

      if (user.practiceExperiences && user.practiceExperiences.length > 0) {
        setExperiences(user.practiceExperiences);
      }

      if (user.signature) {
        setSignature({
          type: "upload",
          dataUrl: user.signature.url,
        });
      }

      if (user.awards && user.awards.length > 0) {
        setAwards(user.awards);
      }

      if (user.qualifications && user.qualifications.length > 0) {
        setQualifications(user.qualifications);
      }

      if (user.idFront) {
        setIdCardFront(user.idFront);
      }

      if (user.idBack) {
        setIdCardBack(user.idBack);
      }

      if (user.consultations && user.consultations.length > 0) {
        setServices(user.consultations);
      }

      if (user.speciality && user.speciality.length > 0) {
        setSpecialties(user.speciality);
      }
    }
  }, [user]);

  const handleSavingState = (saving, success = false) => {
    setIsSaving(saving);
    if (!saving && success) {
      toast({
        title: "Changes saved",
        description: "Your changes have been successfully saved.",
        variant: "success",
      });
    }
  };

  const handleSaveMembership = async (data) => {
    // console.log("MEMBERSHIP => ", data);
    // setIsSaving(true);
    // setTimeout(() => {
    //   setMembershipDetails(data);
    //   setIsSaving(false);
    //   toast({
    //     title: "Membership updated",
    //     description: "Your membership details have been saved.",
    //     variant: "success",
    //   });
    // }, 500);

    // console.log("Membership details saved:", data);
    try {
      setIsSaving(true);
      await updateDoctor_api({
        doctorData: {
          membershipDetails: data.details,
        },
      });
      const res = await me_api();
      dispatch(setUser(res.data));
      setMembershipModalOpen(false);
    } catch (error) {
      console.log("ERROR IN handleSaveMembership => ", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveExperience = async (data) => {
    try {
      setIsSaving(true);
      let experience_array = [];
      const newExperience = { ...data };
      if (selectedExperience) {
        const updatedExperiences = experiences.map((exp) =>
          exp.id === selectedExperience.id ? { ...exp, ...newExperience } : exp,
        );
        experience_array = updatedExperiences;
        setSelectedExperience(null);
      } else {
        const updatedExperiences = [...experiences, newExperience];
        experience_array = updatedExperiences;
      }

      const practiceExperienceFiles = [];
      const practiceExperiences_final = [];

      experience_array.map((experience) => {
        if (data.id && experience.id && data.id === experience.id) {
          practiceExperiences_final.push({
            hospitalName: data.hospitalName,
            designation: data.designation,
            description: data.description,
            startDate: data.startDate,
            endDate: data.endDate,
            id: data.id,
          });
          practiceExperienceFiles.push(data.experienceFile);
        } else {
          practiceExperiences_final.push({
            hospitalName: experience.hospitalName,
            designation: experience.designation,
            description: experience.description,
            startDate: experience.startDate,
            endDate: experience.endDate,
            id: experience.id,
          });
          practiceExperienceFiles.push(experience.experienceFile);
        }
      });

      await updateDoctor_api({
        doctorData: {
          practiceExperiences: practiceExperiences_final,
        },
        practiceExperienceFiles,
      });
      const res = await me_api();
      dispatch(setUser(res.data));
      setExperienceModalOpen(false);
    } catch (error) {
      console.log("ERROR IN handleSaveExperience => ", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveSignature = async (data) => {
    try {
      setIsSaving(true);
      await updateDoctor_api({
        signature: data.file,
      });
      const res = await me_api();
      dispatch(setUser(res.data));

      toast({
        title: "Signature updated",
        description: "Your signature has been saved successfully.",
        variant: "success",
      });

      setSignatureModalOpen(false);
    } catch (error) {
      console.error("ERROR IN handleSaveSignature => ", error);
      toast({
        title: "Error",
        description: "Failed to update signature. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveAward = async (data) => {
    try {
      setIsSaving(true);
      let awards_array = [];
      if (selectedAward) {
        const updatedAwards = awards.map((award) =>
          award.id === selectedAward.id ? { ...award, ...data } : award,
        );
        awards_array = updatedAwards;
        setSelectedAward(null);
      } else {
        const updatedAwards = [...awards, data];
        awards_array = updatedAwards;
      }

      await updateDoctor_api({
        doctorData: {
          awards: awards_array,
        },
      });
      const res = await me_api();
      dispatch(setUser(res.data));
      setAwardsModalOpen(false);
    } catch (error) {
      console.log("ERROR IN handleSaveAward => ", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveQualification = async (data) => {
    try {
      setIsSaving(true);
      let qualifications_array = [];
      const newQualification = { ...data };
      if (selectedQualification) {
        const updatedQualifications = qualifications.map((qual) =>
          qual.id === selectedQualification.id
            ? { ...qual, ...newQualification }
            : qual,
        );
        qualifications_array = updatedQualifications;
        setSelectedQualification(null);
      } else {
        const updatedQualifications = [...qualifications, newQualification];
        qualifications_array = updatedQualifications;
      }

      const qualifications_final = [];
      const qualificationDegreeFiles = [];

      qualifications_array.map((qualification) => {
        if (data.id && qualification.id && data.id === qualification.id) {
          qualifications_final.push({
            degreeName: data.degreeName,
            institutionName: data.institutionName,
            startDate: data.startDate,
            endDate: data.endDate,
            id: data.id,
          });
          qualificationDegreeFiles.push(data.degreeFile);
        } else {
          qualifications_final.push({
            degreeName: qualification.degreeName,
            institutionName: qualification.institutionName,
            startDate: qualification.startDate,
            endDate: qualification.endDate,
            id: qualification.id,
          });
          qualificationDegreeFiles.push(qualification.degreeFile);
        }
      });

      await updateDoctor_api({
        doctorData: {
          qualifications: qualifications_final,
        },
        qualificationDegreeFiles,
      });
      const res = await me_api();
      dispatch(setUser(res.data));
      setQualificationModalOpen(false);
    } catch (error) {
      console.log("ERROR IN handleSaveQualification => ", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveServicePricing = async (data) => {
    console.log("DATA => ", data);
  };

  const handleSpecializationOnSave = async (data) => {
    console.log("Specialization Data => ", data);
  };

  const handleSaveIdCard = async (frontCard, backCard) => {
    try {
      setIsSaving(true);
      await updateDoctor_api({
        idFront: frontCard,
        idBack: backCard,
      });
      const res = await me_api();
      dispatch(setUser(res.data));
      setIdCardModalOpen(false);
    } catch (error) {
      console.log("ERROR IN handleSaveIdCard => ", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleEditExperience = (experience) => {
    setSelectedExperience(experience);
    setExperienceModalOpen(true);
  };

  const handleEditAward = (award) => {
    setSelectedAward(award);
    setAwardsModalOpen(true);
  };

  const handleEditQualification = (qualification) => {
    setSelectedQualification(qualification);
    setQualificationModalOpen(true);
  };

  const handleEditService = (service) => {
    setSelectedService(service);
    setServiceModalOpen(true);
  };

  const handleEditSpecialty = (index) => {
    setSelectedSpecialty(index);
    setSpecialtyModalOpen(true);
  };

  const confirmDelete = (type, id) => {
    setItemToDelete({ type, id });
    setDeleteConfirmOpen(true);
  };

  const executeDelete = async () => {
    try {
      setIsSaving(true);
      switch (itemToDelete.type) {
        case "experience": {
          setExperiences(
            experiences.filter((exp) => exp.id !== itemToDelete.id),
          );
          const experiences_final = experiences.filter(
            (exp) => exp.id !== itemToDelete.id,
          );
          await updateDoctor_api({
            doctorData: {
              practiceExperiences: experiences_final,
            },
          });
          const res_exp = await me_api();
          dispatch(setUser(res_exp.data));
          toast({
            title: "Experience deleted",
            description: "The experience has been removed from your profile.",
            variant: "success",
          });
          setDeleteConfirmOpen(false);
          break;
        }
        case "qualification": {
          setQualifications(
            qualifications.filter((qual) => qual.id !== itemToDelete.id),
          );
          const qualifications_final = qualifications.filter(
            (qual) => qual.id !== itemToDelete.id,
          );
          await updateDoctor_api({
            doctorData: {
              qualifications: qualifications_final,
            },
          });
          const res_qual = await me_api();
          dispatch(setUser(res_qual.data));
          toast({
            title: "Qualification deleted",
            description:
              "The qualification has been removed from your profile.",
            variant: "success",
          });
          setDeleteConfirmOpen(false);
          break;
        }
        case "award": {
          setAwards(awards.filter((award) => award.id !== itemToDelete.id));
          const awards_final = awards.filter(
            (award) => award.id !== itemToDelete.id,
          );
          await updateDoctor_api({
            doctorData: {
              awards: awards_final,
            },
          });
          const res_award = await me_api();
          dispatch(setUser(res_award.data));
          toast({
            title: "Award deleted",
            description: "The award has been removed from your profile.",
            variant: "success",
          });
          setDeleteConfirmOpen(false);
          break;
        }
        case "membership": {
          setMembershipDetails(null);
          await updateDoctor_api({
            doctorData: {
              membershipDetails: null,
            },
          });
          const res_membership = await me_api();
          dispatch(setUser(res_membership.data));
          toast({
            title: "Membership deleted",
            description: "Your membership details have been removed.",
            variant: "success",
          });
          setDeleteConfirmOpen(false);
          break;
        }
        case "service": {
          setServices(
            services.filter((service) => service.id !== itemToDelete.id),
          );
          const services_final = services.filter(
            (service) => service.id !== itemToDelete.id,
          );
          await updateDoctorConsultations_api({
            consultations: services_final,
          });
          const res_service = await me_api();
          dispatch(setUser(res_service.data));
          toast({
            title: "Service deleted",
            description: "The service has been removed from your profile.",
            variant: "success",
          });
          setDeleteConfirmOpen(false);
          break;
        }
        default:
          break;
      }
    } catch (error) {
      console.log("ERROR IN executeDelete => ", error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleDeleteExperience = (experienceId) => {
    confirmDelete("experience", experienceId);
  };

  const handleDeleteQualification = (qualificationId) => {
    confirmDelete("qualification", qualificationId);
  };

  const handleDeleteAward = (awardId) => {
    confirmDelete("award", awardId);
  };

  const handleDeleteMembership = () => {
    confirmDelete("membership", null);
  };

  const handleDeleteService = (serviceId) => {
    confirmDelete("service", serviceId);
  };

  const handleDeleteSpecialty = (index) => {
    setSpecialties(specialties.filter((_, i) => i !== index));
  };

  const formatDate = (dateString) => {
    if (!dateString) return "";
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-4 px-4 sm:py-6 sm:px-6 lg:px-8">
      {isSaving && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-[100]">
          <div className="bg-white p-4 rounded-lg shadow-lg flex items-center gap-3">
            <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
            <span>Saving changes...</span>
          </div>
        </div>
      )}

      {/* Personal Information */}
      <div className="mb-8">
        <h2 className="text-xl font-medium mb-4">Personal Information</h2>
        <div className="rounded-lg p-6 border-solid border-[#E7E8E9] border-[1px]">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="flex-shrink-0 flex flex-col items-center">
              <img
                src={user?.profilePicture?.url || DefaultAvatar}
                alt="Profile"
                className="w-32 h-32 rounded-full object-cover"
              />
            </div>

            <div className="flex-grow">
              <div className="flex justify-between items-start mb-4">
                <h3 className="text-xl font-semibold text-[#1E1E1E]">
                  {user?.firstName} {user?.lastName}
                </h3>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                  onClick={() => setPersonalInfoModalOpen(true)}
                  disabled={isSaving}
                >
                  <EditIcon className="h-4 w-4" />
                </Button>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 items-start">
                <div className="space-y-4">
                  <p className="text-sm text-[#1E1E1E] font-[500] capitalize">
                    <span className="text-[#1E1E1EB2]">Gender:</span>{" "}
                    {user?.gender}
                  </p>
                  <p className="text-sm text-[#1E1E1E] font-[500] capitalize">
                    <span className="text-[#1E1E1EB2]">Date of Birth: </span>
                    {formatDate(user?.dob)}
                  </p>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <PhoneIcon className="h-[16px] w-[16px]" />
                    <h6 className="text-sm text-[#1E1E1E] font-[500]">
                      {user?.contactNumber}
                    </h6>
                  </div>

                  <div className="flex items-center gap-2">
                    <GlobeGrey className="h-[16px] w-[16px]" />
                    <p className="text-sm text-[#1E1E1E] font-[500]">
                      {getArrayLanguageLabels(user?.language)}
                    </p>
                  </div>

                  <div className="flex items-center gap-2">
                    <AddressIconGray className="h-[16px] w-[16px]" />
                    <p className="text-sm text-[#1E1E1E] font-[500]">
                      {user?.address}
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <p className="text-sm text-[#1E1E1E] font-[500]">
                    <span className="text-[#1E1E1EB2]">Country:</span>{" "}
                    {getSingleCountryLabel(user?.country)}
                  </p>
                  <p className="text-sm text-[#1E1E1E] font-[500]">
                    <span className="text-[#1E1E1EB2]">
                      Medical License Number:
                    </span>{" "}
                    {user?.medicalLicenseNumber}
                  </p>
                  <p className="text-sm text-[#1E1E1E] font-[500]">
                    <span className="text-[#1E1E1EB2]">
                      Total Years of Experience:
                    </span>{" "}
                    {user?.yearsOfExperience}
                  </p>
                </div>
              </div>

              {user?.bio && (
                <div className="mt-6">
                  <p className="text-sm text-[#1E1E1EB2]">{user?.bio}</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* ID Card */}
      <div className="mb-8">
        <h2 className="text-xl font-medium mb-4">ID Card</h2>
        <div className="rounded-lg p-6 border-solid border-[#E7E8E9] border-[1px]">
          <div className="flex flex-wrap gap-4 sm:gap-6">
            {idCardFront && idCardBack ? (
              <>
                <div className="w-[206px] h-[108px] border rounded-md overflow-hidden">
                  <img
                    src={idCardFront.url || URL.createObjectURL(idCardFront)}
                    alt="ID Card Front"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="w-[206px] h-[108px] border rounded-md overflow-hidden">
                  <img
                    src={idCardBack.url || URL.createObjectURL(idCardBack)}
                    alt="ID Card Back"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex gap-2 ml-auto">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                    onClick={() => setIdCardModalOpen(true)}
                    disabled={isSaving}
                  >
                    <EditIcon className="h-4 w-4" />
                  </Button>
                </div>
              </>
            ) : (
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => setIdCardModalOpen(true)}
                disabled={isSaving}
              >
                <Plus className="h-4 w-4" />
                Add ID Card
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Signature */}
      <div className="mb-8">
        <h2 className="text-xl font-medium mb-4">Signature</h2>
        <div className="rounded-lg p-6 border-solid border-[#E7E8E9] border-[1px]">
          {signature ? (
            <div className="flex justify-between items-center">
              <div className="w-60 h-20 bg-gray-50 rounded-md flex items-center justify-center p-2">
                <img
                  src={
                    signature.dataUrl ||
                    user?.signature?.url ||
                    "/placeholder.svg" ||
                    "/placeholder.svg"
                  }
                  alt="Signature"
                  className="max-h-full max-w-full object-contain"
                />
              </div>
              <div className="flex gap-2 h-20">
                {/* <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                  onClick={handleDeleteSignature}
                  disabled={isSaving}
                >
                  <Trash2 className="h-4 w-4" />
                </Button> */}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                  onClick={() => setSignatureModalOpen(true)}
                  disabled={isSaving}
                >
                  <EditIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : (
            <Button
              variant="outline"
              className="flex items-center gap-2"
              onClick={() => setSignatureModalOpen(true)}
              disabled={isSaving}
            >
              <Plus className="h-4 w-4" />
              Add Signature
            </Button>
          )}
        </div>
      </div>

      {/* Qualification */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium">Qualification</h2>
          <Button
            variant="outline"
            className="flex items-center gap-2 text-blue-600"
            onClick={() => {
              setSelectedQualification(null);
              setQualificationModalOpen(true);
            }}
            disabled={isSaving}
          >
            <Plus className="h-4 w-4" />
            Add Qualification
          </Button>
        </div>
        <div className="rounded-lg border-solid border-[#E7E8E9] border-[1px]">
          {qualifications.length > 0 ? (
            <div className="divide-y">
              {qualifications.map((qualification, index) => (
                <div key={qualification.id || index} className="p-6">
                  <div className="flex justify-between">
                    <div>
                      <h3 className="font-semibold">
                        {qualification.institutionName}
                      </h3>
                      <p className="text-gray-600">
                        {qualification.degreeName}
                      </p>
                      <p className="text-sm text-gray-500">
                        {formatDate(qualification.startDate)} -{" "}
                        {formatDate(qualification.endDate)}
                      </p>

                      {qualification.degreeFile && (
                        <div className="w-[206px] h-[128px] border rounded-md overflow-hidden mt-4">
                          <img
                            src={
                              qualification?.degreeFile?.url ||
                              URL.createObjectURL(qualification.degreeFile)
                            }
                            alt="Degree Certificate"
                            className="w-full h-full object-contain"
                          />
                        </div>
                      )}
                    </div>
                    <div className="flex gap-2">
                      {qualifications.length > 1 && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                          onClick={() =>
                            handleDeleteQualification(qualification.id)
                          }
                          disabled={isSaving}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}

                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                        onClick={() => handleEditQualification(qualification)}
                        disabled={isSaving}
                      >
                        <EditIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center text-gray-500">
              No qualifications added yet.
            </div>
          )}
        </div>
      </div>

      {/* Experience */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium">Experience</h2>
          <Button
            variant="outline"
            className="flex items-center gap-2 text-blue-600"
            onClick={() => {
              setSelectedExperience(null);
              setExperienceModalOpen(true);
            }}
            disabled={isSaving}
          >
            <Plus className="h-4 w-4" />
            Add Experience
          </Button>
        </div>
        <div className="rounded-lg border-solid border-[#E7E8E9] border-[1px]">
          {experiences.length > 0 ? (
            <div className="divide-y">
              {experiences.map((experience, index) => (
                <div key={experience.id || index} className="p-6">
                  <div className="flex justify-between">
                    <div>
                      <h3 className="font-semibold">
                        {experience.hospitalName}
                      </h3>
                      <p className="text-gray-600">{experience.designation}</p>
                      <p className="text-sm text-gray-500">
                        {formatDate(experience.startDate)} -{" "}
                        {experience.endDate
                          ? formatDate(experience.endDate)
                          : "Present"}
                      </p>
                      {experience.description && (
                        <p className="mt-2 text-gray-700">
                          {experience.description}
                        </p>
                      )}

                      {experience.experienceFile && (
                        <div className="w-[206px] h-[128px] border rounded-md overflow-hidden mt-4">
                          <img
                            src={
                              experience?.experienceFile?.url ||
                              URL.createObjectURL(experience.experienceFile)
                            }
                            alt="Experience Certificate"
                            className="w-full h-full object-contain"
                          />
                        </div>
                      )}
                    </div>
                    <div className="flex gap-2">
                      {experiences.length > 1 && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                          onClick={() => handleDeleteExperience(experience.id)}
                          disabled={isSaving}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}

                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                        onClick={() => handleEditExperience(experience)}
                        disabled={isSaving}
                      >
                        <EditIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center text-gray-500">
              No experience added yet.
            </div>
          )}
        </div>
      </div>

      {/* Services & Pricing */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium">
            Services & Pricing{" "}
            <span className="text-sm text-gray-500">(per session)</span>
          </h2>
          <Button
            variant="outline"
            className="flex items-center gap-2 text-blue-600"
            onClick={() => {
              setSelectedService(null);
              setServiceModalOpen(true);
            }}
            disabled={isSaving}
          >
            <Plus className="h-4 w-4" />
            Add Service
          </Button>
        </div>
        <div className="rounded-lg p-6 border-solid border-[#E7E8E9] border-[1px]">
          <div className="flex flex-wrap gap-2 sm:gap-3">
            {services.length > 0 ? (
              services.map((service, index) => (
                <div
                  key={index}
                  className="bg-gray-100 rounded-md px-3 py-2 flex items-center justify-between"
                >
                  <span>
                    {service.serviceName}{" "}
                    <span className="text-gray-500">${service.price}</span>
                  </span>
                  <div className="flex gap-1 ml-3">
                    {services.length > 1 && (
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                        onClick={() => handleDeleteService(service.id)}
                        disabled={isSaving}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                      onClick={() => handleEditService(service)}
                      disabled={isSaving}
                    >
                      <EditIcon className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-gray-500">No services added yet.</div>
            )}
          </div>
        </div>
      </div>

      {/* Specialty */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium">Specialty</h2>
          <Button
            variant="outline"
            className="flex items-center gap-2 text-blue-600"
            onClick={() => {
              setSelectedSpecialty(null);
              setSpecialtyModalOpen(true);
            }}
            disabled={isSaving}
          >
            <Plus className="h-4 w-4" />
            Add Specialty
          </Button>
        </div>
        <div className="rounded-lg p-6 border-solid border-[#E7E8E9] border-[1px]">
          <div className="flex flex-wrap gap-2 sm:gap-3">
            {specialties.length > 0 ? (
              specialties.map((specialty, index) => (
                <div
                  key={index}
                  className="bg-gray-100 rounded-md px-3 py-2 flex items-center justify-between"
                >
                  <span>{specialty}</span>
                  <div className="flex gap-1 ml-3">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                      onClick={() => handleDeleteSpecialty(index)}
                      disabled={isSaving}
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                      onClick={() => handleEditSpecialty(index)}
                      disabled={isSaving}
                    >
                      <EditIcon className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-gray-500">No specialties added yet.</div>
            )}
          </div>
        </div>
      </div>

      {/* Membership */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium">Membership</h2>
          {!membershipDetails ? (
            <Button
              variant="outline"
              className="flex items-center gap-2 text-blue-600"
              onClick={() => setMembershipModalOpen(true)}
              disabled={isSaving}
            >
              <Plus className="h-4 w-4" />
              Add Membership
            </Button>
          ) : null}
        </div>
        <div className="rounded-lg p-6 border-solid border-[#E7E8E9] border-[1px]">
          {membershipDetails ? (
            <div className="flex justify-between">
              <p className="text-gray-700">{membershipDetails.details}</p>
              <div className="flex gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                  onClick={handleDeleteMembership}
                  disabled={isSaving}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                  onClick={() => setMembershipModalOpen(true)}
                  disabled={isSaving}
                >
                  <EditIcon className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center py-4 text-gray-500">
              No membership details added yet.
            </div>
          )}
        </div>
      </div>

      {/* Awards */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium">Awards</h2>
          <Button
            variant="outline"
            className="flex items-center gap-2 text-blue-600"
            onClick={() => {
              setSelectedAward(null);
              setAwardsModalOpen(true);
            }}
            disabled={isSaving}
          >
            <Plus className="h-4 w-4" />
            Add Award
          </Button>
        </div>
        <div className="rounded-lg border-solid border-[#E7E8E9] border-[1px] p-4 sm:p-6">
          {awards.length > 0 ? (
            <div>
              {awards.map((award, index) => (
                <div
                  key={award.id || index}
                  className="mb-4 rounded-lg relative"
                >
                  <div className="flex justify-between items-center">
                    <h3 className="font-semibold text-[16px] text-[#1E1E1E]">
                      {award.awardName}
                    </h3>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                        onClick={() => handleDeleteAward(award.id)}
                        disabled={isSaving}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-[#0052FD] border-solid border-2 border-[#0052FD]"
                        onClick={() => handleEditAward(award)}
                        disabled={isSaving}
                      >
                        <EditIcon className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <p className="text-[16px] text-[#1E1E1EB2] font-semibold">
                    {award.awardedBy}
                  </p>
                  <p className="text-[16px] text-[#1E1E1EB2] font-semibold">
                    {formatDate(award.issueDate)}
                  </p>
                  {award.awardDetails && (
                    <p className="text-[16px] text-[#1E1E1EB2] font-semibold">
                      {award.awardDetails}
                    </p>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="p-6 text-center text-gray-500">
              No awards added yet.
            </div>
          )}
        </div>
      </div>

      {/* membership Modal */}
      {membershipModalOpen && (
        <MembershipDetailsModal
          initialData={membershipDetails}
          onSave={handleSaveMembership}
          onClose={() => setMembershipModalOpen(false)}
        />
      )}

      {/* Expierience Modal */}
      {experienceModalOpen && (
        <PractiseExpierienceModal
          initialData={selectedExperience}
          onSave={handleSaveExperience}
          onClose={() => {
            setExperienceModalOpen(false);
            setSelectedExperience(null);
          }}
        />
      )}

      {/* signatue modal */}
      {signatureModalOpen && (
        <SignatureModal
          initialData={signature}
          onSave={handleSaveSignature}
          onClose={() => setSignatureModalOpen(false)}
        />
      )}

      {/* awards modal */}
      {awardsModalOpen && (
        <AwardsModal
          initialData={selectedAward}
          onSave={handleSaveAward}
          onClose={() => {
            setAwardsModalOpen(false);
            setSelectedAward(null);
          }}
        />
      )}

      {/* qualificatiion modal */}
      {qualificationModalOpen && (
        <QualificationModal
          qualification={selectedQualification}
          onSave={handleSaveQualification}
          onClose={() => {
            setQualificationModalOpen(false);
            setSelectedQualification(null);
          }}
        />
      )}

      {/* id card modal */}
      {idCardModalOpen && (
        <UploadIdCard
          initialFront={idCardFront}
          initialBack={idCardBack}
          onSave={handleSaveIdCard}
          onClose={() => setIdCardModalOpen(false)}
        />
      )}

      {/* services modal */}
      {serviceModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[80vh] overflow-y-auto">
            <ServiceAndPricing
              services={services}
              onClose={() => {
                setServiceModalOpen(false);
                setSelectedService(null);
              }}
              onSaving={handleSaveServicePricing}
            />
          </div>
        </div>
      )}

      {/* specializations modal */}
      {specialtyModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[80vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4">Your Specialization</h2>
            <SpecializationModal
              specializations={specialties}
              onSaving={handleSpecializationOnSave}
              onClose={() => {
                setSpecialtyModalOpen(false);
                setSelectedService(null);
              }}
            />
          </div>
        </div>
      )}

      {/* personal information modal */}
      {personalInfoModalOpen && (
        <PersonalInformationModal
          onClose={() => setPersonalInfoModalOpen(false)}
          onNext={() => {
            setPersonalInfoModalOpen(false);
            toast({
              description: "Personal information updated successfully",
              variant: "success",
            });
          }}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteConfirmOpen} onOpenChange={setDeleteConfirmOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will remove this item from your profile. This action cannot
              be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <Button variant={"primary"} onClick={executeDelete}>
              Delete
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default Profile;
