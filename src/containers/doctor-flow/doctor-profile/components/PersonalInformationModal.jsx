import React from "react";
import { parsePhoneNumberFromString } from "libphonenumber-js";
import { useState, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../../components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../../components/ui/form";
import { format, parseISO } from "date-fns";
import { Calendar } from "../../../../components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "../../../../components/ui/popover";
import { cn } from "../../../../lib/utils";
import { Textarea } from "../../../../components/ui/textarea";
import { X, Key, Eye, EyeOff } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "../../../../components/ui/dialog";
import ActivityLoaderComponent from "../../../../components/animations/LoadingSpinner";
import { Badge } from "../../../../components/ui/badge";
import { Checkbox } from "../../../../components/ui/checkbox";
import { ScrollArea } from "../../../../components/ui/scroll-area";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../../../components/ui/tooltip";
import { FaUserCircle } from "react-icons/fa";
import { HiMail } from "react-icons/hi";
import { ReactComponent as CalenderIcon } from "../../../../assets/svgs/CalendarIcon.svg";
import { ReactComponent as LanguageIcon } from "../../../../assets/svgs/LanguageIcon.svg";
import { ReactComponent as GenderIcon } from "../../../../assets/svgs/GenderIcon.svg";
import { ReactComponent as AddressIcon } from "../../../../assets/svgs/AddressIcon.svg";
import { ReactComponent as FileIcon } from "../../../../assets/svgs/FileIcon.svg";
import { ReactComponent as ExpierienceIcon } from "../../../../assets/svgs/ExpierienceIcon.svg";
import { useDispatch, useSelector } from "react-redux";
import { updateDoctor_api } from "../../../../api/api_calls/doctor_apiCalls";
import { toast } from "../../../../hooks/use-toast";
import Languages from "../../../../constants/languages";
import Countries from "../../../../constants/countries";
import Genders from "../../../../constants/genders";
import "react-phone-input-2/lib/style.css";
import DefaultAvatar from "../../../../assets/images/DefaultAvatar.png";

// Custom CSS for phone input to match UI
import "./phone-input-custom.css";
import { me_api } from "../../../../api/api_calls/auth_apiCalls";
import { setUser } from "../../../../redux/slices/userSlice";
import PhoneInputWithCountryCode from "../../../../components/phone-input-with-country-code/PhoneInputWithCountryCode";

const formSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  contactNumber: z
    .string()
    .nonempty("Contact number is required")
    .refine(
      (val) => {
        // our phone inputs are saved as digits only, so prepend '+'
        const num = parsePhoneNumberFromString(`+${val}`);
        return num?.isValid() ?? false;
      },
      {
        message: "Phone number is not valid",
      },
    ),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  language: z.array(z.string()).min(1, "Please select at least one language"),
  gender: z
    .string()
    .min(1, "Gender is required")
    .refine((val) => val !== "", {
      message: "Gender is required",
    }),
  address: z.string().min(10, "Address must be at least 10 characters"),
  country: z.string().min(1, "Country is required"),
  medicalLicense: z.string().min(1, "Medical license is required"),
  experience: z.coerce.number().min(0, "Experience cannot be negative"),
  bio: z.string().min(20, "Bio must be at least 20 characters"),
});

// Password Change Schema
const passwordChangeSchema = z
  .object({
    currentPassword: z.string().min(1, "Current password is required"),
    newPassword: z
      .string()
      .min(8, "Password must be at least 8 characters long"),
    confirmPassword: z.string().min(1, "Please confirm your new password"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  })
  .refine((data) => data.currentPassword !== data.newPassword, {
    message: "New password must be different from current password",
    path: ["newPassword"],
  });

// Password Change Modal Component
const PasswordChangeModal = ({ isOpen, onClose }) => {
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const passwordForm = useForm({
    resolver: zodResolver(passwordChangeSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const togglePasswordVisibility = (field) => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const onPasswordSubmit = async (data) => {
    try {
      // TODO: Add API call to change password
      // await changePassword_api({
      //   currentPassword: data.currentPassword,
      //   newPassword: data.newPassword
      // });

      toast({
        title: "Password updated",
        description: "Your password has been changed successfully.",
        variant: "success",
      });

      handleClose();
    } catch (error) {
      console.error("Error updating password:", error);
      toast({
        title: "Error",
        description:
          error.message || "Failed to update password. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleClose = () => {
    passwordForm.reset();
    setShowPasswords({
      current: false,
      new: false,
      confirm: false,
    });
    onClose();
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px] p-0 bg-white rounded-2xl border-0 shadow-2xl">
        {/* Header */}
        <DialogHeader className="px-8 py-6 border-b-0">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-2xl font-semibold text-gray-900">
              Create New Password
            </DialogTitle>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="px-8 pb-8">
          <Form {...passwordForm}>
            <form
              onSubmit={passwordForm.handleSubmit(onPasswordSubmit)}
              className="space-y-6"
            >
              {/* Current Password */}
              <FormField
                control={passwordForm.control}
                name="currentPassword"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Old Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Key className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-600" />
                        <Input
                          {...field}
                          type={showPasswords.current ? "text" : "password"}
                          placeholder="Enter old password"
                          className="pl-12 pr-12"
                          // className="pl-12 pr-12 h-14 text-base border-2 rounded-2xl bg-gray-50 border-gray-200 focus:border-blue-500 focus:bg-white focus:ring-0 placeholder:text-gray-400"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 h-8 w-8 hover:bg-transparent"
                          onClick={() => togglePasswordVisibility("current")}
                        >
                          {showPasswords.current ? (
                            <EyeOff className="h-5 w-5 text-blue-600" />
                          ) : (
                            <Eye className="h-5 w-5 text-blue-600" />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* New Password */}
              <FormField
                control={passwordForm.control}
                name="newPassword"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>New Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Key className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-600" />
                        <Input
                          {...field}
                          type={showPasswords.new ? "text" : "password"}
                          placeholder="Enter new password"
                          className="pl-12 pr-12"
                          // className="pl-12 pr-12 h-14 text-base border-2 rounded-2xl bg-gray-50 border-gray-200 focus:border-blue-500 focus:bg-white focus:ring-0 placeholder:text-gray-400"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 h-8 w-8 hover:bg-transparent"
                          onClick={() => togglePasswordVisibility("new")}
                        >
                          {showPasswords.new ? (
                            <EyeOff className="h-5 w-5 text-blue-600" />
                          ) : (
                            <Eye className="h-5 w-5 text-blue-600" />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Confirm Password */}
              <FormField
                control={passwordForm.control}
                name="confirmPassword"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel>Confirm Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Key className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-blue-600" />
                        <Input
                          {...field}
                          type={showPasswords.confirm ? "text" : "password"}
                          placeholder="Re-enter password"
                          className="pl-12 pr-12"
                          // className="pl-12 pr-12 h-14 text-base border-2 rounded-2xl bg-gray-50 border-gray-200 focus:border-blue-500 focus:bg-white focus:ring-0 placeholder:text-gray-400"
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 h-8 w-8 hover:bg-transparent"
                          onClick={() => togglePasswordVisibility("confirm")}
                        >
                          {showPasswords.confirm ? (
                            <EyeOff className="h-5 w-5 text-blue-600" />
                          ) : (
                            <Eye className="h-5 w-5 text-blue-600" />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Submit Button */}
              <div className="pt-4">
                <Button
                  type="submit"
                  variant={"primary"}
                  disabled={passwordForm.formState.isSubmitting}
                  className="w-full"
                  // className="w-full  bg-blue-600 hover:bg-blue-700 text-white text-lg font-medium rounded-2xl transition-colors"
                >
                  {passwordForm.formState.isSubmitting
                    ? "Changing Password..."
                    : "Change Password"}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

const PersonalInformationModal = ({ onClose, onNext }) => {
  const dispatch = useDispatch();
  const user = useSelector((state) => state.userReducer.user);
  const [image, setImage] = useState(null);
  const [imageError, setImageError] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showPasswordModal, setShowPasswordModal] = useState(false);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: user.firstName ?? "",
      lastName: user.lastName ?? "",
      email: user.email ?? "",
      contactNumber: user.contactNumber?.toString() ?? "", // Convert to string
      dateOfBirth: user.dob ?? "", // Don't set a default date if user.dob is empty
      language: Array.isArray(user.language)
        ? user.language
        : user.language
          ? [user.language]
          : [],
      gender: user.gender ? user.gender : undefined,
      address: user.address ?? "",
      country: user.country ?? "",
      medicalLicense: user.medicalLicenseNumber ?? "",
      experience: user.yearsOfExperience ?? 0,
      bio: user.bio ?? "",
    },
  });

  const initialDate = useMemo(() => {
    const dateValue = form.getValues("dateOfBirth");
    return dateValue ? parseISO(dateValue) : null;
  }, [form]);

  const [selectedYear, setSelectedYear] = useState(
    initialDate ? initialDate.getFullYear() : new Date().getFullYear(),
  );
  const [month, setMonth] = useState(
    initialDate ? initialDate.getMonth() : new Date().getMonth(),
  );

  const years = useMemo(
    () => Array.from({ length: 124 }, (_, i) => 1900 + i),
    [],
  );

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Check if file is a valid image type (JPEG or PNG)
      const validTypes = ["image/jpeg", "image/jpg", "image/png"];
      if (!validTypes.includes(file.type)) {
        setImageError("Please upload only JPEG or PNG images");
        return;
      }

      setImage(file);
      setImageError("");
    }
  };

  const onSubmit = async (data) => {
    if (!user?.profilePicture?.url) {
      if (!image) {
        setImageError("Please upload an image");
        return;
      }
    }

    setIsSubmitting(true);
    try {
      await updateDoctor_api({
        doctorData: {
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          contactNumber: data.contactNumber,
          dob: new Date(data.dateOfBirth),
          language: data.language,
          gender: data.gender,
          address: data.address,
          country: data.country,
          medicalLicenseNumber: data.medicalLicense,
          yearsOfExperience: data.experience,
          bio: data.bio,
        },
        profilePicture: image,
      });

      const res = await me_api();
      dispatch(setUser(res.data));

      toast({
        description: "Profile details updated successfully",
        variant: "success",
      });
      onNext();
    } catch (error) {
      toast({
        description: error.message || "Failed to update profile details",
        variant: "destructive",
      });
      console.log("ERROR IN UPDATE  => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const getLanguageLabel = (value) => {
    const language = Languages.find((lang) => lang.value === value);
    return language ? language.label : value;
  };

  const getCountryLabel = (value) => {
    const country = Countries.find((c) => c.value === value);
    return country ? country.label : value;
  };

  const getUserCountry = () => {
    const phoneNumber = user.contactNumber?.toString() || "";
    if (phoneNumber.startsWith("+")) {
      const countryCodes = {
        1: "us",
        44: "gb",
        91: "in",
        61: "au",
        49: "de",
        33: "fr",
        86: "cn",
        81: "jp",
      };
      for (const [code, iso] of Object.entries(countryCodes)) {
        if (phoneNumber.startsWith(`+${code}`)) {
          return iso;
        }
      }
    }
    return user.country?.toLowerCase() || "us";
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[80vh] overflow-y-auto">
        {isSubmitting && (
          <div className="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-50 rounded-lg">
            <ActivityLoaderComponent />
          </div>
        )}

        <div className="w-full items-center justify-center flex flex-col gap-3 mb-8">
          <label
            htmlFor="dropzone-file"
            className="relative flex flex-col items-center justify-center w-[100px] h-[100px] border-2 border-primary border-solid rounded-full cursor-pointer bg-white hover:bg-gray-100 overflow-hidden transition-all duration-200 hover:shadow-md"
            onDragOver={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onDragEnter={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onDragLeave={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onDrop={(e) => {
              e.preventDefault();
              e.stopPropagation();
              const file = e.dataTransfer.files[0];
              if (file) {
                handleImageChange({ target: { files: [file] } });
              }
            }}
          >
            {image ? (
              <img
                src={URL.createObjectURL(image) || DefaultAvatar}
                alt="Uploaded"
                className="absolute top-0 left-0 w-full h-full object-cover"
              />
            ) : (
              <img
                src={user?.profilePicture?.url || DefaultAvatar}
                alt="Uploaded"
                className="absolute top-0 left-0 w-full h-full object-cover"
              />
            )}
            <div className="flex flex-col items-center justify-center z-10 bg-white bg-opacity-60 hover:bg-opacity-40 transition-all duration-200 w-full h-full">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="40"
                height="40"
                viewBox="0 0 24 24"
                fill="none"
                stroke="#0052FD"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z" />
                <circle cx="12" cy="13" r="3" />
              </svg>
            </div>
            <input
              type="file"
              id="dropzone-file"
              className="hidden"
              onChange={handleImageChange}
              accept=".jpeg,.jpg,.png"
            />
          </label>
          <h1 className="font-bold text-lg sm:text-xl text-center">
            Update Your Picture
          </h1>

          {/* Change Password Link */}
          <button
            onClick={() => setShowPasswordModal(true)}
            className="flex items-center justify-center gap-2 text-blue-600 hover:text-blue-700 transition-colors duration-200 font-medium"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"
              />
            </svg>
            Change Password
          </button>

          {imageError && (
            <p className="text-red-500 text-sm mt-1">{imageError}</p>
          )}
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
              {/* First Name */}
              <FormField
                control={form.control}
                name="firstName"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel className="font-semibold text-gray-800">
                      First Name
                    </FormLabel>
                    <FormControl>
                      <div className="relative w-full">
                        <FaUserCircle
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                          size={18}
                        />
                        <Input
                          {...field}
                          placeholder="Enter your first name"
                          className={`pl-10 transition-all h-10 ${
                            fieldState.error
                              ? "border-destructive focus:ring-destructive/50"
                              : "focus:ring-primary/50 focus:border-primary"
                          } focus:ring-2`}
                        />
                      </div>
                    </FormControl>
                    {fieldState.error && (
                      <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                        {fieldState.error.message}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />

              {/* Last Name */}
              <FormField
                control={form.control}
                name="lastName"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel className="font-semibold text-gray-800">
                      Last Name
                    </FormLabel>
                    <FormControl>
                      <div className="relative w-full">
                        <FaUserCircle
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                          size={18}
                        />
                        <Input
                          {...field}
                          placeholder="Enter your last name"
                          className={`pl-10 transition-all h-10 ${
                            fieldState.error
                              ? "border-destructive focus:ring-destructive/50"
                              : "focus:ring-primary/50 focus:border-primary"
                          } focus:ring-2`}
                        />
                      </div>
                    </FormControl>
                    {fieldState.error && (
                      <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                        {fieldState.error.message}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />

              {/* Email */}
              <FormField
                control={form.control}
                name="email"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel className="font-semibold text-gray-800">
                      Email
                    </FormLabel>
                    <FormControl>
                      <div className="relative w-full">
                        <HiMail
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                          size={18}
                        />
                        <Input
                          {...field}
                          placeholder="Enter your email"
                          className={`pl-10 transition-all h-10 ${
                            fieldState.error
                              ? "border-destructive focus:ring-destructive/50"
                              : "focus:ring-primary/50 focus:border-primary"
                          } focus:ring-2`}
                        />
                      </div>
                    </FormControl>
                    {fieldState.error && (
                      <FormMessage className="animate-in fade-in-0 slide-in-from-top-1 duration-200">
                        {fieldState.error.message}
                      </FormMessage>
                    )}
                  </FormItem>
                )}
              />

              {/* Contact Number */}
              <FormField
                control={form.control}
                name="contactNumber"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel className="font-semibold text-gray-800">
                      Contact Number
                    </FormLabel>
                    <FormControl>
                      <PhoneInputWithCountryCode
                        value={field.value}
                        onChange={field.onChange}
                        error={!!fieldState.error}
                        getUserCountry={getUserCountry}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Date of Birth */}
              <FormField
                control={form.control}
                name="dateOfBirth"
                render={({ field, fieldState }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel className="font-semibold text-gray-800">
                      Date of Birth
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <CalenderIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                        <Popover>
                          <PopoverTrigger asChild>
                            <FormControl>
                              <Button
                                variant={"outline"}
                                className={cn(
                                  "w-full text-left font-normal h-10 pl-10",
                                  !field.value &&
                                    "text-[#1E1E1E80] text-[14px] font-thin",
                                  fieldState.error && "border-red-500",
                                )}
                              >
                                <span className="w-full font-bold">
                                  {field.value
                                    ? format(parseISO(field.value), "PPP")
                                    : "Pick a date"}
                                </span>
                              </Button>
                            </FormControl>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <div className="flex items-center justify-between p-2 border-b">
                              <Select
                                value={selectedYear.toString()}
                                onValueChange={(value) => {
                                  const year = Number.parseInt(value, 10);
                                  setSelectedYear(year);
                                  setMonth(0);
                                }}
                              >
                                <SelectTrigger className="w-[120px] h-8">
                                  <SelectValue>
                                    {selectedYear.toString()}
                                  </SelectValue>
                                </SelectTrigger>
                                <SelectContent>
                                  <ScrollArea className="h-80">
                                    {years.map((year) => (
                                      <SelectItem
                                        key={year}
                                        value={year.toString()}
                                      >
                                        {year}
                                      </SelectItem>
                                    ))}
                                  </ScrollArea>
                                </SelectContent>
                              </Select>
                            </div>
                            <Calendar
                              mode="single"
                              selected={
                                field.value ? parseISO(field.value) : null
                              }
                              onSelect={(date) => {
                                field.onChange(
                                  date ? format(date, "yyyy-MM-dd") : "",
                                );
                                if (date) {
                                  setSelectedYear(date.getFullYear());
                                  setMonth(date.getMonth());
                                }
                              }}
                              month={new Date(selectedYear, month)}
                              onMonthChange={(date) => {
                                setSelectedYear(date.getFullYear());
                                setMonth(date.getMonth());
                              }}
                              disabled={(date) =>
                                date > new Date() ||
                                date < new Date("1900-01-01")
                              }
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="language"
                render={({ field, fieldState }) => {
                  const selected = field.value || [];
                  return (
                    <FormItem>
                      <FormLabel className="font-semibold text-gray-800">
                        Languages
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <LanguageIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary z-10" />
                          <Select>
                            <SelectTrigger
                              className="pl-10 min-h-10"
                              hasError={!!fieldState.error}
                            >
                              <div className="flex flex-wrap items-center gap-1 py-0.5">
                                {selected.length === 0 ? (
                                  <span className="text-[#1E1E1E80] text-[14px] font-bold">
                                    Select Languages
                                  </span>
                                ) : (
                                  <>
                                    {selected.slice(0, 2).map((lang) => (
                                      <Badge
                                        key={lang}
                                        variant="secondary"
                                        className="flex items-center gap-1 bg-blue-100 hover:bg-blue-200 text-blue-800"
                                      >
                                        {getLanguageLabel(lang)}
                                        <X
                                          className="h-3 w-3 cursor-pointer"
                                          onClick={(e) => {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            const newSelected = selected.filter(
                                              (l) => l !== lang,
                                            );
                                            field.onChange(newSelected);
                                          }}
                                        />
                                      </Badge>
                                    ))}
                                    {selected.length > 2 && (
                                      <TooltipProvider>
                                        <Tooltip delayDuration={100}>
                                          <TooltipTrigger asChild>
                                            <div className="cursor-help">
                                              <Badge
                                                variant="secondary"
                                                className="bg-gray-100 text-gray-800"
                                              >
                                                +{selected.length - 2}
                                              </Badge>
                                            </div>
                                          </TooltipTrigger>
                                          <TooltipContent
                                            className="p-3 bg-white border shadow-lg rounded-md z-50"
                                            sideOffset={5}
                                          >
                                            <div className="font-medium text-sm mb-2">
                                              All Selected Languages:
                                            </div>
                                            <div className="flex flex-wrap gap-1 max-w-[250px]">
                                              {selected.map((lang) => (
                                                <Badge
                                                  key={lang}
                                                  variant="secondary"
                                                  className="flex items-center gap-1 bg-blue-100 text-blue-800 mb-1"
                                                >
                                                  {getLanguageLabel(lang)}
                                                  <X
                                                    className="h-3 w-3 cursor-pointer"
                                                    onClick={(e) => {
                                                      e.preventDefault();
                                                      e.stopPropagation();
                                                      const newSelected =
                                                        selected.filter(
                                                          (l) => l !== lang,
                                                        );
                                                      field.onChange(
                                                        newSelected,
                                                      );
                                                    }}
                                                  />
                                                </Badge>
                                              ))}
                                            </div>
                                          </TooltipContent>
                                        </Tooltip>
                                      </TooltipProvider>
                                    )}
                                  </>
                                )}
                              </div>
                            </SelectTrigger>
                            <SelectContent>
                              <ScrollArea className="h-60">
                                {Languages.map((language) => (
                                  <div
                                    key={language.value}
                                    className={cn(
                                      "flex items-center space-x-2 p-2 cursor-pointer hover:bg-accent rounded-sm",
                                      selected.includes(language.value) &&
                                        "bg-accent",
                                    )}
                                    onClick={() => {
                                      const newSelected = selected.includes(
                                        language.value,
                                      )
                                        ? selected.filter(
                                            (l) => l !== language.value,
                                          )
                                        : [...selected, language.value];
                                      field.onChange(newSelected);
                                    }}
                                  >
                                    <Checkbox
                                      checked={selected.includes(
                                        language.value,
                                      )}
                                      onCheckedChange={(checked) => {
                                        const newSelected = checked
                                          ? [...selected, language.value]
                                          : selected.filter(
                                              (l) => l !== language.value,
                                            );
                                        field.onChange(newSelected);
                                      }}
                                      className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                                    />
                                    <span>{language.label}</span>
                                  </div>
                                ))}
                              </ScrollArea>
                            </SelectContent>
                          </Select>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  );
                }}
              />

              {/* Gender */}
              <FormField
                control={form.control}
                name="gender"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel className="font-semibold text-gray-800">
                      Gender
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <GenderIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                        >
                          <SelectTrigger
                            className={cn(
                              "pl-10 h-10",
                              !field.value && "text-muted-foreground",
                              fieldState.error && "border-red-500",
                            )}
                          >
                            <SelectValue placeholder="Select Gender" />
                          </SelectTrigger>
                          <SelectContent>
                            {Genders.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Address */}
              <FormField
                control={form.control}
                name="address"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel className="font-semibold text-gray-800">
                      Address
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <AddressIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                        <Input
                          {...field}
                          type="text"
                          placeholder="Address"
                          className="pl-10 h-10"
                          hasError={!!fieldState.error}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Country */}
              <FormField
                control={form.control}
                name="country"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel className="font-semibold text-gray-800">
                      Country
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <LanguageIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary z-10" />
                        <Select
                          onValueChange={field.onChange}
                          value={field.value}
                          hasError={!!fieldState.error}
                        >
                          <SelectTrigger
                            className={cn(
                              "pl-10 h-10 text-[14px] font-bold",
                              !field.value
                                ? "text-[#1E1E1E80]"
                                : "text-[#1E1E1E]", // 50% opacity when no selection
                              fieldState.error && "border-red-500",
                            )}
                            hasError={!!fieldState.error}
                          >
                            <SelectValue placeholder="Select Country">
                              {field.value
                                ? getCountryLabel(field.value)
                                : "Select Country"}
                            </SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            <ScrollArea className="h-60">
                              {Countries.map((country) => (
                                <SelectItem
                                  key={country.value}
                                  value={country.value}
                                >
                                  {country.label}
                                </SelectItem>
                              ))}
                            </ScrollArea>
                          </SelectContent>
                        </Select>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Medical License Number */}
              <FormField
                control={form.control}
                name="medicalLicense"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel className="font-semibold text-gray-800">
                      Medical License Number
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <FileIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                        <Input
                          {...field}
                          type="text"
                          placeholder="Medical License Number"
                          className="pl-10 h-10"
                          hasError={!!fieldState.error}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Years of Experience */}
              <FormField
                control={form.control}
                name="experience"
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormLabel className="font-semibold text-gray-800">
                      Years of Experience
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <ExpierienceIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                        <Input
                          {...field}
                          type="number"
                          placeholder="Total Years of Experience"
                          className={cn(
                            "pl-10 h-10",
                            field.value === 0
                              ? "text-[#1E1E1E80]"
                              : "text-[#1E1E1E]",
                            fieldState.error && "border-red-500",
                          )}
                          onChange={(e) =>
                            field.onChange(Number.parseInt(e.target.value) || 0)
                          }
                          hasError={!!fieldState.error}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Bio */}
              <FormField
                control={form.control}
                name="bio"
                render={({ field, fieldState }) => (
                  <FormItem className="md:col-span-2">
                    <FormLabel className="font-semibold text-gray-800">
                      Bio
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        {...field}
                        placeholder="Write a short professional bio"
                        className="min-h-[120px] resize-y"
                        hasError={!!fieldState.error}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Form Submission Buttons */}
            <div className="flex justify-end gap-4 pt-4">
              <Button
                variant="outline"
                size="lg"
                type="button"
                onClick={onClose}
                disabled={isSubmitting}
                className="px-6"
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                type="submit"
                size="lg"
                disabled={isSubmitting}
                className="px-8"
              >
                {isSubmitting ? "Submitting..." : "Next"}
              </Button>
            </div>
          </form>
        </Form>

        {/* Password Change Modal */}
        {showPasswordModal && (
          <PasswordChangeModal
            isOpen={showPasswordModal}
            onClose={() => setShowPasswordModal(false)}
          />
        )}
      </div>
    </div>
  );
};

export default PersonalInformationModal;
