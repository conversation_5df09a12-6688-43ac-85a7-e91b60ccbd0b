import React from "react";
import { useState, useEffect } from "react";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "../../../../components/ui/select";
import { X, Info } from "lucide-react";
import { doctorSpecialties } from "../../../../constants/doctorSpecialties";
import { Button } from "../../../../components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "../../../../components/ui/alert-dialog";

const SpecializationModal = ({ specializations, onSaving, onClose }) => {
  const allSpecializations = doctorSpecialties.map((item) => item.name);
  const [currentSelection, setCurrentSelection] = useState("");
  const [isEditing, setIsEditing] = useState(false);
  const [editIndex, setEditIndex] = useState(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [specialtyToDelete, setSpecialtyToDelete] = useState(null);
  const [isSaving] = useState(false);
  const [tempSpecializations, setTempSpecializations] = useState([]);

  // Initialize temporary specializations with current specializations
  useEffect(() => {
    setTempSpecializations([...specializations]);
  }, [specializations]);

  const handleSelectionChange = (selectedValue) => {
    if (!selectedValue) return;

    if (
      tempSpecializations.length < 2 &&
      !tempSpecializations.includes(selectedValue)
    ) {
      setTempSpecializations([...tempSpecializations, selectedValue]);
      setCurrentSelection("");
    }
  };

  const handleRemoveSpecialization = (index) => {
    const updated = tempSpecializations.filter((_, i) => i !== index);
    setTempSpecializations(updated);

    if (isEditing && editIndex === index) {
      setIsEditing(false);
      setEditIndex(null);
      setCurrentSelection("");
    }
  };

  const handleCancel = () => {
    // Discard changes and close the modal
    if (onClose) {
      onClose();
    }
  };

  const renderSpecializations = () => {
    return tempSpecializations.map((spec, index) => (
      <div
        key={index}
        className={`flex items-center gap-2 px-3 py-1 rounded-full border-[1px] border-solid ${
          isEditing && editIndex === index
            ? "border-blue-500 bg-blue-50"
            : "border-border"
        }`}
      >
        <span>{spec}</span>
        <div
          className="h-[24px] w-[24px] flex items-center justify-center bg-[#1E1E1E80] rounded-full cursor-pointer"
          onClick={() => handleRemoveSpecialization(index)}
        >
          <X className="w-4 h-4 text-white" />
        </div>
      </div>
    ));
  };

  return (
    <div className="border border-solid border-border rounded-lg p-6">
      <div className="grid grid-cols-1 gap-6">
        <div>
          <Select
            onValueChange={handleSelectionChange}
            value={currentSelection}
            disabled={tempSpecializations.length >= 2 || isSaving}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select specialty" />
            </SelectTrigger>
            <SelectContent>
              {allSpecializations
                .filter((spec) => !tempSpecializations.includes(spec))
                .map((spec, index) => (
                  <SelectItem key={index} value={spec}>
                    {spec}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>

          <p className="text-gray-500 flex items-center mt-2 text-[14px] text-[#1E1E1E80]">
            <Info className="w-4 h-4 mr-1" /> You can add a maximum of two
            specializations
          </p>
        </div>
      </div>

      <div className="flex flex-wrap gap-2 mt-4">{renderSpecializations()}</div>

      <div className="flex justify-end gap-2 mt-6">
        <Button variant="outline" onClick={handleCancel} disabled={isSaving}>
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={() => onSaving(tempSpecializations)}
          disabled={isSaving}
        >
          {isSaving ? "Saving..." : "Save Changes"}
        </Button>
      </div>

      <AlertDialog
        open={isDeleteDialogOpen}
        onOpenChange={setIsDeleteDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will remove the specialty from your profile. This action
              cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => {
                if (specialtyToDelete !== null) {
                  handleRemoveSpecialization(specialtyToDelete);
                  setSpecialtyToDelete(null);
                }
              }}
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default SpecializationModal;
