import React from "react";
import { useState, useEffect } from "react";
import { useForm, FormProvider } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Input } from "../../../../components/ui/input";
import {
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "../../../../components/ui/form";
import { ReactComponent as ServicesIcon } from "../../../../assets/svgs/ServicesIcon.svg";
import { ReactComponent as PriceIcon } from "../../../../assets/svgs/PriceIcon.svg";
import { Button } from "../../../../components/ui/button";
import { useToast } from "../../../../hooks/use-toast";

const serviceSchema = z.object({
  serviceName: z.string().min(1, "Service name is required"),
  price: z.coerce
    .number({ invalid_type_error: "Price must be a number" })
    .gt(0, "Price must be more than zero"),
});

const ServicesAndPriceModal = ({
  services,
  setServices,
  selectedService = null,
  onClose,
  onSaving,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  const methods = useForm({
    resolver: zodResolver(serviceSchema),
    defaultValues: {
      serviceName: "",
      price: "",
    },
  });

  const {
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = methods;

  useEffect(() => {
    if (selectedService) {
      setValue("serviceName", selectedService.serviceName);
      setValue("price", selectedService.price);
      setIsEditing(true);
    } else {
      reset({
        serviceName: "",
        price: "",
      });
      setIsEditing(false);
    }
  }, [selectedService, setValue, reset]);

  const onSubmit = async (data) => {
    // Just pass the data to parent component, no local logic
    if (onSaving) {
      onSaving(data);
    }
  };

  const handleCancel = () => {
    if (onClose) {
      onClose();
    }
  };

  return (
    <div className="border border-solid border-border p-4 rounded-lg">
      <FormProvider {...methods}>
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-end">
            <FormField
              control={control}
              name="serviceName"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="relative">
                      <ServicesIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary z-10" />
                      <Input
                        {...field}
                        placeholder="Enter service name"
                        className="pl-10"
                      />
                    </div>
                  </FormControl>
                  {errors.serviceName && (
                    <FormMessage>{errors.serviceName.message}</FormMessage>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="price"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className="relative">
                      <PriceIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary z-10" />
                      <Input
                        type="number"
                        {...field}
                        placeholder="Enter price"
                        className="pl-10"
                        onChange={(e) =>
                          field.onChange(
                            e.target.value === "" ? "" : Number(e.target.value),
                          )
                        }
                      />
                    </div>
                  </FormControl>
                  {errors.price && (
                    <FormMessage>{errors.price.message}</FormMessage>
                  )}
                </FormItem>
              )}
            />
          </div>

          <div className="flex justify-end gap-2 mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSaving}
            >
              Cancel
            </Button>
            <Button type="submit" variant="primary" disabled={isSaving}>
              {isSaving ? "Saving..." : isEditing ? "Update" : "Add"} Service
            </Button>
          </div>
        </form>
      </FormProvider>
    </div>
  );
};

export default ServicesAndPriceModal;
