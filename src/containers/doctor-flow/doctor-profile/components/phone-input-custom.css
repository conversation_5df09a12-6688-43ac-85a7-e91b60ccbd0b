/* Custom styles for react-phone-input-2 to match your UI */

.custom-phone-input-wrapper {
  position: relative;
}

.custom-phone-container {
  width: 100%;
}

.custom-phone-input {
  width: 100% !important;
  height: 40px !important; /* h-10 */
  /* padding-left: 40px !important; pl-10 */
  border-radius: 0.375rem !important; /* rounded-md */
  border: 1px solid #e2e8f0 !important; /* border-input */
  background-color: #fff !important; /* bg-background */
  font-size: 14px !important;
  font-weight: 700 !important;
  color: #1e1e1e !important;
  transition: all 0.2s ease-in-out !important;
}

.custom-phone-input:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(0, 82, 253, 0.2) !important; /* focus:ring-2 focus:ring-primary/20 */
  border-color: #0052fd !important; /* border-primary */
}

.custom-phone-input::placeholder {
  color: rgba(30, 30, 30, 0.5) !important; /* text-[#1E1E1E80] */
  font-size: 14px !important;
  font-weight: bold !important;
}

.phone-input-error {
  border-color: #ef4444 !important; /* border-red-500 */
  box-shadow: 0 0 0 2px rgba(239, 68, 68, 0.2) !important; /* focus:ring-red-200 */
}

.custom-phone-dropdown-button {
  background: transparent !important;
  border: none !important;
  border-right: 1px solid #e2e8f0 !important;
  padding: 0 !important;
  position: absolute !important;
  left: 0 !important;
  top: 0 !important;
  height: 100% !important;
  width: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1000;
}

.custom-phone-dropdown-button .selected-flag {
  background: transparent !important;
  padding: 0 !important;
  width: 30px !important;
  height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.custom-phone-dropdown-button .selected-flag .flag {
  transform: scale(1.2) !important;
}

.custom-phone-dropdown-button .selected-flag .arrow {
  left: auto !important;
  right: 0 !important;
  border-top: 4px solid #0052fd !important;
  display: none;
}

.custom-phone-dropdown {
  margin-top: 5px !important;
  border-radius: 0.375rem !important;
  border: 1px solid #e2e8f0 !important;
  box-shadow:
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  max-height: 300px !important;
  width: 300px !important;
  z-index: 1000;
}

.custom-phone-dropdown .country {
  /* padding: 8px 10px !important; */
  display: flex !important;
  align-items: center !important;
}

.custom-phone-dropdown .country:hover {
  /* background-color: #f7f9fc !important; */
}

.custom-phone-dropdown .country-name {
  font-size: 14px !important;
  /* margin-left: 8px !important; */
}

.custom-phone-dropdown .dial-code {
  color: #6b7280 !important;
  font-size: 12px !important;
}

.custom-phone-search {
  padding: 10px !important;
  border-bottom: 1px solid #e2e8f0 !important;
}

.custom-phone-search input {
  width: 100% !important;
  padding: 8px 10px !important;
  border-radius: 0.25rem !important;
  border: 1px solid #e2e8f0 !important;
  font-size: 14px !important;
}

.custom-phone-search input:focus {
  outline: none !important;
  border-color: #0052fd !important;
  box-shadow: 0 0 0 2px rgba(0, 82, 253, 0.2) !important;
}

/* Fix for the icon positioning */
.custom-phone-input-wrapper .react-tel-input .form-control {
  /* padding-left: 48px !important; */
  background-color: aqua;
}

.custom-phone-input-wrapper .flag-dropdown {
  /* left: 20px !important; */
}
