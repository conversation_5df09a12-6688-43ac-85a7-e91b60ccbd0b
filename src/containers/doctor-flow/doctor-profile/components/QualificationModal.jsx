import React from "react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { <PERSON><PERSON> } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "../../../../components/ui/form";
import { ReactComponent as InstituteIcon } from "../../../../assets/svgs/InstituteIcon.svg";
import { ReactComponent as DegreeIcon } from "../../../../assets/svgs/DegreeIcon.svg";
import { useToast } from "../../../../hooks/use-toast";
import DatePicker from "../../../../components/date-picker/DatePicker";

const qualificationSchema = z
  .object({
    institutionName: z
      .string()
      .min(1, "Institute Name is required")
      .regex(
        /^[a-zA-Z\s.,'-]+$/,
        "Institute Name should only contain letters and spaces",
      ),
    degreeName: z
      .string()
      .min(1, "Degree is required")
      .regex(
        /^[a-zA-Z\s.,'-]+$/,
        "Degree should only contain letters and spaces",
      ),
    startDate: z.string().min(1, "Start date is required"),
    endDate: z.string().min(1, "End date is required"),
    degreeFile: z
      .any()
      .refine((file) => file !== null, "Degree image is required"),
  })
  .refine(
    (data) => {
      const start = new Date(data.startDate);
      const end = new Date(data.endDate);
      return start < end;
    },
    {
      message: "Start date must be before end date",
      path: ["startDate"],
    },
  )
  .refine(
    (data) => {
      const start = new Date(data.startDate);
      const end = new Date(data.endDate);
      return end > start;
    },
    {
      message: "End date must be after start date",
      path: ["endDate"],
    },
  );

const QualificationModal = ({ qualification, onSave, onClose }) => {
  const [uploadedImage, setUploadedImage] = useState(null);
  const { toast } = useToast();

  const form = useForm({
    resolver: zodResolver(qualificationSchema),
    defaultValues: qualification || {
      institutionName: "",
      degreeName: "",
      startDate: "",
      endDate: "",
      degreeFile: null,
    },
  });

  const {
    formState: { errors },
    watch,
  } = form;

  const formValues = watch();

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const validTypes = ["image/jpeg", "image/jpg", "image/png"];
      if (!validTypes.includes(file.type)) {
        toast({
          variant: "destructive",
          title: "Invalid file type",
          description: "Please upload only JPEG or PNG images",
        });
        return;
      }
      if (file.size > 5 * 1024 * 1024) {
        toast({
          variant: "destructive",
          title: "File too large",
          description: "Image size should not exceed 5MB",
        });
        return;
      }
      setUploadedImage(file);
      form.setValue("degreeFile", file, {
        shouldValidate: true,
        shouldDirty: true,
      });
    }
  };

  const handleCancel = () => {
    form.reset();
    setUploadedImage(null);
    onClose();
  };

  const renderPreview = (file) => {
    return file ? (
      <img
        src={file.url ? file.url : URL.createObjectURL(file)}
        alt="Preview"
        className="h-full w-full object-cover rounded-lg"
      />
    ) : (
      <div className="flex flex-col items-center justify-center pt-5 pb-6">
        <svg
          className="w-8 h-8 mb-4 text-primary"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 20 16"
          aria-hidden="true"
        >
          <path
            stroke="currentColor"
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M13 13h3a3 3 0 0 0 0-6h-.025A5.56 5.56 0 0 0 16 6.5 5.5 5.5 0 0 0 5.207 5.021C5.137 5.017 5.071 5 5 5a4 4 0 0 0 0 8h2.167M10 15V6m0 0L8 8m2-2 2 2"
          />
        </svg>
        <p className="mb-2 text-sm text-gray-500">
          <span className="font-semibold text-primary">Drag and drop</span> your
          file here, or{" "}
          <span className="font-semibold text-primary">click</span>
        </p>
        <p className="text-xs text-gray-500">JPEG or PNG (MAX. 5MB)</p>
      </div>
    );
  };

  useEffect(() => {
    if (qualification) {
      form.reset(qualification);
      setUploadedImage(qualification.degreeFile || null);
      form.setValue("degreeFile", qualification.degreeFile || null);
    }
  }, [qualification]);

  const handleSubmit = async (data) => {
    try {
      const payload = {
        ...(qualification?.id ? { id: qualification.id } : {}),
        ...data,
      };
      await onSave(payload, uploadedImage);

      toast({
        title: "Success",
        description: "Qualification saved successfully",
      });
      form.reset();
      setUploadedImage(null);
      onClose();
      // eslint-disable-next-line
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to save qualification",
      });
    }
  };

  const isButtonDisabled = !(
    formValues.institutionName &&
    formValues.degreeName &&
    formValues.startDate &&
    formValues.endDate &&
    formValues.degreeFile
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[80vh] overflow-y-auto transform transition-transform duration-300 translate-x-0 opacity-100">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(handleSubmit)}
            className="border border-solid border-border p-4 rounded-lg"
          >
            {/* Institute Name */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="institutionName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Institute Name</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <InstituteIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                        <Input
                          {...field}
                          placeholder="Enter Institute Name"
                          className="pl-10"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Degree */}
              <FormField
                control={form.control}
                name="degreeName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Degree</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <DegreeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                        <Input
                          {...field}
                          placeholder="Enter Degree Name"
                          className="pl-10"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Start From - Using the reusable DatePicker */}
              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <DatePicker
                    label="Start From"
                    value={field.value}
                    onChange={field.onChange}
                    name="startDate"
                    error={errors.startDate?.message}
                  />
                )}
              />

              {/* End In - Using the reusable DatePicker */}
              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <DatePicker
                    label="End In"
                    value={field.value}
                    onChange={field.onChange}
                    name="endDate"
                    error={errors.endDate?.message}
                  />
                )}
              />
            </div>

            {/* Add Degree Image */}
            <div className="w-full my-6">
              <FormField
                control={form.control}
                name="degreeFile"
                render={() => (
                  <FormItem>
                    <FormLabel>Degree Image</FormLabel>
                    <FormControl>
                      <label
                        htmlFor="front-card"
                        className="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 dark:border-gray-600"
                        onDragOver={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                        onDragEnter={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                        onDragLeave={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                        }}
                        onDrop={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          const file = e.dataTransfer.files[0];
                          if (file) {
                            handleImageUpload({ target: { files: [file] } });
                          }
                        }}
                      >
                        {renderPreview(uploadedImage)}
                        <input
                          type="file"
                          id="front-card"
                          className="hidden"
                          accept=".jpeg,.jpg,.png"
                          onChange={(e) => handleImageUpload(e)}
                        />
                      </label>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {uploadedImage && (
                <p className="mt-2 text-sm text-green-600">
                  Uploaded: {uploadedImage.name}
                </p>
              )}
            </div>

            <div className="mt-8 flex justify-end gap-4">
              <Button variant="secondary" type="button" onClick={handleCancel}>
                Cancel
              </Button>
              <Button
                variant="primary"
                type="submit"
                disabled={isButtonDisabled}
              >
                Save & Next
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default QualificationModal;
