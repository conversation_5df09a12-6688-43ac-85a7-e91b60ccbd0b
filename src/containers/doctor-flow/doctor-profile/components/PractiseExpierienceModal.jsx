import React from "react";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "../../../../hooks/use-toast";

import { Button } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { Textarea } from "../../../../components/ui/textarea";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "../../../../components/ui/form";
import { ReactComponent as HospitalIcon } from "../../../../assets/svgs/InstituteIcon.svg";
import { ReactComponent as ProfileIcon } from "../../../../assets/svgs/ProfileIcon.svg";
import DatePicker from "../../../../components/date-picker/DatePicker";

const formSchema = z
  .object({
    hospitalName: z
      .string()
      .min(1, "Hospital name is required")
      .regex(
        /^[a-zA-Z\s.,'-]+$/,
        "Hospital name should only contain letters and spaces",
      ),
    designation: z
      .string()
      .min(1, "Designation is required")
      .regex(
        /^[a-zA-Z\s.,'-]+$/,
        "Designation should only contain letters and spaces",
      ),
    startDate: z.string().min(1, "Start date is required"),
    endDate: z.string().min(1, "End date is required"),
    description: z
      .string()
      .min(1, "Description is required")
      .min(20, "Description should atleast 20 characters")
      .max(200, "Description should max 200 characters"),
  })
  .refine(
    (data) => {
      return new Date(data.startDate) <= new Date(data.endDate);
    },
    {
      message: "Start date must be before or equal to end date",
      path: ["startDate"],
    },
  )
  .refine(
    (data) => {
      return new Date(data.endDate) >= new Date(data.startDate);
    },
    {
      message: "End date must be after or equal to start date",
      path: ["endDate"],
    },
  );
const PractiseExpierienceModal = ({ initialData, onSave, onClose }) => {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      hospitalName: "",
      designation: "",
      startDate: "",
      description: "",
      endDate: "",
    },
  });
  const [certificateFile, setCertificateFile] = useState(null);
  const [certificatePreview, setCertificatePreview] = useState(null);
  const [certificateError, setCertificateError] = useState("");

  const {
    formState: { dirtyFields },
    getValues,
  } = form;

  useEffect(() => {
    if (initialData) {
      form.reset(initialData);
      if (initialData.experienceFile) {
        setCertificateFile(initialData.experienceFile);
        if (initialData.experienceFile instanceof File) {
          const reader = new FileReader();
          reader.onload = () => setCertificatePreview(reader.result);
          reader.readAsDataURL(initialData.experienceFile);
        } else {
          setCertificatePreview(initialData.experienceFile.url);
        }
      }
    }
  }, [initialData, form]);

  const handleFileChange = (e) => {
    const file = e.target.files?.[0] || null;
    if (file) {
      const validTypes = ["image/jpeg", "image/jpg", "image/png"];
      if (!validTypes.includes(file.type)) {
        toast({
          variant: "destructive",
          title: "Invalid file type",
          description: "Please upload only JPEG or PNG images",
        });
        return;
      }

      setCertificateFile(file);
      setCertificateError("");
      const reader = new FileReader();
      reader.onload = () => setCertificatePreview(reader.result);
      reader.readAsDataURL(file);
    } else {
      setCertificatePreview(null);
      setCertificateFile(null);
      setCertificateError("");
    }
  };

  const hasChanges = () => {
    if (!initialData) return true;
    return (
      Object.keys(dirtyFields).length > 0 ||
      (certificateFile !== initialData.experienceFile &&
        !(certificateFile === null && !initialData.experienceFile))
    );
  };

  const isSubmitDisabled = () => {
    const values = getValues();
    const requiredFieldsEmpty =
      !values.hospitalName ||
      !values.designation ||
      !values.startDate ||
      !values.endDate ||
      !values.description ||
      !certificateFile;

    if (!initialData) {
      return requiredFieldsEmpty;
    }

    return requiredFieldsEmpty || !hasChanges();
  };

  const onSubmit = (data) => {
    if (!certificateFile) {
      setCertificateError("Certificate image is required");
      return;
    }

    onSave({
      ...(initialData?.id ? { id: initialData.id } : {}),
      ...data,
      experienceFile: certificateFile,
    });

    toast({
      title: "Success",
      description: `Experience ${initialData ? "updated" : "added"} successfully`,
    });

    onClose();
  };

  const handleClose = () => {
    if (certificatePreview) {
      URL.revokeObjectURL(certificatePreview);
    }
    setCertificateFile(null);
    setCertificatePreview(null);
    setCertificateError("");
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[80vh] overflow-y-auto">
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="border border-solid border-border p-4 rounded-lg"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="hospitalName"
                render={({ field }) => (
                  <FormItem className="mb-4">
                    <FormLabel>Hospital Name</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <HospitalIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                        <Input
                          {...field}
                          placeholder="Enter hospital name"
                          className="pl-10"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="designation"
                render={({ field }) => (
                  <FormItem className="mb-4">
                    <FormLabel>Designation</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <ProfileIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary" />
                        <Input
                          {...field}
                          placeholder="Enter designation"
                          className="pl-10"
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="startDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Date</FormLabel>
                    <DatePicker field={field} />
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Date</FormLabel>
                    <DatePicker field={field} />
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem className="md:col-span-2 mt-4">
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      {...field}
                      placeholder="Describe your experience..."
                      className="min-h-9"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="my-4">
              <label
                className="flex flex-col items-center justify-center w-full h-64 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100"
                onDragOver={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onDragEnter={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onDragLeave={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onDrop={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  const files = e.dataTransfer.files;
                  if (files && files.length > 0) {
                    const file = files[0];
                    const validTypes = ["image/jpeg", "image/jpg", "image/png"];
                    if (!validTypes.includes(file.type)) {
                      toast({
                        variant: "destructive",
                        title: "Invalid file type",
                        description: "Please upload only JPEG or PNG images",
                      });
                      return;
                    }

                    setCertificateFile(file);
                    setCertificateError("");
                    const reader = new FileReader();
                    reader.onload = () => setCertificatePreview(reader.result);
                    reader.readAsDataURL(file);
                  }
                }}
              >
                {certificatePreview ? (
                  <img
                    src={certificatePreview || "/placeholder.svg"}
                    alt="Certificate preview"
                    className="h-full w-full object-contain rounded-lg"
                  />
                ) : (
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <svg
                      className="w-8 h-8 mb-4 text-primary"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                      />
                    </svg>
                    <p className="mb-2 text-sm text-gray-500">
                      <span className="font-semibold text-primary">
                        Click to upload
                      </span>
                    </p>
                    <p className="text-xs text-gray-500">PNG, JPG (MAX. 5MB)</p>
                  </div>
                )}
                <input
                  type="file"
                  className="hidden"
                  accept=".jpeg,.jpg,.png"
                  onChange={handleFileChange}
                />
              </label>
              <p className="mt-2 text-sm text-gray-500">
                Upload a certificate or document verifying your experience.
              </p>
              {certificateFile && (
                <p className="mt-2 text-sm text-green-600">
                  {certificateFile.name || "Certificate uploaded"}
                </p>
              )}
              {certificateError && (
                <p className="mt-2 text-sm text-destructive">
                  {certificateError}
                </p>
              )}
            </div>

            <div className="flex justify-end gap-4">
              <Button variant="secondary" type="button" onClick={handleClose}>
                Cancel
              </Button>
              <Button
                variant="primary"
                type="submit"
                disabled={isSubmitDisabled()}
              >
                {initialData ? "Update" : "Save"} Experience
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default PractiseExpierienceModal;
