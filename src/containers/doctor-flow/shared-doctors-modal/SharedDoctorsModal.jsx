import React from "react";
import { useSelector } from "react-redux";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "../../../components/ui/dialog";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "../../../components/ui/avatar";
import { EyeIcon } from "lucide-react";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";

const SharedDoctorsModal = ({
  isOpen,
  onClose,
  doctors = [],
  viewDoctorProfile,
}) => {
  // Get logged-in user from Redux store
  const loggedInUser = useSelector((state) => state.userReducer.user);
  const loggedInDoctorId = loggedInUser?.id;
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] p-0 bg-white rounded-2xl border-0 shadow-2xl">
        {/* Header */}
        <DialogHeader className="px-6 py-6 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-2xl font-semibold text-[#1E1E1E] flex items-center gap-2">
              Doctors
            </DialogTitle>
          </div>
        </DialogHeader>

        {/* Content */}
        <div className="px-6 py-4 max-h-[500px] overflow-y-auto">
          <div className="space-y-4">
            {doctors && doctors.length > 0 ? (
              doctors.map((doctor) => {
                const isLoggedInDoctor = doctor.id === loggedInDoctorId;
                const doctorName =
                  doctor.name ||
                  `${doctor?.firstName || ""} ${doctor?.lastName || ""}`.trim() ||
                  "N/A";
                const displayName = isLoggedInDoctor
                  ? `${doctorName} (me)`
                  : doctorName;

                return (
                  <div
                    key={doctor.id}
                    className="flex items-center justify-between p-4 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center gap-4">
                      <Avatar className="w-12 h-12 border-2 border-blue-600">
                        <AvatarImage
                          src={
                            doctor?.profilePicture?.url ||
                            doctor.avatar ||
                            DefaultAvatar
                          }
                          alt={`${doctor?.firstName || ""} ${doctor?.lastName || ""}`}
                        />
                        <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold">
                          {`${doctor?.firstName?.[0] || ""}${doctor?.lastName?.[0] || ""}`}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold text-gray-900 text-lg">
                          {displayName}
                        </h3>
                        <p className="text-gray-600 text-sm">
                          {doctor?.qualifications?.length > 0
                            ? doctor.qualifications
                                .map((qual) => qual.degreeName)
                                .join(", ")
                            : doctor?.speciality?.join(", ") || "N/A"}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      {!isLoggedInDoctor && (
                        <EyeIcon
                          className="h-6 w-6 text-blue-600 cursor-pointer hover:text-blue-800 transition-colors"
                          onClick={() => viewDoctorProfile(doctor)}
                        />
                      )}
                    </div>
                  </div>
                );
              })
            ) : (
              <div className="text-center py-8 text-gray-500">
                No doctors found.
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SharedDoctorsModal;
