import React from "react";
import TotalAppointmentsChart from "../../../components/doctor-side-components/charts/total-appointments-chart/TotalAppointmentsChart";
import AppointmentSummaryChart from "../../../components/doctor-side-components/charts/appointment-summary-chart/AppointmentSummaryChart";
// import AppointmentSummaryChart from "../../../components/charts/appointment-summary-chart/AppointmentSummaryChart";
import ListOfPatientTable from "../../../components/doctor-side-components/patients-table/PatientsTable";

import { ReactComponent as TotalEarningIcon } from "../../../assets/svgs/TotalEarningIcon.svg";
import { ReactComponent as TotalOppointments } from "../../../assets/svgs/TotalAppointmentsIcon.svg";
import { ReactComponent as TotalPatientIcon } from "../../../assets/svgs/TotalPatientIcon.svg";
import { ReactComponent as UpcomingAppointments } from "../../../assets/svgs/UpcomingAppoinments.svg";
import { ReactComponent as CompleteAppointments } from "../../../assets/svgs/CompleteAppointments.svg";
import { ReactComponent as CanceledAppointments } from "../../../assets/svgs/CanceledAppointments.svg";

const Dashboard = () => {
  const cardData = [
    {
      id: 1,
      icon: <TotalEarningIcon className="h-auto w-7" />,
      value: "3499",
      label: "Total Earned",
    },
    {
      id: 2,
      icon: <TotalOppointments className="h-auto w-7" />,
      value: "230",
      label: "Total Appointments",
    },
    {
      id: 3,
      icon: <TotalPatientIcon className="h-auto w-7" />,
      value: "210",
      label: "Total Patients",
    },
    {
      id: 4,
      icon: <UpcomingAppointments className="h-auto w-7" />,
      value: "100",
      label: "Upcoming Appointments",
    },
    {
      id: 5,
      icon: <CompleteAppointments className="h-auto w-7" />,
      value: "400",
      label: "Completed Appointment",
    },
    {
      id: 6,
      icon: <CanceledAppointments className="h-auto w-7" />,
      value: "30",
      label: "Cancelled Appointments",
    },
  ];

  return (
    <div className="p-4 ">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {cardData.map((card) => (
          <div
            key={card.id}
            className="flex flex-col items-start justify-between border border-[#E7E8E9] p-4 rounded-xl"
          >
            {card.icon}
            <h1 className="text-black font-bold text-3xl mt-4">{card.value}</h1>
            <h5 className="text-[#1E1E1EB2] font-medium text-base mt-2">
              {card.label}
            </h5>
          </div>
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-12 gap-4 mt-4">
        <div className="md:col-span-8">
          <TotalAppointmentsChart />
        </div>

        <div className="md:col-span-4">
          <AppointmentSummaryChart />
        </div>
      </div>

      <div>
        <ListOfPatientTable />
      </div>
    </div>
  );
};

export default Dashboard;
