import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "../../../components/ui/dialog";
import { Button } from "../../../components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../components/ui/select";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "../../../components/ui/avatar";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";
import { doctorSpecialties } from "../../../constants/doctorSpecialties";
import { ReactComponent as StethoscopeIcon } from "../../../assets/svgs/StethoscopeIcon.svg";
import { ReactComponent as DoctorIcon } from "../../../assets/svgs/DoctorIcon.svg";
import { fetchDoctorsWithFilters_api } from "../../../api/api_calls/doctor_apiCalls";
import { shareEMR_api } from "../../../api/api_calls/emr_apiCalls";
import { toast } from "../../../hooks/use-toast";
import LoadingSpinner from "../../../components/animations/LoadingSpinner";

const SharePatientEMRModal = ({ isOpen, onClose, patient }) => {
  const [selectedSpecialty, setSelectedSpecialty] = useState("");
  const [selectedDoctor, setSelectedDoctor] = useState(null);
  const [doctors, setDoctors] = useState([]);
  const [isLoadingDoctors, setIsLoadingDoctors] = useState(false);
  const [isSharingEMR, setIsSharingEMR] = useState(false);

  // Fetch doctors based on selected specialty
  const fetchDoctors = async (specialty) => {
    if (!specialty) {
      setDoctors([]);
      return;
    }

    try {
      setIsLoadingDoctors(true);
      const res = await fetchDoctorsWithFilters_api({
        speciality: [specialty],
        limit: 50, // Get more doctors to have a good selection
      });
      setDoctors(res.data.doctors || []);
    } catch (error) {
      console.error("Error fetching doctors:", error);
      toast({
        description: error.message || "Failed to fetch doctors",
        variant: "destructive",
      });
      setDoctors([]);
    } finally {
      setIsLoadingDoctors(false);
    }
  };

  // Fetch doctors when specialty changes
  useEffect(() => {
    fetchDoctors(selectedSpecialty);
  }, [selectedSpecialty]);

  // Handle share EMR
  const handleShareEMR = async () => {
    if (!selectedDoctor || !patient?.id) {
      toast({
        description:
          "Please select a doctor and ensure patient information is available",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSharingEMR(true);
      await shareEMR_api({
        doctorId: selectedDoctor.id,
        patientId: patient.id,
      });

      toast({
        description: "Patient EMR shared successfully",
        variant: "default",
      });

      resetAndClose();
    } catch (error) {
      console.error("Error sharing EMR:", error);
      toast({
        description: error.message || "Failed to share patient EMR",
        variant: "destructive",
      });
    } finally {
      setIsSharingEMR(false);
    }
  };

  // Reset state when modal closes
  const resetAndClose = () => {
    setSelectedSpecialty("");
    setSelectedDoctor(null);
    setDoctors([]);
    setIsLoadingDoctors(false);
    setIsSharingEMR(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={resetAndClose}>
      <DialogContent className="sm:max-w-[500px] p-0 bg-white rounded-2xl border-0 shadow-2xl">
        {/* Header */}
        <DialogHeader className="px-6 py-6">
          <DialogTitle className="text-2xl font-semibold text-[#1E1E1E]">
            Patient EMR
          </DialogTitle>
          <p className="text-gray-600 mt-2">
            Share Patient EMR Profile. For Internal data exchange only
          </p>
        </DialogHeader>

        {/* Content */}
        <div className="px-6 space-y-6">
          {/* Specialty Dropdown */}
          <Select
            value={selectedSpecialty}
            onValueChange={(value) => {
              setSelectedSpecialty(value);
              setSelectedDoctor(null);
            }}
          >
            <SelectTrigger>
              <div className="flex items-center gap-2">
                <StethoscopeIcon className="w-5 h-5" />
                <SelectValue placeholder="Select specialty" />
              </div>
            </SelectTrigger>
            <SelectContent>
              {doctorSpecialties.map((specialty, index) => (
                <SelectItem key={index} value={specialty.name}>
                  {specialty.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Doctor Dropdown */}
          <Select
            value={selectedDoctor ? selectedDoctor.id : ""}
            onValueChange={(value) => {
              const doctor = doctors.find((d) => d.id === value);
              setSelectedDoctor(doctor);
            }}
            disabled={!selectedSpecialty || isLoadingDoctors}
          >
            <SelectTrigger>
              <div className="flex items-center gap-2">
                <DoctorIcon className="w-5 h-5" />
                {isLoadingDoctors ? (
                  <span className="text-gray-500">Loading doctors...</span>
                ) : (
                  <SelectValue placeholder="Select Doctor" />
                )}
              </div>
            </SelectTrigger>
            <SelectContent
              className={doctors.length > 4 ? "max-h-64 overflow-y-auto" : ""}
            >
              {isLoadingDoctors ? (
                <div className="py-2 px-2 text-gray-500 text-sm flex items-center gap-2">
                  <LoadingSpinner size="sm" />
                  Loading doctors...
                </div>
              ) : doctors.length > 0 ? (
                doctors.map((doctor) => (
                  <SelectItem
                    key={doctor.id}
                    value={doctor.id}
                    className="py-2"
                  >
                    <div className="flex items-center gap-2">
                      <Avatar className="w-7 h-7 border border-blue-600">
                        <AvatarImage
                          src={doctor.profilePicture?.url || DefaultAvatar}
                          alt={`${doctor.firstName} ${doctor.lastName}`}
                        />
                        <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
                          {`${doctor.firstName?.[0] || ""}${doctor.lastName?.[0] || ""}`}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-normal text-start">
                          Dr. {doctor.firstName} {doctor.lastName}
                        </p>
                        <p className="text-xs text-gray-500">{doctor.email}</p>
                      </div>
                    </div>
                  </SelectItem>
                ))
              ) : (
                <div className="py-2 px-2 text-gray-500 text-sm">
                  {selectedSpecialty
                    ? "No doctors found for this specialty"
                    : "Please select a specialty first"}
                </div>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 space-y-3">
          <Button
            onClick={handleShareEMR}
            disabled={!selectedDoctor || isSharingEMR}
            className="w-full"
            variant={"primary"}
          >
            {isSharingEMR ? (
              <div className="flex items-center gap-2">
                <LoadingSpinner size="sm" />
                Sharing...
              </div>
            ) : (
              "Share"
            )}
          </Button>
          <Button
            variant="outline"
            onClick={resetAndClose}
            className="w-full"
            disabled={isSharingEMR}
          >
            Cancel
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SharePatientEMRModal;
