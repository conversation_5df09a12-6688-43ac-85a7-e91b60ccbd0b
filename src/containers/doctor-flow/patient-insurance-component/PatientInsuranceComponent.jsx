import React from "react";
import FadeInSection from "../../../components/animations/FadeInSection";

const PatientInsuranceComponent = ({ patient }) => {
  const insuranceData = patient?.insuranceDetails || {};

  return (
    <FadeInSection delay={0.1}>
      <div className="bg-white rounded-lg border mb-6">
        <div className="p-4 flex justify-between items-center">
          <h2 className="text-lg font-semibold">Insurance Detail</h2>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <p className="text-gray-500">Patient Name</p>
              <p className="font-medium">
                {insuranceData?.patientRegisteredName ||
                  `${patient?.firstName || ""} ${patient?.lastName || ""}`.trim() ||
                  "N/A"}
              </p>
            </div>

            <div>
              <p className="text-gray-500">Location</p>
              <p className="font-medium">
                {insuranceData?.companyLocation || "N/A"}
              </p>
            </div>

            <div>
              <p className="text-gray-500">Insurance Company Name</p>
              <p className="font-medium">
                {insuranceData?.companyName || "N/A"}
              </p>
            </div>

            <div>
              <p className="text-gray-500">Insurance Plan Type</p>
              <p className="font-medium">{insuranceData?.planType || "N/A"}</p>
            </div>

            <div>
              <p className="text-gray-500">Policy Owner Name</p>
              <p className="font-medium">
                {insuranceData?.policyOwner || "N/A"}
              </p>
            </div>

            <div>
              <p className="text-gray-500">Policy Number</p>
              <p className="font-medium">
                {insuranceData?.policyNumber || "N/A"}
              </p>
            </div>

            <div>
              <p className="text-gray-500">Group Number</p>
              <p className="font-medium">
                {insuranceData?.groupNumber || "N/A"}
              </p>
            </div>

            <div className="mt-4">
              <p className="text-gray-600">Co-pay</p>
              <p className="font-medium">
                {insuranceData?.coPay ? `${insuranceData.coPay}%` : "N/A"}
              </p>
            </div>
          </div>

          <div className="mb-6">
            <p className="text-gray-500">Plan Details</p>
            <p className="text-gray-700 mt-1">
              {insuranceData?.planDetails || "N/A"}
            </p>
          </div>

          <div>
            <p className="text-gray-700 font-medium">
              Authorization and Consent:
            </p>

            <div className="mt-4">
              <p className="text-gray-600">
                Do you authorize the release of any medical information
                necessary to process insurance claims?
              </p>
              <p className="font-medium">
                {insuranceData?.consentForMedInfoRelease ? "Yes" : "No"}
              </p>
            </div>

            <div className="mt-4">
              <p className="text-gray-600">
                Do you understand that you will be financially responsible for
                all charges not covered by insurance?
              </p>
              <p className="font-medium">
                {insuranceData?.consentForFinances ? "Yes" : "No"}
              </p>
            </div>
          </div>
        </div>
      </div>
    </FadeInSection>
  );
};

export default PatientInsuranceComponent;
