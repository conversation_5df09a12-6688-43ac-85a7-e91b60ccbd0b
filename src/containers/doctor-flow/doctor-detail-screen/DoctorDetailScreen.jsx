import React, { useState, useEffect } from "react";
import BasicDetailsModal from "../../../components/doctor-side-components/basic-details-modal/BasicDetailsModal";
import QualificationDetailsModal from "../../../components/doctor-side-components/advance-details-modal/AdvanceDetailsModal";
import ServiceDetailsModal from "../../../components/doctor-side-components/service-details-modal/ServiceDetailsModal";
import { useNavigate } from "react-router-dom";
import { logOutUser } from "../../../common/logoutUser";
import { Button } from "../../../components/ui/button";
import { ChevronLeft } from "lucide-react";
import DocMobilLogo from "../../../assets/images/DocMobilLogo.png";

const DoctorDetailsScreen = () => {
  const [currentModal, setCurrentModal] = useState("basicDetails");
  const [completedSteps, setCompletedSteps] = useState([]); // Track completed steps
  const navigate = useNavigate();

  useEffect(() => {
    setCurrentModal("basicDetails");
    setCompletedSteps([]); // Reset completed steps on mount
  }, []);

  const steps = [
    { title: "Basic Details", key: "basicDetails" },
    { title: "Advance Details", key: "qualificationDetails" },
    { title: "Service Details", key: "serviceDetails" },
  ];

  const getCurrentStepIndex = () => {
    return steps.findIndex((step) => step.key === currentModal);
  };

  // Function to mark a step as completed
  const markStepAsCompleted = (stepKey) => {
    if (!completedSteps.includes(stepKey)) {
      setCompletedSteps([...completedSteps, stepKey]);
    }
  };

  const handleNavigateToProfileReview = () => {
    markStepAsCompleted("serviceDetails"); // Mark final step as completed
    navigate("/doctor/profileUnderReview");
  };

  const handleBackClick = () => {
    const currentIndex = getCurrentStepIndex();
    if (currentIndex > 0) {
      const previousStep = steps[currentIndex - 1];
      setCurrentModal(previousStep.key);
    }
  };

  // Modify onNext to mark the current step as completed
  const handleNext = (nextStepKey, currentStepKey) => {
    markStepAsCompleted(currentStepKey);
    setCurrentModal(nextStepKey);
  };

  return (
    <div className="inset-0 flex items-center justify-center bg-black bg-opacity-0 z-50">
      <div className="relative bg-white shadow-lg rounded-lg w-full max-w-4xl p-6">
        <div className="w-full items-center justify-between flex">
          <div>
            {currentModal !== "basicDetails" && (
              <Button
                variant={"outline"}
                size={"icon"}
                onClick={handleBackClick}
              >
                <ChevronLeft />
              </Button>
            )}
          </div>

          <img alt="DocMobil Logo" src={DocMobilLogo} className="h-10" />

          <button
            className="text-gray-500 hover:text-gray-700 text-lg h-full"
            onClick={() => {
              logOutUser();
            }}
          >
            ✕
          </button>
        </div>

        <div className="flex items-center justify-center space-x-4 mt-4">
          {steps.map((step, index) => (
            <React.Fragment key={step.key}>
              <div className="flex items-center gap-2">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center 
                      ${
                        getCurrentStepIndex() === index ||
                        completedSteps.includes(step.key)
                          ? "bg-[#0052FD] text-white"
                          : "bg-white text-[#0052FD] border-[#0052FD] border"
                      } font-bold text-sm`}
                >
                  {index + 1}
                </div>
                <span
                  className={`text-sm font-medium ${
                    getCurrentStepIndex() === index ||
                    completedSteps.includes(step.key)
                      ? "text-blue-600"
                      : "text-gray-600"
                  }`}
                >
                  {step.title}
                </span>
              </div>
              {index < steps.length - 1 && (
                <span className="flex items-center text-gray-400 text-2xl mb-3">
                  ............
                </span>
              )}
            </React.Fragment>
          ))}
        </div>

        <div className="w-full">
          {currentModal === "basicDetails" && (
            <BasicDetailsModal
              onClose={logOutUser}
              onNext={() => handleNext("qualificationDetails", "basicDetails")}
            />
          )}

          {currentModal === "qualificationDetails" && (
            <QualificationDetailsModal
              onClose={() => setCurrentModal("basicDetails")}
              onNext={() =>
                handleNext("serviceDetails", "qualificationDetails")
              }
            />
          )}

          {currentModal === "serviceDetails" && (
            <ServiceDetailsModal
              onClose={() => setCurrentModal("qualificationDetails")}
              onSave={handleNavigateToProfileReview}
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default DoctorDetailsScreen;
