import React, { useState } from "react";
import { ReactComponent as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../assets/svgs/DocMobilLogoWithText.svg";
import { HiMail } from "react-icons/hi";
import { <PERSON><PERSON><PERSON><PERSON> } from "react-icons/fa";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, HiMiniEyeSlash } from "react-icons/hi2";
import { <PERSON><PERSON> } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { ReactComponent as GoogleIcon } from "../../../../assets/svgs/GoogleIcon.svg";
import { useNavigate } from "react-router-dom";
import PatientAuthImg from "../../../../assets/images/PateintAithImg.png";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../../components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "../../../../components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { loginPatient_api } from "../../../../api/api_calls/auth_apiCalls";
import { toast } from "../../../../hooks/use-toast";
import { useDispatch } from "react-redux";
import { setUser } from "../../../../redux/slices/userSlice";
import { Checkbox } from "../../../../components/ui/checkbox";
import { Loader2 } from "lucide-react";

const formSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  rememberMe: z.boolean().optional().default(false),
});

const Login = () => {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const plugin = React.useMemo(
    () => Autoplay({ delay: 2000, stopOnInteraction: true }),
    [],
  );

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      const res = await loginPatient_api({
        email: data.email,
        password: data.password,
      });

      if (!res.data || !res.data.token) {
        throw new Error("Invalid response from server");
      }

      dispatch(setUser(res.data.user));
      localStorage.setItem("accessToken", res.data.token);
      toast({
        description: "Login successful",
        variant: "success",
      });
    } catch (error) {
      const errorMessage =
        error.response?.data?.message ||
        error.message ||
        "Login failed. Please try again.";
      toast({
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleForgotPasswordClick = () => {
    navigate("/patient/forgot-password/email");
  };

  return (
    <div className="flex items-center justify-center flex-col w-full h-screen overflow-hidden">
      <div className="flex items-center justify-center w-full h-full">
        <div className="relative w-3/6 h-screen hidden lg:block">
          <div className="absolute inset-0 bg-[#00000066] z-10"></div>
          <img
            alt="Patient Auth Img"
            src={PatientAuthImg}
            className="w-full h-full object-cover absolute inset-0"
          />
          <div className="absolute inset-0 z-20 flex flex-col items-center justify-center">
            <Carousel className="w-full" plugins={[plugin]}>
              <CarouselContent className="w-full h-full">
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Empowering Health with Trusted Care and Expert Insights
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Personalized Health Services Tailored to You
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Comprehensive Care at Your Fingertips
                    </h1>
                  </div>
                </CarouselItem>
              </CarouselContent>
            </Carousel>
          </div>
        </div>

        <div className="flex items-center justify-center sm:w-full lg:w-3/6  h-full overflow-scroll pb-12 ">
          <div className=" w-96">
            <div className="flex w-full items-center justify-center flex-col mt-28">
              <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="flex flex-col w-full gap-4 px-4 shadow-lg p-8 bg-white rounded-lg"
              >
                <div className="flex w-full items-center justify-center flex-col ">
                  <h5 className="font-DMSans text-3xl font-bold text-gray-800 mb-2 antialiased leading-tight tracking-wide">
                    Welcome Back
                  </h5>
                  <h4 className="text-sm text-gray-800/70 mb-4 font-bold antialiased leading-tight">
                    Enter your credentials to log in to your account
                  </h4>
                </div>
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="text-gray-800">Email</FormLabel>
                      <FormControl>
                        <div className="relative w-full">
                          <HiMail
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                            size={18}
                          />
                          <Input
                            type="text"
                            placeholder="Enter your email"
                            className="pl-10"
                            {...field}
                            hasError={!!fieldState.error}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel className="text-gray-800">Password</FormLabel>
                      <FormControl>
                        <div className="relative w-full">
                          <FaKey
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                            size={18}
                          />
                          <Input
                            type={passwordVisible ? "text" : "password"}
                            placeholder="Enter your password"
                            className="pl-10 pr-10"
                            {...field}
                            hasError={!!fieldState.error}
                          />
                          <div
                            onClick={() => setPasswordVisible(!passwordVisible)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-primary"
                          >
                            {passwordVisible ? (
                              <HiMiniEyeSlash size={18} />
                            ) : (
                              <HiMiniEye size={18} />
                            )}
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="rememberMe"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                      <FormControl>
                        <Checkbox
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <div className="space-y-1 leading-none">
                        <FormLabel>Remember me</FormLabel>
                      </div>
                    </FormItem>
                  )}
                />

                <div
                  className="flex items-end justify-start text-[#1E1E1EB2] cursor-pointer"
                  onClick={handleForgotPasswordClick}
                >
                  <h1 className="font-bold antialiased leading-tight">
                    Forgot Password?
                  </h1>
                </div>

                <Button
                  variant="primary"
                  size="fm"
                  className="w-full mt-4"
                  type="submit"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Logging in...
                    </>
                  ) : (
                    "Login"
                  )}
                </Button>

                <div className="flex items-center justify-center w-full mt-2">
                  <div className="flex-grow h-px bg-gray-300"></div>
                  <h3 className="mx-4 text-gray-500 font-bold">OR</h3>
                  <div className="flex-grow h-px bg-gray-300"></div>
                </div>

                <Button
                  variant="social"
                  size="fm"
                  className="w-full flex items-center justify-center gap-2"
                >
                  <GoogleIcon className="w-5 h-5" />
                  Login with Google
                </Button>
              </form>
            </Form>

            <div className="mt-6 text-center">
              <h4 className="text-sm text-[#0D0D0DB2]">
                Don’t have an account?{" "}
                <span
                  className="text-[#0052FD] font-[600] cursor-pointer"
                  onClick={() => navigate("/patient/signup")}
                >
                  {" "}
                  Sign up
                </span>
              </h4>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
