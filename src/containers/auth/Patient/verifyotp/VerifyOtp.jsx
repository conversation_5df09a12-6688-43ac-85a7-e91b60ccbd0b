import React, { useState } from "react";
import { ReactComponent as <PERSON><PERSON><PERSON>il<PERSON><PERSON> } from "../../../../assets/svgs/DocMobilLogoWithText.svg";
import { But<PERSON> } from "../../../../components/ui/button";
import { useNavigate } from "react-router-dom";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "../../../../components/ui/input-otp";
import PatientAuthImg from "../../../../assets/images/PateintAithImg.png";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "../../../../components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";

const VerifyOtp = () => {
  const [otp, setOtp] = useState("");
  const navigate = useNavigate();

  const handleOtpChange = (value) => {
    setOtp(value);
  };

  const handleLoginClick = () => {
    if (otp.length === 4) {
      console.log("OTP Value:", otp);
      navigate("/patient/dashboard");
    } else {
      console.log("Please enter a valid 4-digit OTP.");
    }
  };

  const plugin = React.useRef(
    Autoplay({ delay: 2000, stopOnInteraction: true }),
  );

  return (
    <div className="flex items-center justify-center flex-col w-full h-screen overflow-hidden">
      <div className="flex items-center justify-center w-full h-full">
        <div className="relative w-3/6 h-screen hidden lg:block">
          <div className="absolute inset-0 bg-[#00000066] z-10"></div>
          <img
            alt="Patient Auth Img"
            src={PatientAuthImg}
            className="w-full h-full object-cover absolute inset-0"
          />
          <div className="absolute inset-0 z-20 flex flex-col items-center justify-center">
            <Carousel className="w-full" plugins={[plugin.current]}>
              <CarouselContent className="w-full h-full">
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Empowering Health with Trusted Care and Expert Insights
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Personalized Health Services Tailored to You
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Comprehensive Care at Your Fingertips
                    </h1>
                  </div>
                </CarouselItem>
              </CarouselContent>
            </Carousel>
          </div>
        </div>

        <div className="flex items-center justify-center sm:w-full lg:w-3/6 h-full">
          <div className="w-96">
            <div className="flex w-full items-center justify-center flex-col">
              <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
            </div>

            <div className="flex flex-col w-full gap-4 px-4 shadow-lg p-8 bg-white rounded-lg items-center">
              <h5 className="font-DMSans text-2xl font-bold text-gray-800 mb-2 antialiased leading-tight tracking-wide mt-8 truncate">
                Two Factor Authentication asdasdasdasd
              </h5>
              <h4 className="text-sm text-gray-800/70 mb-8 font-bold antialiased leading-tight">
                Enter the 4 digit authentication code below
              </h4>
              <InputOTP maxLength={4} value={otp} onChange={handleOtpChange}>
                <InputOTPGroup>
                  <InputOTPSlot index={0} />
                  <InputOTPSlot index={1} />
                  <InputOTPSlot index={2} />
                  <InputOTPSlot index={3} />
                </InputOTPGroup>
              </InputOTP>

              <div>
                <h6>
                  Didn’t receive the code?{" "}
                  <span className="text-primary underline">Resend Code</span>
                </h6>
              </div>

              <Button
                variant="primary"
                size="fm"
                className="w-full mt-4"
                onClick={handleLoginClick}
                disabled={otp.length !== 4}
              >
                Login
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerifyOtp;
