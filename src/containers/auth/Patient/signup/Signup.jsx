import React, { useState, useEffect } from "react";
import { ReactComponent as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../assets/svgs/DocMobilLogoWithText.svg";
import { HiMail } from "react-icons/hi";
import { <PERSON>a<PERSON><PERSON> } from "react-icons/fa";
import { <PERSON><PERSON>iniEye, HiMiniEyeSlash } from "react-icons/hi2";
import { Button } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { FaUserCircle } from "react-icons/fa";
import { Checkbox } from "../../../../components/ui/checkbox";
import { useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../../components/ui/form";
import { useToast } from "../../../../hooks/use-toast";
import PatientAuthImg from "../../../../assets/images/PateintAithImg.png";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "../../../../components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { createPatient_api } from "../../../../api/api_calls/patient_apiCalls";
import { Loader2 } from "lucide-react";
import { ReactComponent as GoogleIcon } from "../../../../assets/svgs/GoogleIcon.svg";
import PhoneInputWithCountryCode from "../../../../components/phone-input-with-country-code/PhoneInputWithCountryCode";
import { parsePhoneNumberFromString } from "libphonenumber-js";

const formSchema = z
  .object({
    firstName: z
      .string()
      .min(3, "First name must be at least 3 characters")
      .max(12, "First name must not exceed 12 characters")
      .refine((value) => /^[a-zA-Z\s]+$/.test(value), {
        message: "First name should only contain letters",
      }),
    lastName: z
      .string()
      .min(3, "Last name must be at least 3 characters")
      .max(12, "Last name must not exceed 12 characters")
      .refine((value) => /^[a-zA-Z\s]+$/.test(value), {
        message: "Last name should only contain letters",
      }),
    email: z
      .string()
      .min(1, "Email is required")
      .email("Invalid email address")
      .regex(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        "Must be a valid email address",
      ),
    password: z
      .string()
      .min(6, { message: "Password must be at least 6 characters." }),
    confirmPassword: z.string().min(1, "Please confirm your password"),
    contactNumber: z
      .string()
      .nonempty("Contact number is required")
      .refine(
        (val) => {
          // our phone inputs are saved as digits only, so prepend '+'
          const num = parsePhoneNumberFromString(`+${val}`);
          return num?.isValid() ?? false;
        },
        {
          message: "Phone number is not valid",
        },
      ),
    terms: z.literal(true, {
      errorMap: () => ({
        message: "You must accept the terms and conditions.",
      }),
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

const Signup = () => {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [userCountry, setUserCountry] = useState("us"); // Default country
  const navigate = useNavigate();
  const { toast } = useToast();

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
      contactNumber: "",
      terms: false,
    },
    mode: "onChange",
  });

  useEffect(() => {
    const fetchCountry = async () => {
      try {
        if (navigator.geolocation) {
          const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject);
          });

          const response = await fetch(
            `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${position.coords.latitude}&longitude=${position.coords.longitude}&localityLanguage=en`,
          );
          const data = await response.json();
          const countryCode = data.countryCode?.toLowerCase() || "us";
          setUserCountry(countryCode);
        } else {
          console.warn("Geolocation not supported");
          setUserCountry("us");
        }
      } catch (error) {
        console.error("Error fetching country:", error);
        setUserCountry("us");
      }
    };

    fetchCountry();
  }, []);

  const onSubmit = async (values) => {
    setIsSubmitting(true);
    try {
      // eslint-disable-next-line
      const res = await createPatient_api({
        email: values.email,
        password: values.password,
        firstName: values.firstName,
        lastName: values.lastName,
        contactNumber: values.contactNumber,
      });

      toast({
        description: "Account has been created",
      });

      navigate("/patient/information");
    } catch (error) {
      toast({
        variant: "destructive",
        description: error.message || "Failed to create account",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const plugin = React.useRef(
    Autoplay({ delay: 2000, stopOnInteraction: true }),
  );

  return (
    <div className="flex items-center justify-center flex-col w-full h-screen">
      <div className="flex items-center justify-center w-full h-full">
        <div className="relative w-3/6 h-screen hidden lg:block">
          <div className="absolute inset-0 bg-[#********] z-10"></div>
          <img
            alt="Patient Auth Img"
            src={PatientAuthImg || "/placeholder.svg"}
            className="w-full h-full object-cover absolute inset-0"
          />
          <div className="absolute inset-0 z-20 flex flex-col items-center justify-center">
            <Carousel className="w-full" plugins={[plugin.current]}>
              <CarouselContent className="w-full h-full">
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Empowering Health with Trusted Care and Expert Insights
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Personalized Health Services Tailored to You
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Comprehensive Care at Your Fingertips
                    </h1>
                  </div>
                </CarouselItem>
              </CarouselContent>
            </Carousel>
          </div>
        </div>

        <div className="flex items-center justify-center sm:w-full lg:w-3/6 h-full overflow-auto scrollbar-hide pb-12">
          <div className="w-96">
            <div className="flex w-full items-center justify-center flex-col mt-96">
              <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="flex flex-col w-full gap-4 px-4 shadow-lg p-8 bg-white rounded-lg"
              >
                <h5 className="font-DMSans text-2xl font-bold text-gray-800 antialiased leading-tight tracking-wide truncate text-center">
                  Create an Account
                </h5>
                <h4 className="text-sm text-gray-800/70 font-bold antialiased leading-tight text-center">
                  Let&apos;s get started!
                </h4>

                <FormField
                  control={form.control}
                  name="firstName"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>First Name</FormLabel>
                      <FormControl>
                        <div className="relative w-full">
                          <FaUserCircle
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                            size={18}
                          />
                          <Input
                            {...field}
                            placeholder="Enter your first name"
                            className={`pl-10 transition-all ${
                              fieldState.error
                                ? "border-red-500 focus:ring-red-500/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="lastName"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>Last Name</FormLabel>
                      <FormControl>
                        <div className="relative w-full">
                          <FaUserCircle
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                            size={18}
                          />
                          <Input
                            {...field}
                            placeholder="Enter your last name"
                            className={`pl-10 transition-all ${
                              fieldState.error
                                ? "border-red-500 focus:ring-red-500/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <div className="relative w-full">
                          <HiMail
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                            size={18}
                          />
                          <Input
                            {...field}
                            placeholder="Enter your email"
                            className={`pl-10 transition-all ${
                              fieldState.error
                                ? "border-red-500 focus:ring-red-500/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="password"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>Password</FormLabel>
                      <FormControl>
                        <div className="relative w-full">
                          <FaKey
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                            size={18}
                          />
                          <Input
                            {...field}
                            type={passwordVisible ? "text" : "password"}
                            placeholder="Enter your password"
                            className={`pl-10 transition-all ${
                              fieldState.error
                                ? "border-red-500 focus:ring-red-500/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                          <div
                            onClick={() => setPasswordVisible(!passwordVisible)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-primary"
                          >
                            {passwordVisible ? (
                              <HiMiniEyeSlash size={18} />
                            ) : (
                              <HiMiniEye size={18} />
                            )}
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>Confirm Password</FormLabel>
                      <FormControl>
                        <div className="relative w-full">
                          <FaKey
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                            size={18}
                          />
                          <Input
                            {...field}
                            type={confirmPasswordVisible ? "text" : "password"}
                            placeholder="Confirm your password"
                            className={`pl-10 pr-10 transition-all ${
                              fieldState.error
                                ? "border-red-500 focus:ring-red-500/50"
                                : "focus:ring-primary/50 focus:border-primary"
                            } focus:ring-2`}
                          />
                          <div
                            onClick={() =>
                              setConfirmPasswordVisible(!confirmPasswordVisible)
                            }
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-primary"
                          >
                            {confirmPasswordVisible ? (
                              <HiMiniEyeSlash size={18} />
                            ) : (
                              <HiMiniEye size={18} />
                            )}
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage className="text-red-500 text-xs mt-1" />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactNumber"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormLabel>Contact Number</FormLabel>
                      <FormControl>
                        <PhoneInputWithCountryCode
                          value={field.value}
                          onChange={field.onChange}
                          error={fieldState.error}
                          defaultCountry={userCountry}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="terms"
                  render={({ field, fieldState }) => (
                    <FormItem>
                      <FormControl>
                        <div className="items-top flex space-x-2">
                          <Checkbox
                            id="terms1"
                            checked={field.value}
                            onCheckedChange={field.onChange}
                            className={fieldState.error ? "border-red-500" : ""}
                          />
                          <div className="grid gap-1.5 leading-none">
                            <label
                              htmlFor="terms1"
                              className="text-sm font-medium text-[#1e1e1e] leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              I have read and accept{" "}
                              <span className="text-sm text-muted-foreground text-primary underline">
                                Doctor Disclosure Agreement
                              </span>
                            </label>
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button
                  variant="primary"
                  size="fm"
                  className="w-full mt-4 gap-2"
                  type="submit"
                  disabled={isSubmitting || !form.formState.isValid}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Processing...
                    </>
                  ) : (
                    "Create Account"
                  )}
                </Button>

                <div className="flex items-center justify-center w-full mt-2">
                  <div className="flex-grow h-px bg-gray-300"></div>
                  <h3 className="mx-4 text-gray-500 font-bold">OR</h3>
                  <div className="flex-grow h-px bg-gray-300"></div>
                </div>

                <Button
                  variant="social"
                  size="fm"
                  className="w-full flex items-center justify-center gap-2"
                >
                  <GoogleIcon className="w-5 h-5" />
                  Login with Google
                </Button>
              </form>
            </Form>

            <div className="mt-6 text-center">
              <h4 className="text-sm text-[#0D0D0DB2]">
                Already have an account?{" "}
                <span
                  className="text-[#0052FD] font-[600] cursor-pointer"
                  onClick={() => navigate("/patient/login")}
                >
                  Log In
                </span>
              </h4>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Signup;
