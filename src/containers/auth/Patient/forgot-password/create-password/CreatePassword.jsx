import React, { useState, useEffect, useMemo } from "react";
import { ReactComponent as <PERSON><PERSON>obil<PERSON><PERSON> } from "../../../../../assets/svgs/DocMobilLogoWithText.svg";
import { <PERSON>a<PERSON><PERSON> } from "react-icons/fa";
import { <PERSON><PERSON>ini<PERSON>ye, HiMiniEyeSlash } from "react-icons/hi2";
import { But<PERSON> } from "../../../../../components/ui/button";
import { Input } from "../../../../../components/ui/input";
import { useLocation, useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../../../components/ui/form";
import { toast } from "../../../../../hooks/use-toast";
import { resetPatientPassword_api } from "../../../../../api/api_calls/patient_apiCalls";
import PatientAuthImg from "../../../../../assets/images/PateintAithImg.png";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "../../../../../components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { Loader2 } from "lucide-react";

const passwordSchema = z
  .object({
    newPassword: z
      .string()
      .min(6, "Password must be at least 6 characters long"),

    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

const CreatePassword = () => {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { email, resetPasswordToken } = location.state || {};

  const plugin = useMemo(
    () => Autoplay({ delay: 2000, stopOnInteraction: true }),
    [],
  );

  // Redirect if email or token is missing
  useEffect(() => {
    if (!email || !resetPasswordToken) {
      toast({
        description:
          "Missing information. Please restart the password reset process.",
        variant: "destructive",
      });
      navigate("/patient/forgot-password/email");
    }
  }, [email, resetPasswordToken, navigate]);

  const form = useForm({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
    mode: "onChange", // Validate on change for better UX
  });

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      await resetPatientPassword_api({
        email: email,
        newPassword: data.confirmPassword,
        resetPasswordToken: resetPasswordToken,
      });

      toast({
        description:
          "Password reset successful. You can now login with your new password.",
        variant: "success",
      });

      navigate("/patient/login");
    } catch (error) {
      toast({
        description:
          error.message || "Failed to reset password. Please try again.",
        variant: "destructive",
      });
      console.log("ERROR IN RESET PASSWORD => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center flex-col w-full h-screen overflow-hidden">
      <div className="flex items-center justify-center w-full h-full">
        <div className="relative w-3/6 h-screen hidden lg:block">
          <div className="absolute inset-0 bg-[#00000066] z-10"></div>
          <img
            alt="Patient Auth Img"
            src={PatientAuthImg || "/placeholder.svg"}
            className="w-full h-full object-cover absolute inset-0"
          />
          <div className="absolute inset-0 z-20 flex flex-col items-center justify-center">
            <Carousel className="w-full" plugins={[plugin]}>
              <CarouselContent className="w-full h-full">
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Empowering Health with Trusted Care and Expert Insights
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Personalized Health Services Tailored to You
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Comprehensive Care at Your Fingertips
                    </h1>
                  </div>
                </CarouselItem>
              </CarouselContent>
            </Carousel>
          </div>
        </div>

        <div className="flex items-center justify-center sm:w-full lg:w-3/6 h-full">
          <div className="w-96">
            <div className="flex w-full items-center justify-center flex-col">
              <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
            </div>

            <div className="flex flex-col w-full gap-4 px-4 shadow-lg p-8 bg-white rounded-lg">
              <div className="flex w-full items-center justify-center flex-col">
                <h5 className="font-DMSans text-2xl font-bold text-gray-800 mb-2 antialiased leading-tight tracking-wide">
                  Create New Password
                </h5>
                <h4 className="text-sm text-gray-800/70 mb-4 font-bold antialiased leading-tight text-center">
                  Please enter a new password for your account
                </h4>
              </div>

              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="flex flex-col w-full gap-4"
                >
                  <FormField
                    control={form.control}
                    name="newPassword"
                    render={({ field, fieldState }) => (
                      <FormItem>
                        <FormLabel
                          htmlFor="newPassword"
                          className="text-gray-800"
                        >
                          New Password
                        </FormLabel>
                        <FormControl>
                          <div className="relative w-full">
                            <FaKey
                              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                              size={18}
                              aria-hidden="true"
                            />
                            <Input
                              id="newPassword"
                              type={passwordVisible ? "text" : "password"}
                              placeholder="Enter new password"
                              className={`pl-10 pr-10 transition-all ${
                                fieldState.error
                                  ? "border-red-500 focus:ring-red-500/50"
                                  : "focus:ring-primary/50 focus:border-primary"
                              } focus:ring-2`}
                              {...field}
                              aria-describedby={
                                fieldState.error
                                  ? "newPassword-error"
                                  : undefined
                              }
                            />
                            <button
                              type="button"
                              onClick={() =>
                                setPasswordVisible(!passwordVisible)
                              }
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-primary"
                              aria-label={
                                passwordVisible
                                  ? "Hide password"
                                  : "Show password"
                              }
                            >
                              {passwordVisible ? (
                                <HiMiniEyeSlash size={18} />
                              ) : (
                                <HiMiniEye size={18} />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage id="newPassword-error" />

                        {/* {field.value && (
                          <div className="mt-2">
                            <div className="flex justify-between items-center mb-1">
                              <span className="text-xs">
                                Password Strength:
                              </span>
                              <span
                                className={`text-xs font-medium ${
                                  passwordStrength <= 25
                                    ? "text-red-500"
                                    : passwordStrength <= 50
                                      ? "text-orange-500"
                                      : passwordStrength <= 75
                                        ? "text-yellow-500"
                                        : "text-green-500"
                                }`}
                              >
                                {getStrengthText()}
                              </span>
                            </div>
                            <Progress
                              value={passwordStrength}
                              className="h-1.5"
                              indicatorClassName={getStrengthColor()}
                            />
                            <ul className="text-xs mt-2 space-y-1 text-gray-500">
                              <li
                                className={
                                  /[A-Z]/.test(field.value)
                                    ? "text-green-500"
                                    : ""
                                }
                              >
                                • At least one uppercase letter
                              </li>
                              <li
                                className={
                                  /[a-z]/.test(field.value)
                                    ? "text-green-500"
                                    : ""
                                }
                              >
                                • At least one lowercase letter
                              </li>
                              <li
                                className={
                                  /[0-9]/.test(field.value)
                                    ? "text-green-500"
                                    : ""
                                }
                              >
                                • At least one number
                              </li>
                              <li
                                className={
                                  field.value.length >= 6
                                    ? "text-green-500"
                                    : ""
                                }
                              >
                                • Minimum 6 characters
                              </li>
                            </ul>
                          </div>
                        )} */}
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="confirmPassword"
                    render={({ field, fieldState }) => (
                      <FormItem>
                        <FormLabel
                          htmlFor="confirmPassword"
                          className="text-gray-800"
                        >
                          Confirm Password
                        </FormLabel>
                        <FormControl>
                          <div className="relative w-full">
                            <FaKey
                              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                              size={18}
                              aria-hidden="true"
                            />
                            <Input
                              id="confirmPassword"
                              type={
                                confirmPasswordVisible ? "text" : "password"
                              }
                              placeholder="Confirm your password"
                              className={`pl-10 pr-10 transition-all ${
                                fieldState.error
                                  ? "border-red-500 focus:ring-red-500/50"
                                  : "focus:ring-primary/50 focus:border-primary"
                              } focus:ring-2`}
                              {...field}
                              aria-describedby={
                                fieldState.error
                                  ? "confirmPassword-error"
                                  : undefined
                              }
                            />
                            <button
                              type="button"
                              onClick={() =>
                                setConfirmPasswordVisible(
                                  !confirmPasswordVisible,
                                )
                              }
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-primary"
                              aria-label={
                                confirmPasswordVisible
                                  ? "Hide password"
                                  : "Show password"
                              }
                            >
                              {confirmPasswordVisible ? (
                                <HiMiniEyeSlash size={18} />
                              ) : (
                                <HiMiniEye size={18} />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage id="confirmPassword-error" />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    variant="primary"
                    className="w-full mt-4"
                    disabled={isSubmitting || !form.formState.isValid}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Resetting Password...
                      </>
                    ) : (
                      "Reset Password"
                    )}
                  </Button>
                </form>
              </Form>

              <div className="mt-4 text-center">
                <button
                  className="text-sm text-[#0D0D0DB2] hover:underline"
                  onClick={() => navigate("/patient/login")}
                >
                  Back to Login
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreatePassword;
