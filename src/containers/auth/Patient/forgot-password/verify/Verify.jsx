import React, { useState, useEffect, useRef, useMemo } from "react";
import { ReactComponent as <PERSON><PERSON>obil<PERSON><PERSON> } from "../../../../../assets/svgs/DocMobilLogoWithText.svg";
import { But<PERSON> } from "../../../../../components/ui/button";
import { useLocation, useNavigate } from "react-router-dom";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "../../../../../components/ui/input-otp";
import {
  verifyForgotPatientPasswordOtp_api,
  requestForgotPatientPassword_api,
} from "../../../../../api/api_calls/patient_apiCalls";
import { toast } from "../../../../../hooks/use-toast";
import PatientAuthImg from "../../../../../assets/images/PateintAithImg.png";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "../../../../../components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { Loader2 } from "lucide-react";

const Verify = () => {
  const [otp, setOtp] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState("");
  const navigate = useNavigate();
  const location = useLocation();
  const { email } = location.state || {};
  const inputRef = useRef(null);

  const plugin = useMemo(
    () => Autoplay({ delay: 2000, stopOnInteraction: true }),
    [],
  );

  // Focus first input on mount
  useEffect(() => {
    if (inputRef.current) {
      setTimeout(() => {
        inputRef.current.focus();
      }, 100);
    }
  }, []);

  // Redirect if email is missing
  useEffect(() => {
    if (!email) {
      toast({
        description: "Email information is missing. Please try again.",
        variant: "destructive",
      });
      navigate("/patient/forgot-password/email");
    }
  }, [email, navigate]);

  const handleOtpChange = (value) => {
    setOtp(value);
    setError("");
  };

  const handleVerifyClick = async () => {
    if (otp.length === 4) {
      setIsVerifying(true);
      setError("");
      try {
        const res = await verifyForgotPatientPasswordOtp_api({
          email: email,
          otp: otp,
        });
        toast({
          description: "Verification successful",
          variant: "success",
        });
        navigate("/patient/forgot-password/create-password", {
          state: { email: email, resetPasswordToken: res.data },
        });
      } catch (error) {
        console.error("OTP verification failed:", error);
        setError("Invalid verification code. Please try again.");
        toast({
          description: error.message || "Invalid verification code",
          variant: "destructive",
        });
      } finally {
        setIsVerifying(false);
      }
    }
  };

  const handleResendCode = async () => {
    setResendDisabled(true);
    try {
      await requestForgotPatientPassword_api({
        email: email,
      });

      toast({
        description: "Verification code resent to your email",
      });

      // Start countdown timer (60 seconds)
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      console.error("Failed to resend code:", error);
      setResendDisabled(false);
      toast({
        description: "Failed to resend verification code",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex items-center justify-center flex-col w-full h-screen overflow-hidden">
      <div className="flex items-center justify-center w-full h-full">
        <div className="relative w-3/6 h-screen hidden lg:block">
          <div className="absolute inset-0 bg-[#00000066] z-10"></div>
          <img
            alt="Patient Auth Img"
            src={PatientAuthImg || "/placeholder.svg"}
            className="w-full h-full object-cover absolute inset-0"
          />
          <div className="absolute inset-0 z-20 flex flex-col items-center justify-center">
            <Carousel className="w-full" plugins={[plugin]}>
              <CarouselContent className="w-full h-full">
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Empowering Health with Trusted Care and Expert Insights
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Personalized Health Services Tailored to You
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Comprehensive Care at Your Fingertips
                    </h1>
                  </div>
                </CarouselItem>
              </CarouselContent>
            </Carousel>
          </div>
        </div>

        <div className="flex items-center justify-center sm:w-full lg:w-3/6 h-full">
          <div className="w-96">
            <div className="flex w-full items-center justify-center flex-col">
              <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
            </div>

            <div className="flex flex-col w-full gap-4 px-4 shadow-lg p-8 bg-white rounded-lg items-center">
              <h5
                className="font-DMSans text-2xl font-bold text-gray-800 mb-2 antialiased leading-tight tracking-wide mt-8 truncate"
                id="otp-heading"
              >
                Verify Your Email
              </h5>
              <h4
                className="text-sm text-gray-800/70 mb-8 font-bold antialiased leading-tight text-center"
                id="otp-instructions"
              >
                Enter the 4 digit verification code sent to {email}
              </h4>

              <InputOTP
                maxLength={4}
                value={otp}
                onChange={handleOtpChange}
                aria-labelledby="otp-heading otp-instructions"
              >
                <InputOTPGroup>
                  <InputOTPSlot
                    index={0}
                    ref={inputRef}
                    aria-label="OTP digit 1 of 4"
                  />
                  <InputOTPSlot index={1} aria-label="OTP digit 2 of 4" />
                  <InputOTPSlot index={2} aria-label="OTP digit 3 of 4" />
                  <InputOTPSlot index={3} aria-label="OTP digit 4 of 4" />
                </InputOTPGroup>
              </InputOTP>

              {error && (
                <div className="text-red-500 text-sm mt-2 text-center w-full">
                  {error}
                </div>
              )}

              <div className="mt-4">
                <h6 className="text-sm">
                  Didn&apos;t receive the code?{" "}
                  {countdown > 0 ? (
                    <span className="text-gray-500">
                      Resend in {countdown}s
                    </span>
                  ) : (
                    <button
                      className="text-primary underline focus:outline-none"
                      onClick={handleResendCode}
                      disabled={resendDisabled}
                    >
                      Resend Code
                    </button>
                  )}
                </h6>
              </div>

              <Button
                variant="primary"
                size="fm"
                className="w-full mt-6"
                onClick={handleVerifyClick}
                disabled={otp.length !== 4 || isVerifying}
              >
                {isVerifying ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Verifying...
                  </>
                ) : (
                  "Verify & Continue"
                )}
              </Button>

              <div className="mt-4 text-center">
                <button
                  className="text-sm text-[#0D0D0DB2] hover:underline"
                  onClick={() => navigate("/patient/forgot-password/email")}
                >
                  Back to Email Entry
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Verify;
