import React, { useState, useMemo } from "react";
import { ReactComponent as <PERSON><PERSON>obil<PERSON><PERSON> } from "../../../../../assets/svgs/DocMobilLogoWithText.svg";
import { HiMail } from "react-icons/hi";
import { <PERSON><PERSON> } from "../../../../../components/ui/button";
import { Input } from "../../../../../components/ui/input";
import { useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormField,
  FormControl,
  FormItem,
  FormMessage,
  FormLabel,
} from "../../../../../components/ui/form";
import { requestForgotPatientPassword_api } from "../../../../../api/api_calls/patient_apiCalls";
import { toast } from "../../../../../hooks/use-toast";
import PatientAuthImg from "../../../../../assets/images/PateintAithImg.png";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "../../../../../components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { Loader2 } from "lucide-react";

const emailSchema = z.object({
  email: z
    .string()
    .email("Please enter a valid email address")
    .nonempty("Email is required"),
});

const Email = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();

  const plugin = useMemo(
    () => Autoplay({ delay: 2000, stopOnInteraction: true }),
    [],
  );

  const form = useForm({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: "",
    },
    mode: "onChange", // Validate on change for better UX
  });

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      // eslint-disable-next-line
      const res = await requestForgotPatientPassword_api({
        email: data.email,
      });
      toast({
        description: "Recovery email sent successfully",
        variant: "success",
      });
      navigate("/patient/forgot-password/verify", {
        state: { email: data.email },
      });
    } catch (error) {
      toast({
        description: error.message || "Failed to send recovery email",
        variant: "destructive",
      });
      console.log("ERROR IN FORGOT PASSWORD  => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center flex-col w-full h-screen overflow-hidden">
      <div className="flex items-center justify-center w-full h-full">
        <div className="relative w-3/6 h-screen hidden lg:block">
          <div className="absolute inset-0 bg-[#00000066] z-10"></div>
          <img
            alt="Patient Auth Img"
            src={PatientAuthImg || "/placeholder.svg"}
            className="w-full h-full object-cover absolute inset-0"
          />
          <div className="absolute inset-0 z-20 flex flex-col items-center justify-center">
            <Carousel className="w-full" plugins={[plugin]}>
              <CarouselContent className="w-full h-full">
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Empowering Health with Trusted Care and Expert Insights
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Personalized Health Services Tailored to You
                    </h1>
                  </div>
                </CarouselItem>
                <CarouselItem className="w-full">
                  <div className="flex items-center justify-center w-full h-full">
                    <h1 className="font-bold text-[40px] text-white text-center">
                      Comprehensive Care at Your Fingertips
                    </h1>
                  </div>
                </CarouselItem>
              </CarouselContent>
            </Carousel>
          </div>
        </div>

        <div className="flex items-center justify-center sm:w-full lg:w-3/6 h-full">
          <div className="w-96">
            <div className="flex w-full items-center justify-center flex-col">
              <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
            </div>

            <div className="flex flex-col w-full gap-4 px-4 shadow-lg p-8 bg-white rounded-lg">
              <div className="flex w-full items-center justify-center flex-col">
                <h5 className="font-DMSans text-2xl font-bold text-gray-800 mb-2 antialiased leading-tight tracking-wide">
                  Forgot Password?
                </h5>
                <h4 className="text-sm text-gray-800/70 mb-4 font-bold antialiased leading-tight text-center">
                  We will send you an email to reset your password
                </h4>
              </div>

              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="flex flex-col w-full gap-4"
                >
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field, fieldState }) => (
                      <FormItem>
                        <FormLabel htmlFor="email" className="text-gray-800">
                          Email
                        </FormLabel>
                        <FormControl>
                          <div className="relative w-full">
                            <HiMail
                              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                              size={18}
                              aria-hidden="true"
                            />
                            <Input
                              id="email"
                              {...field}
                              type="email"
                              placeholder="Enter your email"
                              className={`pl-10 transition-all ${
                                fieldState.error
                                  ? "border-red-500 focus:ring-red-500/50"
                                  : "focus:ring-primary/50 focus:border-primary"
                              } focus:ring-2`}
                              aria-describedby={
                                fieldState.error ? "email-error" : undefined
                              }
                            />
                          </div>
                        </FormControl>
                        <FormMessage id="email-error" />
                      </FormItem>
                    )}
                  />

                  <Button
                    variant="primary"
                    size="fm"
                    className="w-full mt-4"
                    type="submit"
                    disabled={isSubmitting || !form.formState.isValid}
                  >
                    {isSubmitting ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                        Sending...
                      </>
                    ) : (
                      "Send Recovery Email"
                    )}
                  </Button>
                </form>
              </Form>

              <div className="mt-6 text-center">
                <h4 className="text-sm text-[#0D0D0DB2]">
                  Remember your password?{" "}
                  <span
                    className="text-[#0052FD] font-[600] cursor-pointer"
                    onClick={() => navigate("/patient/login")}
                  >
                    Back to Login
                  </span>
                </h4>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Email;
