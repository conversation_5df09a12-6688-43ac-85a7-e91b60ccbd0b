import React, { useState, useEffect, useRef } from "react";
import { ReactComponent as <PERSON><PERSON>obil<PERSON><PERSON> } from "../../../../../assets/svgs/DocMobilLogoWithText.svg";
import { HiMail } from "react-icons/hi";
import { But<PERSON> } from "../../../../../components/ui/button";
import { Input } from "../../../../../components/ui/input";
import { useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormField,
  FormControl,
  FormItem,
  FormMessage,
  FormLabel,
} from "../../../../../components/ui/form";
import { requestForgotDoctorPassword_api } from "../../../../../api/api_calls/doctor_apiCalls";
import { toast } from "../../../../../hooks/use-toast";
import { Loader2 } from "lucide-react";
import { FaAngleLeft } from "react-icons/fa";

const emailSchema = z.object({
  email: z
    .string()
    .email("Please enter a valid email address")
    .nonempty("Email is required"),
});

const Email = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const emailInputRef = useRef(null);

  const form = useForm({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: "",
    },
    mode: "onChange", // Validate on change for better UX
  });

  // Auto-focus email input on mount
  useEffect(() => {
    if (emailInputRef.current) {
      setTimeout(() => {
        emailInputRef.current.focus();
      }, 100);
    }
  }, []);

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      // eslint-disable-next-line
      const res = await requestForgotDoctorPassword_api({
        email: data.email,
      });
      toast({
        description: "Recovery email sent successfully",
        variant: "success",
      });
      navigate("/doctor/forgot-password/verify", {
        state: { email: data.email },
      });
    } catch (error) {
      toast({
        description: error.message || "Failed to send recovery email",
        variant: "destructive",
      });
      console.log("ERROR IN FORGOT PASSWORD  => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen flex-col relative">
      {/* Back button positioned in the top left corner */}
      <div className="absolute top-10 left-20">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate("/doctor/login")}
          className="flex items-center justify-center"
        >
          <FaAngleLeft />
        </Button>
      </div>

      <div className="bg-white rounded-2xl w-96">
        <div className="flex w-full items-center justify-center flex-col">
          <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
        </div>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col w-full gap-4 px-4 shadow-lg p-8 bg-white rounded-lg"
          >
            <div className="text-center px-4">
              <h5 className="font-DMSans text-2xl font-bold text-gray-800 mb-2 antialiased leading-tight tracking-wide">
                Forgot Password?
              </h5>
              <h4 className="text-sm text-gray-800/70 font-bold antialiased leading-tight">
                We will send you an email to reset your password
              </h4>
            </div>
            <FormField
              control={form.control}
              name="email"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="email" className="text-gray-800">
                    Email
                  </FormLabel>
                  <FormControl>
                    <div className="relative w-full">
                      <HiMail
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                        size={18}
                        aria-hidden="true"
                      />
                      <Input
                        id="email"
                        {...field}
                        ref={emailInputRef}
                        type="email"
                        placeholder="Enter your email"
                        className={`pl-10 transition-all ${
                          fieldState.error
                            ? "border-destructive focus:ring-destructive/50"
                            : "focus:ring-primary/50 focus:border-primary"
                        } focus:ring-2`}
                        aria-describedby={
                          fieldState.error ? "email-error" : undefined
                        }
                      />
                    </div>
                  </FormControl>
                  <FormMessage id="email-error">
                    {fieldState.error?.message}
                  </FormMessage>
                </FormItem>
              )}
            />

            <Button
              variant="primary"
              size="fm"
              className="w-full mt-4"
              type="submit"
              disabled={isSubmitting || !form.formState.isValid}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Sending...
                </>
              ) : (
                "Send Recovery Email"
              )}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default Email;
