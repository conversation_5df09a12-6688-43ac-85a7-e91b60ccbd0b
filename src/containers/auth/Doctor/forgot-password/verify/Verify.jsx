import React, { useEffect, useRef, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";

import { ReactComponent as DocMobilLogo } from "../../../../../assets/svgs/DocMobilLogoWithText.svg";
import { Button } from "../../../../../components/ui/button";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "../../../../../components/ui/input-otp";
import {
  verifyForgotDoctorPasswordOtp_api,
  requestForgotDoctorPassword_api,
} from "../../../../../api/api_calls/doctor_apiCalls";
import { toast } from "../../../../../hooks/use-toast";
import { Loader2 } from "lucide-react";
import { FaAngleLeft } from "react-icons/fa";

const Verify = () => {
  const [otp, setOtp] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [error, setError] = useState("");

  const location = useLocation();
  const { email } = location.state || {};
  const navigate = useNavigate();
  const inputRef = useRef(null);

  useEffect(() => {
    if (inputRef.current) {
      setTimeout(() => {
        inputRef.current.focus();
      }, 100);
    }
  }, []);

  useEffect(() => {
    if (!email) {
      toast({
        description: "Email information is missing. Please try again.",
        variant: "destructive",
      });
      navigate("/doctor/forgot-password/email");
    }
  }, [email, navigate]);

  const handleOtpChange = (value) => {
    setOtp(value);
    setError("");
  };

  const handleVerifyClick = async () => {
    if (otp.length === 4) {
      setIsVerifying(true);
      setError("");
      try {
        const res = await verifyForgotDoctorPasswordOtp_api({
          email: email,
          otp: otp,
        });
        toast({
          description: "Verification successful",
          variant: "success",
        });
        navigate("/doctor/forgot-password/create-password", {
          state: { email: email, resetPasswordToken: res.data },
        });
      } catch (error) {
        console.error("OTP verification failed:", error);
        setError("Invalid verification code. Please try again.");
        toast({
          description: error.message || "Invalid verification code",
          variant: "destructive",
        });
      } finally {
        setIsVerifying(false);
      }
    }
  };

  const handleResendCode = async () => {
    setResendDisabled(true);
    try {
      await requestForgotDoctorPassword_api({
        email: email,
      });

      toast({
        description: "Verification code resent to your email",
        variant: "success",
      });

      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            setResendDisabled(false);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } catch (error) {
      console.error("Failed to resend code:", error);
      setResendDisabled(false);
      toast({
        description: error.message || "Failed to resend verification code",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen flex-col relative">
      <div className="absolute top-10 left-20">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate("/doctor/forgot-password/email")}
          className="flex items-center justify-center"
        >
          <FaAngleLeft />
        </Button>
      </div>
      <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
      <div className="bg-white rounded-2xl w-96 shadow-lg p-8">
        <div className="flex w-full items-center justify-center flex-col">
          <h5
            className="font-DMSans text-2xl font-bold text-gray-800 mb-2 antialiased leading-tight tracking-wide mt-4"
            id="otp-heading"
          >
            Enter Verification Code
          </h5>
          <h4
            className="text-sm text-gray-800/70 mb-8 font-bold antialiased leading-tight text-center"
            id="otp-instructions"
          >
            Check your email and enter the code
          </h4>
        </div>

        <div className="flex flex-col items-center justify-center gap-4">
          <InputOTP
            maxLength={4}
            value={otp}
            onChange={handleOtpChange}
            aria-labelledby="otp-heading otp-instructions"
          >
            <InputOTPGroup>
              <InputOTPSlot
                index={0}
                ref={inputRef}
                aria-label="OTP digit 1 of 4"
              />
              <InputOTPSlot index={1} aria-label="OTP digit 2 of 4" />
              <InputOTPSlot index={2} aria-label="OTP digit 3 of 4" />
              <InputOTPSlot index={3} aria-label="OTP digit 4 of 4" />
            </InputOTPGroup>
          </InputOTP>

          {error && (
            <div className="text-red-500 text-sm mt-2 text-center w-full">
              {error}
            </div>
          )}

          <div className="mt-2">
            <h6 className="text-sm">
              Didn&apos;t receive the code?{" "}
              {countdown > 0 ? (
                <span className="text-gray-500">Resend in {countdown}s</span>
              ) : (
                <button
                  className="text-primary underline focus:outline-none"
                  onClick={handleResendCode}
                  disabled={resendDisabled}
                >
                  Resend Code
                </button>
              )}
            </h6>
          </div>

          <Button
            variant="primary"
            size="fm"
            className="w-full mt-6"
            onClick={handleVerifyClick}
            disabled={otp.length !== 4 || isVerifying}
          >
            {isVerifying ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Verifying...
              </>
            ) : (
              "Verify & Continue"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Verify;
