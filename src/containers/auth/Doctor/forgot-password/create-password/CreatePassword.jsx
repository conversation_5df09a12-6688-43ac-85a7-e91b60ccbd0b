import React, { useState, useEffect, useRef } from "react";
import { ReactComponent as <PERSON><PERSON>obil<PERSON><PERSON> } from "../../../../../assets/svgs/DocMobilLogoWithText.svg";
import { <PERSON>a<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "react-icons/fa";
import { <PERSON><PERSON>ini<PERSON>ye, HiMiniEyeSlash } from "react-icons/hi2";
import { Button } from "../../../../../components/ui/button";
import { Input } from "../../../../../components/ui/input";
import { useNavigate, useLocation } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../../../components/ui/form";
import { resetDoctorPassword_api } from "../../../../../api/api_calls/doctor_apiCalls";
import { toast } from "../../../../../hooks/use-toast";
import { Loader2 } from "lucide-react";

const passwordSchema = z
  .object({
    newPassword: z
      .string()
      .min(6, "Password must be at least 6 characters long"),
    // .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    // .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    // .regex(/[0-9]/, "Password must contain at least one number"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

const CreatePassword = () => {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { email, resetPasswordToken } = location.state || {};
  const passwordInputRef = useRef(null);

  // Auto-focus first input on mount
  useEffect(() => {
    if (passwordInputRef.current) {
      setTimeout(() => {
        passwordInputRef.current.focus();
      }, 100);
    }
  }, []);

  // Redirect if email or token is missing
  useEffect(() => {
    if (!email || !resetPasswordToken) {
      toast({
        description:
          "Missing information. Please restart the password reset process.",
        variant: "destructive",
      });
      navigate("/doctor/forgot-password/email");
    }
  }, [email, resetPasswordToken, navigate]);

  const form = useForm({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      newPassword: "",
      confirmPassword: "",
    },
    mode: "onChange", // Validate on change for better UX
  });

  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      await resetDoctorPassword_api({
        email: email,
        newPassword: data.confirmPassword,
        resetPasswordToken: resetPasswordToken,
      });

      toast({
        description:
          "Password reset successful. You can now login with your new password.",
        variant: "success",
      });

      navigate("/doctor/login");
    } catch (error) {
      toast({
        description:
          error.message || "Failed to reset password. Please try again.",
        variant: "destructive",
      });
      console.log("ERROR IN RESET PASSWORD => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen flex-col relative">
      <div className="absolute top-10 left-20">
        <Button
          variant="outline"
          size="icon"
          onClick={() => navigate("/doctor/login")}
          className="flex items-center justify-center"
        >
          <FaAngleLeft />
        </Button>
      </div>
      <div className="bg-white rounded-2xl w-96">
        <div className="flex w-full items-center justify-center flex-col">
          <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col w-full gap-4 px-4 shadow-lg p-8 bg-white rounded-lg"
          >
            <div className="flex w-full items-center justify-center flex-col px-4">
              <h5 className="font-DMSans text-2xl font-bold text-gray-800 mb-2 antialiased leading-tight tracking-wide">
                Create New Password
              </h5>
              <h4 className="text-sm text-gray-800/70 mb-6 font-bold antialiased leading-tight text-center">
                Please enter a new password for your account
              </h4>
            </div>
            {/* New Password Field */}
            <FormField
              control={form.control}
              name="newPassword"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="newPassword" className="text-gray-800">
                    New Password
                  </FormLabel>
                  <FormControl>
                    <div className="relative w-full">
                      <FaKey
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                        size={18}
                        aria-hidden="true"
                      />
                      <Input
                        id="newPassword"
                        type={passwordVisible ? "text" : "password"}
                        placeholder="Enter new password"
                        className={`pl-10 pr-10 transition-all ${
                          fieldState.error
                            ? "border-destructive focus:ring-destructive/50"
                            : "focus:ring-primary/50 focus:border-primary"
                        } focus:ring-2`}
                        {...field}
                        ref={passwordInputRef}
                        aria-describedby={
                          fieldState.error ? "newPassword-error" : undefined
                        }
                      />
                      <button
                        type="button"
                        onClick={() => setPasswordVisible(!passwordVisible)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-primary"
                        aria-label={
                          passwordVisible ? "Hide password" : "Show password"
                        }
                      >
                        {passwordVisible ? (
                          <HiMiniEyeSlash size={18} />
                        ) : (
                          <HiMiniEye size={18} />
                        )}
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage id="newPassword-error" />
                </FormItem>
              )}
            />

            {/* Confirm Password Field */}
            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel
                    htmlFor="confirmPassword"
                    className="text-gray-800"
                  >
                    Confirm Password
                  </FormLabel>
                  <FormControl>
                    <div className="relative w-full">
                      <FaKey
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                        size={18}
                        aria-hidden="true"
                      />
                      <Input
                        id="confirmPassword"
                        type={confirmPasswordVisible ? "text" : "password"}
                        placeholder="Confirm Password"
                        className={`pl-10 pr-10 transition-all ${
                          fieldState.error
                            ? "border-destructive focus:ring-destructive/50"
                            : "focus:ring-primary/50 focus:border-primary"
                        } focus:ring-2`}
                        {...field}
                        aria-describedby={
                          fieldState.error ? "confirmPassword-error" : undefined
                        }
                      />
                      <button
                        type="button"
                        onClick={() =>
                          setConfirmPasswordVisible(!confirmPasswordVisible)
                        }
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-primary"
                        aria-label={
                          confirmPasswordVisible
                            ? "Hide password"
                            : "Show password"
                        }
                      >
                        {confirmPasswordVisible ? (
                          <HiMiniEyeSlash size={18} />
                        ) : (
                          <HiMiniEye size={18} />
                        )}
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage id="confirmPassword-error" />
                </FormItem>
              )}
            />

            {/* Submit Button */}
            <Button
              type="submit"
              variant="primary"
              className="w-full mt-4"
              disabled={isSubmitting || !form.formState.isValid}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Resetting Password...
                </>
              ) : (
                "Reset Password"
              )}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default CreatePassword;
