import React, { useState, useEffect, useRef } from "react";
import { ReactComponent as <PERSON><PERSON>obil<PERSON><PERSON> } from "../../../../assets/svgs/DocMobilLogoWithText.svg";
import { HiMail } from "react-icons/hi";
import { <PERSON>a<PERSON><PERSON> } from "react-icons/fa";
import { HiMiniEye, HiMiniEyeSlash } from "react-icons/hi2";
import { Button } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { Checkbox } from "../../../../components/ui/checkbox";
import { ReactComponent as GoogleIcon } from "../../../../assets/svgs/GoogleIcon.svg";
import { useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../../components/ui/form";
import { loginDoctor_api } from "../../../../api/api_calls/auth_apiCalls";
import { toast } from "../../../../hooks/use-toast";
import { useDispatch } from "react-redux";
import { setUser } from "../../../../redux/slices/userSlice";
import { Loader2 } from "lucide-react";

const formSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  rememberMe: z.boolean().optional().default(false),
});

const Login = () => {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const emailInputRef = useRef(null);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
    mode: "onChange",
  });

  // Auto-focus email input on mount
  useEffect(() => {
    if (emailInputRef.current) {
      setTimeout(() => {
        emailInputRef.current.focus();
      }, 100);
    }
  }, []);

  const onSubmit = async (data) => {
    console.log("Form Data:", data);
    setIsSubmitting(true);
    try {
      const res = await loginDoctor_api({
        email: data.email,
        password: data.password,
      });
      dispatch(setUser(res.data.user));
      localStorage.setItem("accessToken", res.data.token);

      toast({
        description: "Login successful",
        variant: "success",
      });
    } catch (error) {
      toast({
        description: error.message || "Login failed. Please try again.",
        variant: "destructive",
      });
      console.log("ERROR IN LOGIN  => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleForgotPasswordClick = () => {
    navigate("/doctor/forgot-password/email");
  };

  return (
    <div className="flex items-center justify-center min-h-screen py-8">
      <div className="bg-white rounded-2xl w-96">
        <div className="flex w-full items-center justify-center flex-col">
          <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
        </div>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col w-full gap-4 px-4 shadow-lg p-8 bg-white rounded-lg"
          >
            <FormField
              control={form.control}
              name="email"
              render={({ field, fieldState }) => (
                <FormItem>
                  <div className="flex w-full items-center justify-center flex-col">
                    <h5 className="font-DMSans text-3xl font-bold text-gray-800 mb-2 antialiased leading-tight tracking-wide">
                      Welcome Back
                    </h5>
                    <h4 className="text-sm text-gray-800/70 mb-8 font-bold antialiased leading-tight">
                      Enter your credentials to log in to your account
                    </h4>
                  </div>
                  <FormLabel htmlFor="email" className="text-gray-800">
                    Email
                  </FormLabel>
                  <FormControl>
                    <div className="relative w-full">
                      <HiMail
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                        size={18}
                        aria-hidden="true"
                      />
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter your email"
                        className={`pl-10 transition-all ${
                          fieldState.error
                            ? "border-destructive focus:ring-destructive/50"
                            : "focus:ring-primary/50 focus:border-primary"
                        } focus:ring-2`}
                        {...field}
                        ref={emailInputRef}
                        aria-describedby={
                          fieldState.error ? "email-error" : undefined
                        }
                      />
                    </div>
                  </FormControl>
                  <FormMessage id="email-error" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="password" className="text-gray-800">
                    Password
                  </FormLabel>
                  <FormControl>
                    <div className="relative w-full">
                      <FaKey
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                        size={18}
                        aria-hidden="true"
                      />
                      <Input
                        id="password"
                        type={passwordVisible ? "text" : "password"}
                        placeholder="Enter your password"
                        className={`pl-10 pr-10 transition-all ${
                          fieldState.error
                            ? "border-destructive focus:ring-destructive/50"
                            : "focus:ring-primary/50 focus:border-primary"
                        } focus:ring-2`}
                        {...field}
                        aria-describedby={
                          fieldState.error ? "password-error" : undefined
                        }
                      />
                      <button
                        type="button"
                        onClick={() => setPasswordVisible(!passwordVisible)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-primary"
                        aria-label={
                          passwordVisible ? "Hide password" : "Show password"
                        }
                      >
                        {passwordVisible ? (
                          <HiMiniEyeSlash size={18} />
                        ) : (
                          <HiMiniEye size={18} />
                        )}
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage id="password-error" />
                </FormItem>
              )}
            />

            <div className="flex items-center justify-between">
              <FormField
                control={form.control}
                name="rememberMe"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-2 space-y-0">
                    <FormControl>
                      <Checkbox
                        id="rememberMe"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel
                        htmlFor="rememberMe"
                        className="text-sm font-medium cursor-pointer"
                      >
                        Remember me
                      </FormLabel>
                    </div>
                  </FormItem>
                )}
              />

              <div
                className="text-sm text-[#1E1E1EB2] font-bold cursor-pointer"
                onClick={handleForgotPasswordClick}
              >
                Forgot Password?
              </div>
            </div>

            <Button
              variant="primary"
              size="fm"
              className="w-full mt-4"
              type="submit"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Logging in...
                </>
              ) : (
                "Log In"
              )}
            </Button>

            <div className="flex items-center justify-center w-full mt-2">
              <div className="flex-grow h-px bg-[#********]"></div>
              <h3 className="mx-4 text-[#********] font-bold">OR</h3>
              <div className="flex-grow h-px bg-[#********]"></div>
            </div>

            <Button
              variant="social"
              size="fm"
              className="w-full flex items-center justify-center gap-2"
              type="button"
            >
              <GoogleIcon className="w-5 h-5" />
              Login with Google
            </Button>
          </form>
        </Form>

        <div className="mt-6 text-center">
          <h4 className="text-sm text-[#0D0D0DB2]">
            Don't have an account?{" "}
            <span
              className="text-[#0052FD] font-[600] cursor-pointer underline"
              onClick={() => navigate("/doctor/signup")}
            >
              Sign up
            </span>
          </h4>
        </div>
      </div>
    </div>
  );
};

export default Login;
