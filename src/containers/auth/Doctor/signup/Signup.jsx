import React, { useState, useEffect, useRef } from "react";
import { ReactComponent as <PERSON><PERSON>obil<PERSON>ogo } from "../../../../assets/svgs/DocMobilLogoWithText.svg";
import { HiMail } from "react-icons/hi";
import { <PERSON>a<PERSON><PERSON> } from "react-icons/fa";
import { Hi<PERSON>iniEye, HiMiniEyeSlash } from "react-icons/hi2";
import { Button } from "../../../../components/ui/button";
import { Input } from "../../../../components/ui/input";
import { FaUserCircle } from "react-icons/fa";
import { Checkbox } from "../../../../components/ui/checkbox";
import { useNavigate } from "react-router-dom";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../../../components/ui/form";
import { useToast } from "../../../../hooks/use-toast";
import { createDoctor_api } from "../../../../api/api_calls/doctor_apiCalls";
import { Loader2 } from "lucide-react";
import { ReactComponent as GoogleIcon } from "../../../../assets/svgs/GoogleIcon.svg";

const formSchema = z
  .object({
    firstName: z
      .string()
      .min(1, "First name is required")
      .max(12, "First name must be 12 characters or less")
      .refine((value) => /^[a-zA-Z\s]+$/.test(value), {
        message: "First name should only contain letters",
      }),
    lastName: z
      .string()
      .min(1, "Last name is required")
      .max(12, "Last name must be 12 characters or less")
      .refine((value) => /^[a-zA-Z\s]+$/.test(value), {
        message: "Last name should only contain letters",
      }),
    email: z
      .string()
      .min(1, "Email is required")
      .email("Invalid email address")
      .regex(
        /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        "Must be a valid email address",
      ),
    password: z
      .string()
      .min(6, { message: "Password must be at least 6 characters." }),
    // .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    // .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    // .regex(/[0-9]/, "Password must contain at least one number"),
    confirmPassword: z.string(),
    terms: z.literal(true, {
      errorMap: () => ({
        message: "You must accept the terms and conditions.",
      }),
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match.",
    path: ["confirmPassword"],
  });

const Signup = () => {
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [confirmPasswordVisible, setConfirmPasswordVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const firstNameInputRef = useRef(null);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
      terms: false,
    },
    mode: "onChange",
    reValidateMode: "onChange",
  });

  // Auto-focus first input on mount
  useEffect(() => {
    if (firstNameInputRef.current) {
      setTimeout(() => {
        firstNameInputRef.current.focus();
      }, 100);
    }
  }, []);

  const onSubmit = async (values) => {
    setIsSubmitting(true);
    try {
      // eslint-disable-next-line
      const res = await createDoctor_api({
        email: values.email,
        password: values.password,
        firstName: values.firstName,
        lastName: values.lastName,
      });

      toast({
        description: "Account created successfully!",
        variant: "success",
      });

      navigate("/doctor/information");
    } catch (error) {
      toast({
        description:
          error.message || "Failed to create account. Please try again.",
        variant: "destructive",
      });
      console.log("ERROR IN SIGNUP  => ", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen py-8">
      <div className="w-96">
        <div className="flex w-full items-center justify-center flex-col ">
          <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col w-full gap-4 px-4 shadow-xl p-8 bg-white rounded-lg"
          >
            <h5 className="font-DMSans text-2xl font-bold text-gray-800 antialiased leading-tight tracking-wide truncate text-center">
              Create an Account
            </h5>
            <h4 className="text-sm text-gray-800/70 font-bold antialiased leading-tight text-center">
              Let&apos;s get started!
            </h4>
            {/* First Name */}
            <FormField
              control={form.control}
              name="firstName"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="firstName">First Name</FormLabel>
                  <FormControl>
                    <div className="relative w-full">
                      <FaUserCircle
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                        size={18}
                        aria-hidden="true"
                      />
                      <Input
                        id="firstName"
                        {...field}
                        ref={firstNameInputRef}
                        placeholder="Enter your first name"
                        className={`pl-10 transition-all ${
                          fieldState.error
                            ? "border-destructive focus:ring-destructive/50"
                            : "focus:ring-primary/50 focus:border-primary"
                        } focus:ring-2`}
                        aria-describedby={
                          fieldState.error ? "firstName-error" : undefined
                        }
                      />
                    </div>
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage
                      id="firstName-error"
                      className="animate-in fade-in-0 slide-in-from-top-1 duration-200"
                    >
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />

            {/* Last Name */}
            <FormField
              control={form.control}
              name="lastName"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="lastName">Last Name</FormLabel>
                  <FormControl>
                    <div className="relative w-full">
                      <FaUserCircle
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                        size={18}
                        aria-hidden="true"
                      />
                      <Input
                        id="lastName"
                        {...field}
                        placeholder="Enter your last name"
                        className={`pl-10 transition-all ${
                          fieldState.error
                            ? "border-destructive focus:ring-destructive/50"
                            : "focus:ring-primary/50 focus:border-primary"
                        } focus:ring-2`}
                        aria-describedby={
                          fieldState.error ? "lastName-error" : undefined
                        }
                      />
                    </div>
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage
                      id="lastName-error"
                      className="animate-in fade-in-0 slide-in-from-top-1 duration-200"
                    >
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />

            {/* Email */}
            <FormField
              control={form.control}
              name="email"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="email">Email</FormLabel>
                  <FormControl>
                    <div className="relative w-full">
                      <HiMail
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                        size={18}
                        aria-hidden="true"
                      />
                      <Input
                        id="email"
                        type="email"
                        {...field}
                        placeholder="Enter your email"
                        className={`pl-10 transition-all ${
                          fieldState.error
                            ? "border-destructive focus:ring-destructive/50"
                            : "focus:ring-primary/50 focus:border-primary"
                        } focus:ring-2`}
                        aria-describedby={
                          fieldState.error ? "email-error" : undefined
                        }
                      />
                    </div>
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage
                      id="email-error"
                      className="animate-in fade-in-0 slide-in-from-top-1 duration-200"
                    >
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="password">Password</FormLabel>
                  <FormControl>
                    <div className="relative w-full">
                      <FaKey
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                        size={18}
                        aria-hidden="true"
                      />
                      <Input
                        id="password"
                        {...field}
                        type={passwordVisible ? "text" : "password"}
                        placeholder="Enter your password"
                        className={`pl-10 pr-10 transition-all ${
                          fieldState.error
                            ? "border-destructive focus:ring-destructive/50"
                            : "focus:ring-primary/50 focus:border-primary"
                        } focus:ring-2 bg-transparent hide-browser-eye-icon`} // Added hide-browser-eye-icon class
                        aria-describedby={
                          fieldState.error ? "password-error" : undefined
                        }
                      />
                      <button
                        type="button"
                        onClick={() => setPasswordVisible(!passwordVisible)}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-primary z-10"
                        aria-label={
                          passwordVisible ? "Hide password" : "Show password"
                        }
                      >
                        {passwordVisible ? (
                          <HiMiniEyeSlash size={18} className="fill-current" />
                        ) : (
                          <HiMiniEye size={18} className="fill-current" />
                        )}
                      </button>
                    </div>
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage
                      id="password-error"
                      className="animate-in fade-in-0 slide-in-from-top-1 duration-200"
                    >
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="confirmPassword"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormLabel htmlFor="confirmPassword">
                    Confirm Password
                  </FormLabel>
                  <FormControl>
                    <div className="relative w-full">
                      <FaKey
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-primary"
                        size={18}
                        aria-hidden="true"
                      />
                      <Input
                        id="confirmPassword"
                        {...field}
                        type={confirmPasswordVisible ? "text" : "password"}
                        placeholder="Confirm Password"
                        className={`pl-10 pr-10 transition-all ${
                          fieldState.error
                            ? "border-destructive focus:ring-destructive/50"
                            : "focus:ring-primary/50 focus:border-primary"
                        } focus:ring-2 bg-transparent hide-browser-eye-icon`} // Added hide-browser-eye-icon class
                        aria-describedby={
                          fieldState.error ? "confirmPassword-error" : undefined
                        }
                      />
                      <button
                        type="button"
                        onClick={() =>
                          setConfirmPasswordVisible(!confirmPasswordVisible)
                        }
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer text-primary z-10"
                        aria-label={
                          confirmPasswordVisible
                            ? "Hide password"
                            : "Show password"
                        }
                      >
                        {confirmPasswordVisible ? (
                          <HiMiniEyeSlash size={18} className="fill-current" />
                        ) : (
                          <HiMiniEye size={18} className="fill-current" />
                        )}
                      </button>
                    </div>
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage
                      id="confirmPassword-error"
                      className="animate-in fade-in-0 slide-in-from-top-1 duration-200"
                    >
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="terms"
              render={({ field, fieldState }) => (
                <FormItem>
                  <FormControl>
                    <div className="items-top flex space-x-2">
                      <Checkbox
                        id="terms1"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        aria-describedby={
                          fieldState.error ? "terms-error" : undefined
                        }
                      />
                      <div className="grid gap-1.5 leading-none">
                        <label
                          htmlFor="terms1"
                          className="text-sm font-medium text-[#1e1e1e] leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          I have read and accept{" "}
                          <span
                            className="text-sm text-muted-foreground text-primary underline cursor-pointer"
                            onClick={() =>
                              window.open(
                                "/doctor-disclosure-agreement",
                                "_blank",
                              )
                            }
                          >
                            Doctor Disclosure Agreement
                          </span>
                        </label>
                      </div>
                    </div>
                  </FormControl>
                  {fieldState.error && (
                    <FormMessage
                      id="terms-error"
                      className="animate-in fade-in-0 slide-in-from-top-1 duration-200"
                    >
                      {fieldState.error.message}
                    </FormMessage>
                  )}
                </FormItem>
              )}
            />

            <Button
              variant="primary"
              size="fm"
              className="w-full mt-4 gap-2"
              type="submit"
              disabled={!form.formState.isValid || isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Sign Up"
              )}
            </Button>

            <div className="flex items-center justify-center w-full mt-2">
              <div className="flex-grow h-px bg-gray-300"></div>
              <h3 className="mx-4 text-gray-500 font-bold">OR</h3>
              <div className="flex-grow h-px bg-gray-300"></div>
            </div>

            <Button
              variant="social"
              size="fm"
              className="w-full flex items-center justify-center gap-2"
              type="button"
            >
              <GoogleIcon className="w-5 h-5" />
              Sign up with Google
            </Button>

            <div className="text-center">
              <h4 className="text-sm text-[#0D0D0DB2]">
                Already have an account?{" "}
                <span
                  className="text-[#0052FD] font-[600] cursor-pointer underline"
                  onClick={() => navigate("/doctor/login")}
                >
                  Log In
                </span>
              </h4>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default Signup;
