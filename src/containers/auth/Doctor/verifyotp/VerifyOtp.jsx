import React, { useState } from "react";
import { ReactComponent as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "../../../../assets/svgs/DocMobilLogoWithText.svg";
import { Button } from "../../../../components/ui/button";
import { useNavigate } from "react-router-dom";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "../../../../components/ui/input-otp";

const VerifyOtp = () => {
  const [otp, setOtp] = useState("");
  const navigate = useNavigate();

  const handleOtpChange = (value) => {
    setOtp(value);
  };

  const handleLoginClick = () => {
    if (otp.length === 4) {
      console.log("OTP Value:", otp);
      navigate("/doctor/dashboard");
    } else {
      console.log("Please enter a valid 4-digit OTP.");
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen ">
      <div className="bg-white rounded-2xl w-96">
        <div className="flex w-full items-center justify-center flex-col ">
          <DocMobilLogo className="mb-6 h-[46px] w-[205px]" />
        </div>

        <div className="flex flex-col w-full gap-4 px-4 shadow-lg p-8 bg-white rounded-lg items-center">
          <h5 className="font-DMSans text-2xl font-bold text-gray-800 mb-2 antialiased leading-tight tracking-wide mt-8 truncate">
            Two Factor Authentication
          </h5>
          <h4 className="text-sm text-gray-800/70 mb-8 font-bold antialiased leading-tight">
            Enter the 4 digit authentication code below
          </h4>
          <InputOTP maxLength={4} value={otp} onChange={handleOtpChange}>
            <InputOTPGroup>
              <InputOTPSlot index={0} />
              <InputOTPSlot index={1} />
              <InputOTPSlot index={2} />
              <InputOTPSlot index={3} />
            </InputOTPGroup>
          </InputOTP>

          <div>
            <h6>
              Didn’t receive the code?{" "}
              <span className="text-primary underline">Resend Code</span>
            </h6>
          </div>

          <Button
            variant="primary"
            size="fm"
            className="w-full mt-4"
            onClick={handleLoginClick}
            disabled={otp.length !== 4}
          >
            Login
          </Button>
        </div>
      </div>
    </div>
  );
};

export default VerifyOtp;
