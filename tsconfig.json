{
  "compilerOptions": {
    "target": "ES6", // Use "es6" or "es2015" for better compatibility
    "module": "ESNext", // Use "ESNext" for modern module resolution
    "jsx": "react-jsx", // Use "react-jsx" for React 17+ JS<PERSON> transform
    "esModuleInterop": true, // Allow default imports for CommonJS modules
    "allowSyntheticDefaultImports": true, // Allow synthetic default imports
    "strict": true, // Enable all strict type-checking options
    "skipLibCheck": true, // Skip type checking of declaration files
    "forceConsistentCasingInFileNames": true, // Ensure consistent file naming
    "moduleResolution": "node", // Use Node.js-style module resolution
    "resolveJsonModule": true, // Allow importing JSON files
    "baseUrl": "./", // Base directory for module resolution
    "paths": {
      "@/*": ["src/*"] // Map "@/*" to "src/*" for absolute imports
    },
    "lib": ["dom", "dom.iterable", "esnext"] // Include necessary libraries
  },
  "include": ["src"] // Include all files in the "src" directory
}
